/**
 * @file APP_Net_Regs.h
 * <AUTHOR> (liu<PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2025-08-24
 * @last modified 2025-08-24
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#ifndef APP_NET_REGS_H
#define APP_NET_REGS_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>

#define SYS_CFG_REG_NUM 55
#define SYS_STATUS_REG_NUM 50
#define SYS_CMD_REG_NUM 2

#define SYS_REG_NUM (SYS_CFG_REG_NUM + SYS_STATUS_REG_NUM + SYS_CMD_REG_NUM)

//[0,SYS_REG_NUM - 1]闭区间
#define SYS_REG_ADDR_BASE 0
#define SYS_REG_ADDR_END (SYS_REG_ADDR_BASE + SYS_REG_NUM - 1)

//[SYS_REG_ADDR_BASE,SYS_CFG_REG_ADDR_BASE + SYS_CFG_REG_NUM - 1]闭区间
#define SYS_CFG_REG_ADDR_BASE (0 + SYS_REG_ADDR_BASE)
#define SYS_CFG_REG_ADDR_END (SYS_CFG_REG_ADDR_BASE + SYS_CFG_REG_NUM - 1)

//闭区间
#define SYS_STATUS_REG_ADDR_BASE (0 + SYS_REG_ADDR_BASE + SYS_CFG_REG_NUM)
#define SYS_STATUS_REG_ADDR_END (SYS_STATUS_REG_ADDR_BASE + SYS_STATUS_REG_NUM - 1)

//闭区间
#define SYS_CMD_REG_ADDR_BASE (0 + SYS_REG_ADDR_BASE + SYS_CFG_REG_NUM + SYS_STATUS_REG_NUM)
#define SYS_CMD_REG_ADDR_END (SYS_CMD_REG_ADDR_BASE + SYS_CMD_REG_NUM - 1)

#define SYS_CMD_REG_ADDR1 (SYS_CMD_REG_ADDR_END + 1) //105
#define SYS_CMD_REG_ADDR2 (SYS_CMD_REG_ADDR_END + 2) //106

    typedef struct tagSysRegs
    {
        uint32_t reg[SYS_REG_NUM];
        uint16_t _write_cnt; //write count
        uint16_t _crc; //CRC16-Modbus Check
    } SysRegs_t;
#ifdef __cplusplus
}
#endif
#endif //! APP_NET_REGS_H
