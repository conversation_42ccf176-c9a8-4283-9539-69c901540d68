/*
 * MotorDrive_150kW_AD2S1210.h
 *
 *  Created on: 2023年5月12日
 *      Author: KUST410_2
 *      DMA过采样头文件
 */

#ifndef USER_INCLUDE_MOTORDRIVE_AD2S1210_H_
#define USER_INCLUDE_MOTORDRIVE_AD2S1210_H_

#ifdef __cplusplus
extern "C" {
#endif

//  Includes
#include "F28x_Project.h"


#ifdef _FLASH
#pragma CODE_SECTION(ReadFaultandPosFromAD2S1210, ".TI.ramfunc");


#endif

//Mode Select
#define AD2S1210_POSITION    0
#define AD2S1210_VELOCITY    1
#define AD2S1210_CONFIG      2


//Register Map
#define AD2S1210_POSITIONMSB            0x80
#define AD2S1210_POSITIONLSB            0x81
#define AD2S1210_VELOCITYMSB            0x82
#define AD2S1210_VELOCITYLSB            0x83
#define AD2S1210_LOSTHRES               0x88
#define AD2S1210_DOSORTHRES             0x89
#define AD2S1210_DOSMISTHRES            0x8A
#define AD2S1210_DOSRSTMXTHRES          0x8B
#define AD2S1210_DOSRSTMITHRES          0x8C
#define AD2S1210_LOTHITHRES             0x8D
#define AD2S1210_LOTLOTHRES             0x8E
#define AD2S1210_EXFREQUENCY            0x91
#define AD2S1210_CONTROL                0x92
#define AD2S1210_SOFTRESET              0xF0
#define AD2S1210_FAULT                  0xFF
#define AD2S1210_POS_VEL                0x00  //void register for normal read address


extern unsigned char data1, data2,data3,data4;


void AD2S1210Init(void);
void AD2S1210ConfigMode(void);
void AD2S1210SoftReset(void);
void WriteToAD2S1210(unsigned char address, unsigned char data);
unsigned char ReadFromAD2S1210(unsigned char address);
unsigned char ReadPosFromAD2S1210(unsigned char address);
void ReadFaultandPosFromAD2S1210(Uint16* Position,Uint16* Fault);

#ifdef __cplusplus
}
#endif /* extern "C" */

#endif /* USER_INCLUDE_MOTORDRIVE_350KW_DMA_H_ */
