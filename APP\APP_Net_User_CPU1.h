/**
 * @file APP_Net_User_CPU1.h
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-28
 * @last modified 2025-08-28
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#ifndef APP_NET_USER_CPU1_H
#define APP_NET_USER_CPU1_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>

#define FLOAT_TO_U32_FIXED_POINT(fp32_var) ((uint32_t)((int32_t)((fp32_var) * (100000))))
#define U32_FIXED_POINT_TO_FLOAT(u32_var) (((int32_t)(u32_var)) * 0.00001f)

#define MOTOR_CFG_REG_BASE 8
#define MOTOR_CFG_REG_ADDR(x) ((x))

// 网络配置相关寄存器宏定义（地址 0~7）
#define REG_DEV_IP              MOTOR_CFG_REG_ADDR(1)  // 本机 IP
#define REG_GW_IP               MOTOR_CFG_REG_ADDR(2)  // 网关 IP
#define REG_NETMASK             MOTOR_CFG_REG_ADDR(3)  // 子网掩码
#define REG_MAC_LOW             MOTOR_CFG_REG_ADDR(4)  // MAC 地址低 4 字节
#define REG_PC_IP               MOTOR_CFG_REG_ADDR(5)  // 目标 IP
#define REG_PC_PORT             MOTOR_CFG_REG_ADDR(6)  // 目标端口
#define REG_RESERVED            MOTOR_CFG_REG_ADDR(7)  // 保留
#define REG_Vdcset_ref MOTOR_CFG_REG_ADDR(8)
#define REG_Iset_d_ref MOTOR_CFG_REG_ADDR(9)
#define REG_TIMER0_PRD MOTOR_CFG_REG_ADDR(10)
#define REG_TIMER1_PRD MOTOR_CFG_REG_ADDR(11)
#define REG_DutyMAX MOTOR_CFG_REG_ADDR(12)
#define REG_DutyMIN MOTOR_CFG_REG_ADDR(13)
#define REG_Rottx_Zero_Current MOTOR_CFG_REG_ADDR(14)
#define REG_PID_Parameters_PID_Speed_PID_Kp MOTOR_CFG_REG_ADDR(15)
#define REG_PID_Parameters_PID_Speed_PID_Ki MOTOR_CFG_REG_ADDR(16)
#define REG_PID_Parameters_PID_Speed_PID_Kd MOTOR_CFG_REG_ADDR(17)
#define REG_PID_Parameters_PID_Speed_PID_Kd_Filter MOTOR_CFG_REG_ADDR(18)
#define REG_PID_Parameters_PID_Id_PID_Kp MOTOR_CFG_REG_ADDR(19)
#define REG_PID_Parameters_PID_Id_PID_Ki MOTOR_CFG_REG_ADDR(20)
#define REG_PID_Parameters_PID_Id_PID_Kd MOTOR_CFG_REG_ADDR(21)
#define REG_PID_Parameters_PID_Id_PID_Kd_Filter MOTOR_CFG_REG_ADDR(22)
#define REG_PID_Parameters_PID_Iq_PID_Kp MOTOR_CFG_REG_ADDR(23)
#define REG_PID_Parameters_PID_Iq_PID_Ki MOTOR_CFG_REG_ADDR(24)
#define REG_PID_Parameters_PID_Iq_PID_Kd MOTOR_CFG_REG_ADDR(25)
#define REG_PID_Parameters_PID_Iq_PID_Kd_Filter MOTOR_CFG_REG_ADDR(26)
#define REG_Motor_Limits_Motor_Id_Max MOTOR_CFG_REG_ADDR(27)
#define REG_Motor_Limits_Motor_Id_Min MOTOR_CFG_REG_ADDR(28)
#define REG_Motor_Limits_Motor_Id_Max_Lowspeed MOTOR_CFG_REG_ADDR(30)
#define REG_Motor_Limits_Motor_Id_Min_Lowspeed MOTOR_CFG_REG_ADDR(31)
#define REG_Motor_Limits_Motor_Iq_Max MOTOR_CFG_REG_ADDR(32)
#define REG_Motor_Limits_Motor_Iq_Min MOTOR_CFG_REG_ADDR(33)
#define REG_Motor_Limits_Motor_Iq_Max_Lowspeed MOTOR_CFG_REG_ADDR(34)
#define REG_Motor_Limits_Motor_Iq_Min_Lowspeed MOTOR_CFG_REG_ADDR(35)
#define REG_Motor_Limits_Motor_Speed_Max MOTOR_CFG_REG_ADDR(36)
#define REG_Motor_Limits_Motor_Speed_Min MOTOR_CFG_REG_ADDR(37)
#define REG_Motor_Limits_Reserved1 MOTOR_CFG_REG_ADDR(38)
#define REG_Motor_Limits_Reserved2 MOTOR_CFG_REG_ADDR(39)
#define REG_Motor_Parameters_Motor_Ld MOTOR_CFG_REG_ADDR(40)
#define REG_Motor_Parameters_Motor_Ld_Inv MOTOR_CFG_REG_ADDR(41)
#define REG_Motor_Parameters_Motor_Lq MOTOR_CFG_REG_ADDR(42)
#define REG_Motor_Parameters_Motor_Lq_Inv MOTOR_CFG_REG_ADDR(43)
#define REG_Motor_Parameters_Motor_Flux MOTOR_CFG_REG_ADDR(44)
#define REG_Motor_Parameters_Motor_Rs MOTOR_CFG_REG_ADDR(45)
#define REG_Motor_Parameters_Motor_Pn MOTOR_CFG_REG_ADDR(46)
#define REG_Motor_Parameters_Motor_Pn_Inv MOTOR_CFG_REG_ADDR(47)
#define REG_Motor_Parameters_Motor_Resolver_Zero MOTOR_CFG_REG_ADDR(48)
#define REG_Motor_Parameters_Motor_RpstoRpm_COEF MOTOR_CFG_REG_ADDR(49)
#define REG_mEncrypt MOTOR_CFG_REG_ADDR(50)

#ifdef __cplusplus
}
#endif
#endif //! APP_NET_USER_CPU1_H
