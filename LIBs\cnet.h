/**
 * @file cnet.h
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-27
 * @last modified 2025-08-27
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#ifndef CNET_H
#define CNET_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>
#include "ccommon.h"

/**
 * @brief 将IPv4地址转换为uint32_t
 * @param ip1 第一个字节 (0-255)
 * @param ip2 第二个字节 (0-255)
 * @param ip3 第三个字节 (0-255)
 * @param ip4 第四个字节 (0-255)
 * @return uint32_t IPv4地址的32位表示
 * @note 在16bit字节平台上，需要将8bit数据组合成正确的格式
 */
#define IPV4_TO_UINT32(ip1, ip2, ip3, ip4) \
    ((uint32_t)(((uint32_t)((ip1) & 0xFF) << 24) | ((uint32_t)((ip2) & 0xFF) << 16) | ((uint32_t)((ip3) & 0xFF) << 8) | (uint32_t)((ip4) & 0xFF)))

/**
 * @brief 将uint32_t转换为IPv4地址数组
 * @param ip uint32_t格式的IPv4地址
 * @param arr 用于存储IPv4地址的数组，需要至少4个元素
 * @note 在16bit字节平台上，每个数组元素实际占用16bit，但只使用低8bit
 */
#define UINT32_TO_IPV4_ARRAY(ip, arr)     \
    do                                    \
    {                                     \
        (arr)[0] = (((ip) >> 24) & 0xFF); \
        (arr)[1] = (((ip) >> 16) & 0xFF); \
        (arr)[2] = (((ip) >> 8) & 0xFF);  \
        (arr)[3] = ((ip) & 0xFF);         \
    } while (0)

/**
 * @brief 从IPv4地址数组创建uint32_t（数组版本）
 * @param arr IPv4地址数组，包含4个元素
 * @return uint32_t IPv4地址的32位表示
 */
#define IPV4_ARRAY_TO_UINT32(arr) \
    IPV4_TO_UINT32((arr)[0], (arr)[1], (arr)[2], (arr)[3])

/**
 * @brief 提取uint32_t IPv4地址的各个字节
 */
#define IPV4_GET_BYTE1(ip) (((ip) >> 24) & 0xFF)
#define IPV4_GET_BYTE2(ip) (((ip) >> 16) & 0xFF)
#define IPV4_GET_BYTE3(ip) (((ip) >> 8) & 0xFF)
#define IPV4_GET_BYTE4(ip) ((ip) & 0xFF)

/**
 * @brief 常用的IPv4地址宏定义
 */
#define IPV4_LOCALHOST IPV4_TO_UINT32(127, 0, 0, 1)
#define IPV4_BROADCAST IPV4_TO_UINT32(255, 255, 255, 255)
#define IPV4_ANY IPV4_TO_UINT32(0, 0, 0, 0)

    /* 示例使用方法：
     *
     * // IPv4地址转uint32_t
     * uint32_t ip = IPV4_TO_UINT32(192, 168, 1, 100);
     *
     * // uint32_t转IPv4数组
     * byte_t ip_array[4];
     * UINT32_TO_IPV4_ARRAY(ip, ip_array);
     *
     * // 从数组创建uint32_t
     * byte_t addr[4] = {192, 168, 1, 100};
     * uint32_t ip2 = IPV4_ARRAY_TO_UINT32(addr);
     *
     * // 提取各个字节
     * byte_t byte1 = IPV4_GET_BYTE1(ip); // 192
     */

    /* MAC地址低4位转换宏定义 */

/**
 * @brief 将MAC地址的低4个字节转换为uint32_t
 * @param mac2 MAC地址第3个字节 (0-255)
 * @param mac3 MAC地址第4个字节 (0-255)
 * @param mac4 MAC地址第5个字节 (0-255)
 * @param mac5 MAC地址第6个字节 (0-255)
 * @return uint32_t MAC地址低4位的32位表示
 */
#define MAC_LOW4_TO_UINT32(mac2, mac3, mac4, mac5) \
    ((uint32_t)(((uint32_t)((mac2) & 0xFF) << 24) | ((uint32_t)((mac3) & 0xFF) << 16) | ((uint32_t)((mac4) & 0xFF) << 8) | (uint32_t)((mac5) & 0xFF)))

/**
 * @brief 将uint32_t转换为MAC地址低4位数组
 * @param mac_low uint32_t格式的MAC地址低4位
 * @param arr 用于存储MAC地址低4位的数组，需要至少4个元素
 */
#define UINT32_TO_MAC_LOW4_ARRAY(mac_low, arr) \
    do                                         \
    {                                          \
        (arr)[0] = (((mac_low) >> 24) & 0xFF); \
        (arr)[1] = (((mac_low) >> 16) & 0xFF); \
        (arr)[2] = (((mac_low) >> 8) & 0xFF);  \
        (arr)[3] = ((mac_low) & 0xFF);         \
    } while (0)

/**
 * @brief 从MAC地址低4位数组创建uint32_t
 * @param arr MAC地址低4位数组，包含4个元素
 * @return uint32_t MAC地址低4位的32位表示
 */
#define MAC_LOW4_ARRAY_TO_UINT32(arr) \
    MAC_LOW4_TO_UINT32((arr)[0], (arr)[1], (arr)[2], (arr)[3])

/**
 * @brief 提取uint32_t MAC地址低4位的各个字节
 */
#define MAC_GET_BYTE3(mac_low) (((mac_low) >> 24) & 0xFF)
#define MAC_GET_BYTE4(mac_low) (((mac_low) >> 16) & 0xFF)
#define MAC_GET_BYTE5(mac_low) (((mac_low) >> 8) & 0xFF)
#define MAC_GET_BYTE6(mac_low) ((mac_low) & 0xFF)

    /* MAC地址使用示例：
     *
     * // MAC地址低4位转uint32_t (假设MAC为 AA:BB:CC:DD:EE:FF)
     * uint32_t mac_low = MAC_LOW4_TO_UINT32(0xCC, 0xDD, 0xEE, 0xFF);
     *
     * // uint32_t转MAC地址低4位数组
     * byte_t mac_array[4];
     * UINT32_TO_MAC_LOW4_ARRAY(mac_low, mac_array);
     *
     * // 从数组创建uint32_t
     * byte_t mac_bytes[4] = {0xCC, 0xDD, 0xEE, 0xFF};
     * uint32_t mac_low2 = MAC_LOW4_ARRAY_TO_UINT32(mac_bytes);
     */

    /**
     * @brief 将uint32_t IPv4地址展开为初始化列表格式
     * @param ip uint32_t格式的IPv4地址
     * @return 展开为 {byte1, byte2, byte3, byte4} 格式
     */
#define IPV4_UINT32_TO_INIT(ip) \
    (((ip) >> 24) & 0xFF),      \
        (((ip) >> 16) & 0xFF),  \
        (((ip) >> 8) & 0xFF),   \
        ((ip) & 0xFF)

    /**
     * @brief 将uint32_t MAC地址低4位展开为部分初始化列表格式
     * @param mac_low uint32_t格式的MAC地址低4位
     * @return 展开为 {byte3, byte4, byte5, byte6} 格式
     */
#define MAC_LOW4_UINT32_TO_INIT(mac_low) \
    (((mac_low) >> 24) & 0xFF),          \
        (((mac_low) >> 16) & 0xFF),      \
        (((mac_low) >> 8) & 0xFF),       \
        ((mac_low) & 0xFF)

#ifdef __cplusplus
}
#endif
#endif //! CNET_H
