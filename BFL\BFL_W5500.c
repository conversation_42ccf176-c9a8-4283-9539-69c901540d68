/**
 * @file BFL_W5500.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-12
 * @last modified 2025-08-12
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#include "BFL_W5500.h"
#include "F28x_Project.h"
#include <stddef.h>
#include "HDL_CPU_Time.h"

int16_t socket(uint16_t sn, uint16_t protocol, uint16_t port, uint16_t flag);
void W5500_Init_CPU1(void);
void W5500_Init_CPU2(void);
void W5500_Init(void);
static void W5500_Platform_Init();

//  #define CPU1_ONLY

void BFL_W5500_Init(void)
{
#if defined(CPU2)
    W5500_Init_CPU2();
    W5500_Platform_Init();
#elif defined(CPU1_ONLY)
    W5500_Init();
    // 初始化W5500平台
    W5500_Platform_Init();
#elif defined(CPU1)
    W5500_Init_CPU1();
#else
#error "CPU1 or CPU2 must be defined for W5500 initialization"
#endif // CPU1_ONLY
}

// W5500 socket pointer status
static uint16_t g_w5500_tx_write_ptr = 0;
static uint16_t g_w5500_rx_read_ptr = 0;
static uint16_t g_w5500_tx_free_size = 0;
static uint16_t g_w5500_rx_data_size = 0;

// Network configuration constants
#define SOCKET_NUM 0
static uint16_t SELF_PORT = 16011;
static uint16_t DEST_PORT = 16011;
#define BUFFER_SIZE_KB 16

// Initialize the network configuration structure
static wiz_NetInfo DEFAULT_NET_CONFIG = {
    .mac = {0x00, 0x08, 0xDC, 0x12, 0x34, 0x5C},
    .ip = {192, 168, 137, 99},
    .sn = {255, 255, 255, 0},
    .gw = {192, 168, 137, 2}};

static uint16_t dest_ip[4] = {192, 168, 137, 2};

/**
 * @brief Set default network configuration
 * @param config Pointer to network configuration structure
 */
void BFL_Net_set_default_config(const wiz_NetInfo *config)
{
    if (config)
    {
        DEFAULT_NET_CONFIG = *config;
    }
}

/**
 * @brief Set destination IP address
 * @param ip Destination IP address array, 4 bytes
 */
void BFL_Net_set_dest_ip(const byte_t ip[4])
{
    if (ip)
    {
        for (int i = 0; i < 4; i++)
        {
            dest_ip[i] = ip[i];
        }
    }
}

/**
 * @brief Get current default network configuration (read-only)
 * @return const wiz_NetInfo* Constant pointer to default network configuration
 */
const wiz_NetInfo *BFL_Net_get_default_config(void)
{
    return &DEFAULT_NET_CONFIG;
}

/**
 * @brief Get current destination IP address (read-only)
 * @param ip Array to receive destination IP address, at least 4 elements
 */
void BFL_Net_get_dest_ip(byte_t ip[4])
{
    if (ip)
    {
        for (int i = 0; i < 4; i++)
        {
            ip[i] = dest_ip[i];
        }
    }
}

void BFL_Net_Set_DstPort(uint16_t port) { DEST_PORT = port; }

void BFL_Net_Set_SelfPort(uint16_t port) { SELF_PORT = port; }

static void configure_network_settings(const wiz_NetInfo *config)
{
    setSHAR((uint16_t *)&config->mac);
    setGAR((uint16_t *)&config->gw);
    setSUBR((uint16_t *)&config->sn);
    setSIPR((uint16_t *)&config->ip);
}

static void verify_network_settings(void)
{
    wiz_NetInfo read_config;
    getSHAR(read_config.mac);
    getGAR(read_config.gw);
    getSUBR(read_config.sn);
    getSIPR(read_config.ip);
    // Configuration validation logic can be added here
}

static void setup_udp_socket(void)
{
    socket(SOCKET_NUM, Sn_MR_UDP, SELF_PORT, 0x00);
    setSn_DIPR(SOCKET_NUM, (uint16_t *)&dest_ip);
    setSn_DPORT(SOCKET_NUM, DEST_PORT);
    setSn_TXBUF_SIZE(SOCKET_NUM, BUFFER_SIZE_KB);
    setSn_RXBUF_SIZE(SOCKET_NUM, BUFFER_SIZE_KB);
    setSn_TX_WR(SOCKET_NUM, g_w5500_tx_write_ptr);
}

static void initialize_socket_pointers(void)
{
    uint16_t reg_buffer[2 * 5] = {0};
    WIZCHIP_READ_BUF(Sn_TX_FSR(SOCKET_NUM), reg_buffer, sizeof(reg_buffer) / sizeof(reg_buffer[0]));

    g_w5500_tx_write_ptr = (reg_buffer[4] << 8) | reg_buffer[5];
    g_w5500_rx_read_ptr = (reg_buffer[8] << 8) | reg_buffer[9];
}

static void W5500_Platform_Init()
{
    // Read the version register
    HDL_CPU_Time_DelayUs(1000);

    uint16_t version = getVERSIONR();

    // Configure network parameters
    configure_network_settings(&DEFAULT_NET_CONFIG);

    // Verify network configuration
    verify_network_settings();

    // Set up UDP socket
    setup_udp_socket();

    // Initialize pointers
    initialize_socket_pointers();
}

static void read_socket_status()
{
    uint16_t status_buffer[8] = {0};
    WIZCHIP_READ_BUF(Sn_TX_FSR(SOCKET_NUM), status_buffer, sizeof(status_buffer) / sizeof(status_buffer[0]));

    g_w5500_tx_free_size = (status_buffer[0] << 8) | status_buffer[1];
    g_w5500_tx_write_ptr = (status_buffer[4] << 8) | status_buffer[5];
    g_w5500_rx_data_size = (status_buffer[6] << 8) | status_buffer[7];
}

static void send_data(const uint16_t *data, uint16_t length)
{
    wiz_send_data_ex_sn0(&g_w5500_tx_write_ptr, (uint16_t *)data, length);

    setSn_CR(SOCKET_NUM, Sn_CR_SEND);
    while (getSn_CR(SOCKET_NUM)) // Wait for the command to be processed
        ;
}

static void receive_data(uint16_t *buffer, uint16_t data_size)
{
    uint32_t rx_addr = ((uint32_t)g_w5500_rx_read_ptr << 8) + (WIZCHIP_RXBUF_BLOCK(SOCKET_NUM) << 3);

    WIZCHIP_READ_BUF(rx_addr, buffer, data_size);
    g_w5500_rx_read_ptr += data_size;

    setSn_RX_RD(SOCKET_NUM, g_w5500_rx_read_ptr);
    setSn_CR(SOCKET_NUM, Sn_CR_RECV);
    while (getSn_CR(SOCKET_NUM)) // Wait for the command to be processed
        ;
}

w5500_rx_cb_t g_w5500_receive_data_callback = NULL;
uint16_t rx_buf[256] = {0};

// W5500 Poll function
void BFL_W5500_Poll()
{
#if defined(CPU1_ONLY)

    uint16_t rx_buffer[128] = {0};

    // Test data
    const uint16_t *test_data = "AcccccccccccccccaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaAcccccccccccccccaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
    uint16_t data_length = 121;

    // Limit data length
    if (data_length > W5500_MAX_TX_BUF_SIZE)
    {
        data_length = W5500_MAX_TX_BUF_SIZE;
    }

    // read socket status
    read_socket_status();

    // Adjust the send length based on the available buffer
    if (data_length > g_w5500_tx_free_size)
    {
        data_length = g_w5500_tx_free_size;
    }

    // Send data
    if (data_length > 0)
    {
        send_data(test_data, data_length);
    }

    // Receive data
    if (g_w5500_rx_data_size > 0)
    {
        receive_data(rx_buffer, g_w5500_rx_data_size);
        if (g_w5500_receive_data_callback != NULL)
        {
            g_w5500_receive_data_callback(0, rx_buffer, g_w5500_rx_data_size);
        }
    }

#elif defined(CPU2)
    // Read socket status
    read_socket_status();

    // Receive data
    if (g_w5500_rx_data_size > 0)
    {
        receive_data(rx_buf, g_w5500_rx_data_size);
        if (g_w5500_receive_data_callback != NULL)
        {
            g_w5500_receive_data_callback(0, rx_buf, g_w5500_rx_data_size);
        }
    }
#endif // CPU1_ONLY

    /*
     * TODO: Issues to be verified:
     * 1. Check the HALT comment in the DMA interrupt to see if RUNSTS will automatically RESET. -> Disabling the interrupt allows automatic RESET.
     *    This helps to determine the previous issue with the deadlock caused by using a bool flag. The manual states that it will be automatically cleared in non-continuous mode.
     * 2. Connect CS with high-speed flying wires. -> High-speed testing passed.
     * 3. Check if Sn_TX_WR and Sn_RX_RD will auto-increment. -> They will not.
     */
}

uint32_t BFL_W5500_UDP_Write_Blocked(uint16_t sn, const uint16_t *data, uint16_t length)
{
    // Limit data length
    if (length > W5500_MAX_TX_BUF_SIZE)
    {
        length = W5500_MAX_TX_BUF_SIZE;
    }

    // Send data
    if (length > 0)
    {
        send_data(data, length);
    }

    return length;
}

void BFL_W5500_UDP_Set_ReceiveCallback(w5500_rx_cb_t callback)
{
    g_w5500_receive_data_callback = callback;
}
