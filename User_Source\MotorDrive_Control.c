//=======================================================
//    Project：         MMC_PET_CPU1
//    File:                 MMC_PET_Control.c
//    Created on:     2021
//    By：                GCY
//    Describe：       定义全局变量函数
//=======================================================
//====================== Include ========================//
#include "MotorDrive_Control.h"

//====================== Defines ========================//

//====================Variable Definition=====================//
unsigned char ucTXMsgData[8] = {0x1, 0x2, 0x3, 0x4, 0, 0, 0, 0}; // TX Data
unsigned char ucRXMsgData[2] = {0, 0};                           // RX Data
uint32_t TxmessageSize = sizeof(ucTXMsgData);                    // Message Size (DLC)
uint32_t RxmessageSize = sizeof(ucRXMsgData);                    // Message Size (DLC)
volatile unsigned long msgCount = 0;                             // A counter that keeps track of the
                                                                 // number of times the transmit was
                                                                 // successful.
volatile unsigned long errFlag = 0;                              // A flag to indicate that some
                                                                 // transmission error occurred.

float Sinoperator; // theta 角度最小增量值 = 3.14 * 2 / oper_num = 3.14 * frequency / 5000
float oper_num;    // 生成一个周期正弦波需要的中断次数  1s/frequency/100us=10000/frequency;

float Idref = 0.0f, Iqref = 0.0f;
float Udcmax = 0.0f;
float ids = 0.0f;
float iqs = 0.0f;

float INV_Ubase = 0; //---1/输出电压基值

PID_STRUCT ACR_d_PID; // d轴电流控制器
PID_STRUCT ACR_q_PID; // q轴电流控制器

PID_STRUCT ASR_PI; // 速度环控制器

SVGENDPWM svgendpwm1;

float DutyA = 0, DutyB = 0, DutyC = 0; // 占空比寄存器
float DutyA_remain = 0, DutyB_remain = 0, DutyC_remain = 0;

float Xtheta = 0.0; // 角度

float sinthetaA = 0, sinthetaB = 0, sinthetaC = 0;
float costhetaA = 0, costhetaB = 0, costhetaC = 0;

unsigned int dac_num = 0; // 输出sin波形 角度计数

Uint16 SW1_STATUS = 1;      // 初始化为1，无按键按下
Uint16 PreCh1FB_STATUS = 0; // 初始化为0，继电器断开
Uint16 PreCh2FB_STATUS = 0; // 初始化为0，继电器断开

enum MCTRL_SM_ID mMCTRL_State = MCTRL_SM_ID_IDLE;
enum SpeedCtrlMode mSpeedCtrlMode = LoSpeedMode;

volatile struct CAN_MSG_STRUC CANmMsgRecv = {0, 0, 0, 0, 0, 0, 0, 0};
volatile struct CAN_MSG_STRUC CANmMsgTran = {0x55, 1, 0, 0, 0, 0, 1, 0xAA};

volatile struct CAN_MSG_STRUC CANBmMsgRecv = {0, 0, 0, 0, 0, 0, 0, 0};

volatile struct SCI_MSG_STRUC mMsgRecv_B = {0, 0, 0, 0, 0, 0, 0, 0};
volatile struct SCI_MSG_STRUC mMsgRecv_C = {0, 0, 0, 0, 0, 0, 0, 0};
volatile struct SCI_MSG_STRUC mMsgRecv_D = {0, 0, 0, 0, 0, 0, 0, 0};
volatile struct SCI_DATA_STRUC mMsgDATA_B = {0, 0, 0, 0, 0};
volatile struct SCI_DATA_STRUC mMsgDATA_C = {0, 0, 0, 0, 0};
volatile struct SCI_DATA_STRUC mMsgDATA_D = {0, 0, 0, 0, 0};

// volatile struct SCI_MSG_STRUC mMsgTran={0x55,1,0,0,0,0,1,0xAA};
Uint16 SCIA_DataCheckFLG = 0;
Uint16 SCIB_DataCheckFLG = 0;
Uint16 SCIC_DataCheckFLG = 0;
Uint16 SCID_DataCheckFLG = 0;

Uint16 SCIB_CheckErr_Cnt = 0;
Uint16 SCIC_CheckErr_Cnt = 0;
Uint16 SCID_CheckErr_Cnt = 0;

Uint16 SCIB_ResetEn = 0;
Uint16 SCIC_ResetEn = 0;
Uint16 SCID_ResetEn = 0;

enum SCI_SM_ID mSCI_State_B = SCI_SM_ID_IDLE;
enum SCI_SM_ID mSCI_State_C = SCI_SM_ID_IDLE;
enum SCI_SM_ID mSCI_State_D = SCI_SM_ID_IDLE;

enum CAN_MSG_SM_ID mCAN_MSG_State = CAN_MSG_SM_IDLE;

enum CAN_MSG_SM_ID Last_CMD = CAN_MSG_SM_IDLE; // 前一条指令值。

enum CANB_MSG_SM_ID mCANB_MSG_State = CANB_MSG_SM_IDLE;

Uint16 CAN_CMD = 0;     // CAN报文编号
Uint16 CAN_CMD1FLG = 0; // 指令1收到标志位。1---收到，0---未收到
Uint16 CAN_CMD2FLG = 0; // 指令2收到标志位
Uint16 CAN_CMD3FLG = 0; // 指令3收到标志位

Uint16 CAN_CMD4FLG = 0;
Uint16 CAN_CMD5FLG = 0;
Uint16 CAN_CMD6FLG = 0;
Uint16 CAN_CMD7FLG = 0;
Uint16 CAN_CMD8FLG = 0;
Uint16 CAN_CMD9FLG = 0;
Uint16 CAN_CMD10FLG = 0;
Uint16 CAN_CMD11FLG = 0;
Uint16 CAN_CMD12FLG = 0;

Uint16 Vdcset_ref = 100; // 母线电压设置参考值
Uint16 Iset_d_ref = 0;   // d轴电流参考值

Uint16 CAN_CMD30FLG = 0;

Uint16 CAN_CMD40FLG = 0; // 指令40收到标志位.

Uint16 CAN_CMD1OKFLG = 0; // 0---未执行完，1-----执行完。
Uint16 CAN_CMD2OKFLG = 0; // 指令2收到标志位
Uint16 CAN_CMD3OKFLG = 0;

Uint16 CAN_CMD4OKFLG = 0;
Uint16 CAN_CMD5OKFLG = 0;
Uint16 CAN_CMD6OKFLG = 0;
Uint16 CAN_CMD7OKFLG = 0;
Uint16 CAN_CMD8OKFLG = 0;
Uint16 CAN_CMD9OKFLG = 0;
Uint16 CAN_CMD10OKFLG = 0;
Uint16 CAN_CMD11OKFLG = 0;
Uint16 CAN_CMD12OKFLG = 0;

Uint16 CAN_CMD30OKFLG = 0;

Uint16 CAN_CMD40OKFLG = 0; // 指令40执行完成标志位

Uint16 PWMOffFlag = 0; // 只有当无硬件错误时，才可软件置位此标志

float Motor_Control_Ud; // 上个控制周期的Ud
float Motor_Control_Uq; // 上个控制周期的Uq

//=======================================================
// 函数名称:  ProcessCANA()
// 功能:   接收CAN数据
// 输入:
// 返回:
// 备注:
//=======================================================
void ProcessCANA(void)
{
    bool CanRx1status, CanRx2status, CanRx3status, CanRx4status, CanRx5status, CanRx6status;
    bool CanRx7status, CanRx8status, CanRx9status, CanRx10status, CanRx11status, CanRx12status;

    CanRx1status = getCANAMessage(CANA_RX1_MSG_OBJ);
    CanRx2status = getCANAMessage(CANA_RX2_MSG_OBJ);
    CanRx3status = getCANAMessage(CANA_RX3_MSG_OBJ);
    CanRx4status = getCANAMessage(CANA_RX4_MSG_OBJ);
    CanRx5status = getCANAMessage(CANA_RX5_MSG_OBJ);
    CanRx6status = getCANAMessage(CANA_RX6_MSG_OBJ);
    // Type II
    CanRx7status = getCANAMessageTypeII(CANA_RX7_MSG_OBJ);
    CanRx8status = getCANAMessageTypeII(CANA_RX8_MSG_OBJ);
    CanRx9status = getCANAMessageTypeII(CANA_RX9_MSG_OBJ);
    CanRx10status = getCANAMessageTypeII(CANA_RX10_MSG_OBJ);
    CanRx11status = getCANAMessageTypeII(CANA_RX11_MSG_OBJ);
    CanRx12status = getCANAMessageTypeII(CANA_RX12_MSG_OBJ);

    if (CanRx1status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD1; // 状态1查询指令
    else if (CanRx2status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD2; // 报文2设置速度指令
    else if (CanRx3status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD3; // 报文3故障复位指令（备用）
    else if (CanRx4status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD4; // 报文4状态2查询指令（测试）
    else if (CanRx5status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD5; // 报文5旋变调零；------继电器断开（测试用）
    else if (CanRx6status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD6; // 报文6驱动器状态（测试用）
    else if (CanRx7status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD7; // 报文7,设置速度环PI参数    （测试用）
    else if (CanRx8status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD8; // 报文8，设置电流环d轴PI参数    （测试用）
    else if (CanRx9status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD9; // 报文9，设置电流环q轴PI参数（测试用）
    else if (CanRx10status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD10; // 报文10，设置直流母线电压和电流环参考值   （测试用）
    else if (CanRx11status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD11; // 报文11，设置工作模式（测试用）
    else if (CanRx12status == true)
        mCAN_MSG_State = CAN_MSG_SM_CMD12; // 报文12，设置SCI boot模式（测试用）
                                           //    else if(mMsgRecv.DATA0==0xA3) mCAN_MSG_State=CAN_MSG_SM_CMD30;// 继电器断开（测试用）
                                           //   //else if(mMsgRecv.DATA0==6) mSCI_MSG_State=CAN_MSG_SM_CMD7;
                                           //    //else if(mMsgRecv.DATA0==7) mSCI_MSG_State=SCI_MSG_SM_CMD8;
                                           //
                                           //    else if(mMsgRecv.DATA0==0xAF) mCAN_MSG_State=CAN_MSG_SM_CMD40;  //急停
    else
        mCAN_MSG_State = CAN_MSG_SM_IDLE;

    // CAN接收到数据，则超时计数器清零
    if (CanRx1status == true)
        CANTimeOutCNT = 0; // 超时计数器清零
    else if (CanRx2status == true)
        CANTimeOutCNT = 0; // 超时计数器清零
    else if (CanRx3status == true)
        CANTimeOutCNT = 0; // 超时计数器清零
    else if (CanRx4status == true)
        CANTimeOutCNT = 0; // 超时计数器清零
    else if (CanRx5status == true)
        CANTimeOutCNT = 0; // 超时计数器清零
    else if (CanRx6status == true)
        CANTimeOutCNT = 0; // 超时计数器清零
    else if (CanRx7status == true)
        CANTimeOutCNT = 0; // 超时计数器清零
    else if (CanRx8status == true)
        CANTimeOutCNT = 0; // 超时计数器清零
    else if (CanRx9status == true)
        CANTimeOutCNT = 0; // 超时计数器清零）
    else if (CanRx10status == true)
        CANTimeOutCNT = 0; // 超时计数器清零
    else if (CanRx11status == true)
        CANTimeOutCNT = 0; // 超时计数器清零
    else if (CanRx12status == true)
        CANTimeOutCNT = 0; // 超时计数器清零

    ProcessCanMSG(); // CAN 数据处理
}

//=======================================================
// 函数名称:  ProcessCanMSG()
// 功能:   处理CAN数据
// 输入:
// 返回:
// 备注:
//=======================================================
void ProcessCanMSG()
{
    switch (mCAN_MSG_State)
    {
    case CAN_MSG_SM_IDLE:
        break;
    case CAN_MSG_SM_CMD1: // 报文1状态查询指令

        if ((CANmMsgRecv.DATA0 == 0xF8) && (CANmMsgRecv.DATA1 == 0x8F))
        {
            CANmMsgTran.DATA0 = mFaultCANStatus1Tran.OCFLT.all;
            CANmMsgTran.DATA1 = mFaultCANStatus1Tran.SpeedH;
            CANmMsgTran.DATA2 = mFaultCANStatus1Tran.SpeedL;
            CANmMsgTran.DATA3 = mFaultCANStatus1Tran.SYSSTATUS.all;
            CANmMsgTran.DATA4 = mFaultCANStatus1Tran.VbusH;
            CANmMsgTran.DATA5 = mFaultCANStatus1Tran.VbusL;
            CANmMsgTran.DATA6 = mFaultCANStatus1Tran.IbusH;
            CANmMsgTran.DATA7 = mFaultCANStatus1Tran.IbusL;
        }
        else
        {

            CANmMsgTran.DATA0 = mCANStatus1Tran.OCFLT.all;
            CANmMsgTran.DATA1 = mCANStatus1Tran.SpeedH;
            CANmMsgTran.DATA2 = mCANStatus1Tran.SpeedL;
            CANmMsgTran.DATA3 = mCANStatus1Tran.SYSSTATUS.all;
            CANmMsgTran.DATA4 = mCANStatus1Tran.VbusH;
            CANmMsgTran.DATA5 = mCANStatus1Tran.VbusL;
            CANmMsgTran.DATA6 = mCANStatus1Tran.IbusH;
            CANmMsgTran.DATA7 = mCANStatus1Tran.IbusL;
        }

        CAN_CMD1FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD1)
            CAN_CMD1OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA3 = CAN_CMD1OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD1;
        CAN_CMD = 1;

        sendCANAMessage(CANA_TX1_MSG_OBJ); // 状态数据反馈

        break;
    case CAN_MSG_SM_CMD2: // 报文2速度设置指令

        MCV.Last_Set_Speed_Ref = MCV.Set_Speed_Ref;
        MCV.Set_Speed_Ref = -(float)((int)((CANmMsgRecv.DATA0 << 8) | CANmMsgRecv.DATA1)); // 转速控制指令

        if (MCV.Set_Speed_Ref > mVar_RAM.Motor_Limits.Motor_Speed_Max)
            MCV.Set_Speed_Ref = mVar_RAM.Motor_Limits.Motor_Speed_Max; // 转速限幅
        else if (MCV.Set_Speed_Ref < mVar_RAM.Motor_Limits.Motor_Speed_Min)
            MCV.Set_Speed_Ref = mVar_RAM.Motor_Limits.Motor_Speed_Min;
        {
            if (MCV.Last_Set_Speed_Ref != MCV.Set_Speed_Ref) // 新的转速设置
                MCV.RampSet_Speed_Ref = MCV.mSpeed;
        }

        //         mMsgTran.DATA0 = mMsgRecv.DATA0;
        //         mMsgTran.DATA1 = mMsgRecv.DATA1;
        //         mMsgTran.DATA2 = mMsgRecv.DATA2;

        CAN_CMD2FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD2)
            CAN_CMD2OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA3 = CAN_CMD2OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD2;
        CAN_CMD = 2;
        break;
    case CAN_MSG_SM_CMD3: // 报文3故障复位指令（备用）

        //
        //         mMsgTran.DATA0 = mMsgRecv.DATA0;
        //         mMsgTran.DATA1 = mMsgRecv.DATA1;

        CAN_CMD3FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD3)
            CAN_CMD3OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA3 = CAN_CMD3OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD3;
        CAN_CMD = 3;
        break;

    case CAN_MSG_SM_CMD4: // 报文4状态1查询指令

        if ((CANmMsgRecv.DATA0 == 0xF9) && (CANmMsgRecv.DATA1 == 0x9F))
        {
            CANmMsgTran.DATA0 = mFaultCANStatus2Tran.INVSTATUS.all;
            CANmMsgTran.DATA1 = mFaultCANStatus2Tran.MOTSTATUS.all;
            CANmMsgTran.DATA2 = mFaultCANStatus2Tran.IaH;
            CANmMsgTran.DATA3 = mFaultCANStatus2Tran.IaL;
            CANmMsgTran.DATA4 = mFaultCANStatus2Tran.IbH;
            CANmMsgTran.DATA5 = mFaultCANStatus2Tran.IbL;
            CANmMsgTran.DATA6 = mFaultCANStatus2Tran.IcH;
            CANmMsgTran.DATA7 = mFaultCANStatus2Tran.IcL;
        }

        else
        {
            CANmMsgTran.DATA0 = mCANStatus2Tran.INVSTATUS.all;
            CANmMsgTran.DATA1 = mCANStatus2Tran.MOTSTATUS.all;
            CANmMsgTran.DATA2 = mCANStatus2Tran.IaH;
            CANmMsgTran.DATA3 = mCANStatus2Tran.IaL;
            CANmMsgTran.DATA4 = mCANStatus2Tran.IbH;
            CANmMsgTran.DATA5 = mCANStatus2Tran.IbL;
            CANmMsgTran.DATA6 = mCANStatus2Tran.IcH;
            CANmMsgTran.DATA7 = mCANStatus2Tran.IcL;
        }

        CAN_CMD4FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD4)
            CAN_CMD4OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA1 = SCI_CMD4OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD4;
        CAN_CMD = 4;

        sendCANAMessage(CANA_TX2_MSG_OBJ); // 状态数据反馈

        break;

    case CAN_MSG_SM_CMD5: // 定位电机零位（测试用），Ua：+, Ub：-，Uc:-;产生一小串小脉冲

        //         mMsgTran.DATA0 = mMsgRecv.DATA0;
        //         mMsgTran.DATA1 = mMsgRecv.DATA1;
        //         mMsgTran.DATA2 = mMsgRecv.DATA2;
        if (MCV.Set_Speed_Ref == 0) // 驱动器不运行时才可进行参数设置
        {

            RotTX_ZeroEN_FLG = 1;
        }

        CAN_CMD5FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD5)
            CAN_CMD5OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA3 = CAN_CMD30OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD5;
        CAN_CMD = 5;
        break;
    case CAN_MSG_SM_CMD6: // 报文6驱动器状态查询指令

        if ((CANmMsgRecv.DATA0 == 0xFA) && (CANmMsgRecv.DATA1 == 0xAF))
        {
            CANmMsgTran.DATA0 = mFaultCANDRVStatusTran.ERRORCODE1.all;
            CANmMsgTran.DATA1 = mFaultCANDRVStatusTran.Temp1;
            CANmMsgTran.DATA2 = mFaultCANDRVStatusTran.ERRORCODE2.all;
            CANmMsgTran.DATA3 = mFaultCANDRVStatusTran.Temp2;
            CANmMsgTran.DATA4 = mFaultCANDRVStatusTran.ERRORCODE3.all;
            CANmMsgTran.DATA5 = mFaultCANDRVStatusTran.Temp3;
            CANmMsgTran.DATA6 = mFaultCANDRVStatusTran.RSVD1;
            CANmMsgTran.DATA7 = mFaultCANDRVStatusTran.RSVD2;
        }
        else
        {
            CANmMsgTran.DATA0 = mCANDRVStatusTran.ERRORCODE1.all;
            CANmMsgTran.DATA1 = mCANDRVStatusTran.Temp1;
            CANmMsgTran.DATA2 = mCANDRVStatusTran.ERRORCODE2.all;
            CANmMsgTran.DATA3 = mCANDRVStatusTran.Temp2;
            CANmMsgTran.DATA4 = mCANDRVStatusTran.ERRORCODE3.all;
            CANmMsgTran.DATA5 = mCANDRVStatusTran.Temp3;
            CANmMsgTran.DATA6 = mCANDRVStatusTran.RSVD1;
            CANmMsgTran.DATA7 = mCANDRVStatusTran.RSVD2;
        }

        CAN_CMD6FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD6)
            CAN_CMD6OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA1 = SCI_CMD4OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD6;
        CAN_CMD = 6;

        sendCANAMessage(CANA_TX3_MSG_OBJ); // 状态数据反馈

        break;
    case CAN_MSG_SM_CMD7: //

        CANmMsgTran.DATA0 = 0x01;

        if (MCV.Set_Speed_Ref == 0) // 驱动器不运行时才可进行参数设置
        {
            ASR_PI.Kp = ((float)((CANmMsgRecv.DATA0 << 8) | CANmMsgRecv.DATA1)) * 0.00001; // 转速控制P参数

            ASR_PI.Ki = ((float)((CANmMsgRecv.DATA2 << 8) | CANmMsgRecv.DATA3)) * 0.000001; // 转速控制I参数，比屏幕设置小10倍

            CANmMsgTran.DATA1 = 0x01;
        }
        else
            CANmMsgTran.DATA1 = 0x0;

        CAN_CMD7FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD7)
            CAN_CMD7OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA3 = CAN_CMD30OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD7;
        CAN_CMD = 7;

        sendCANAMessageTypeII(CANA_TX4_MSG_OBJ); // 参数设置状态数据反馈
        break;

    case CAN_MSG_SM_CMD8: //

        CANmMsgTran.DATA0 = 0x02;
        //        if(Set_Speed_Ref==0)//驱动器不运行时才可进行参数设置
        {
            ACR_d_PID.Kd_Filter = ((float)((CANmMsgRecv.DATA0 << 8) | CANmMsgRecv.DATA1)) * 0.001; // d轴电流速控制P参数

            ACR_d_PID.Kd = ((float)((CANmMsgRecv.DATA2 << 8) | CANmMsgRecv.DATA3)) * 0.0001; // d轴电流控制D参数
            CANmMsgTran.DATA1 = 0x01;
        }
        //        else     CANmMsgTran.DATA1 = 0x0;

        CAN_CMD8FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD8)
            CAN_CMD8OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA3 = CAN_CMD30OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD8;
        CAN_CMD = 8;

        sendCANAMessageTypeII(CANA_TX4_MSG_OBJ); // 参数设置状态数据反馈
        break;

    case CAN_MSG_SM_CMD9: //

        CANmMsgTran.DATA0 = 0x03;
        //        if(Set_Speed_Ref==0)//驱动器不运行时才可进行参数设置
        {
            ACR_q_PID.Kd_Filter = ((float)((CANmMsgRecv.DATA0 << 8) | CANmMsgRecv.DATA1)) * 0.001; // q轴电流速控制P参数

            ACR_q_PID.Kd = ((float)((CANmMsgRecv.DATA2 << 8) | CANmMsgRecv.DATA3)) * 0.0001; // q轴电流控制D参数
            CANmMsgTran.DATA1 = 0x01;
        }
        //        else     CANmMsgTran.DATA1 = 0x0;

        CAN_CMD9FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD9)
            CAN_CMD9OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA3 = CAN_CMD30OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD9;
        CAN_CMD = 9;

        sendCANAMessageTypeII(CANA_TX4_MSG_OBJ); // 参数设置状态数据反馈
        break;

    case CAN_MSG_SM_CMD10: //

        CANmMsgTran.DATA0 = 0x04;
        if (MCV.Set_Speed_Ref == 0) // 驱动器不运行时才可进行参数设置
        {
            Vdcset_ref = ((float)((CANmMsgRecv.DATA0 << 8) | CANmMsgRecv.DATA1)); // 母线电压设置参考值
            Iset_d_ref = ((float)((CANmMsgRecv.DATA2 << 8) | CANmMsgRecv.DATA3)); // d轴电流参考值

            CANmMsgTran.DATA1 = 0x01;
        }
        else
            CANmMsgTran.DATA1 = 0x0;

        CAN_CMD10FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD10)
            CAN_CMD10OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA3 = CAN_CMD30OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD10;
        CAN_CMD = 10;

        sendCANAMessageTypeII(CANA_TX4_MSG_OBJ); // 参数设置状态数据反馈
        break;

    case CAN_MSG_SM_CMD11: // 控制模式设置（测试用）

        CANmMsgTran.DATA0 = 0x05;

        if (MCV.Set_Speed_Ref == 0) // 驱动器不运行时才可进行参数设置
        {
            if (CANmMsgRecv.DATA0 == 1)
            {
                SYS_OpertionMode = 1; // 电流环模式
                CANmMsgTran.DATA1 = 0x01;
            }
            else if (CANmMsgRecv.DATA0 == 2)
            {
                SYS_OpertionMode = 2; // 速度环环模式
                CANmMsgTran.DATA1 = 0x01;
            }
            else
            {
                SYS_OpertionMode = 0;
                CANmMsgTran.DATA1 = 0x0;
            }
        }
        else
            CANmMsgTran.DATA1 = 0x0;

        CAN_CMD11FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD11)
            CAN_CMD11OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA3 = CAN_CMD30OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD11;
        CAN_CMD = 11;

        sendCANAMessageTypeII(CANA_TX4_MSG_OBJ); // 参数设置状态数据反馈
        break;

    case CAN_MSG_SM_CMD12: //

        CANmMsgTran.DATA0 = 0x06;
        if (MCV.Set_Speed_Ref == 0) // 驱动器不运行时才可进行参数设置
        {

            CANmMsgTran.DATA1 = 0x01;

            sendCANAMessageTypeII(CANA_TX4_MSG_OBJ); // 参数设置状态数据反馈

            DELAY_US(10000);

            SCI_BOOT_EN; // 输出低，使能SCI boot 模式，该引脚在DSP复位后会被置位高电平。

            DELAY_US(10);
            MY_SysCtl_resetDevice(); // 系统复位
        }
        else
            CANmMsgTran.DATA1 = 0x0;

        CAN_CMD12FLG = 1; // 该指令执行完，该位被置0
        if (Last_CMD != CAN_MSG_SM_CMD12)
            CAN_CMD12OKFLG = 0; // 收到新指令，该字节清零0，指令执行完该节被置1
        // mMsgTran.DATA3 = CAN_CMD30OKFLG;//该指令执行完，该字节被置1
        Last_CMD = CAN_MSG_SM_CMD12;
        CAN_CMD = 12;

        sendCANAMessageTypeII(CANA_TX4_MSG_OBJ); // 参数设置状态数据反馈

        break;

    default:
        break;
    }
}

//=======================================================
// 函数名称:  ProcessCANB()
// 功能:   接收CANB数据
// 输入:
// 返回:
// 备注:
//=======================================================
void ProcessCANB(void)
{
    bool CanbRx1status;

    CanbRx1status = getCANBMessage(CANB_RX1_MSG_OBJ);

    if (CanbRx1status == true)
        mCANB_MSG_State = CANB_MSG_SM_CMD1; // 报文1，DC-DC模块状态
    else
        mCANB_MSG_State = CANB_MSG_SM_IDLE;

    // CAN接收到数据，则超时计数器清零
    if (CanbRx1status == true)
        CANBTimeOutCNT = 0; // 超时计数器清零

    switch (mCANB_MSG_State)
    {
    case CANB_MSG_SM_IDLE:
        break;
    case CANB_MSG_SM_CMD1: // 报文1，DC-DC模块状态

        if (CANBmMsgRecv.DATA0 == 0x0)
        {
            DCtoDCFLTFlag = 0x0; // 无故障
            //        	CANBTimeOutCNT = 0;
        }
        else
            DCtoDCFLTFlag = 0x1; // 有故障

        break;

    default:
        break;
    }
}

//=======================================================
// 函数名称:  ProcessSCIB()
// 功能:   处理SCIB数据----RXIN_4接口
// 输入:
// 返回:
// 备注:
//=======================================================
void ProcessSCIB(void)
{
    uint16_t ReceivedChar;

    // If there is a new data
    //
    if ((ScibRegs.SCIRXST.bit.RXERROR == 1) && (SCIB_ResetEn == 0)) // 有故障则重启SCI,一次复位结束才可响应第二次
    {

        //
        ScibRegs.SCICTL1.bit.SWRESET = 0; // If a SCI error is detected, reset SCI.
        SCIB_ResetEn = 1;
        //        F28x_usDelay(10000);//延时要够长
        //
        //        ScibRegs.SCICTL1.bit.SWRESET=1;//Re-eanble SCI.
        // Init_scib_fifo();//重新初始化
    }

    if (SCIB_ResetEn == 1)
    {

        SCIB_CheckErr_Cnt++; // 计数延时后复位

        if (SCIB_CheckErr_Cnt > 10000)
        {
            ScibRegs.SCICTL1.bit.SWRESET = 1; // Re-eanble SCI.
            SCIB_CheckErr_Cnt = 0;
            SCIB_ResetEn = 0;
        }
    }

    while (ScibRegs.SCIFFRX.bit.RXFFST > 0)
    {
        ReceivedChar = (ScibRegs.SCIRXBUF.all) & 0xff;
        switch (mSCI_State_B)
        {
        case SCI_SM_ID_IDLE: // SCI idle state
            if (ReceivedChar == SCI_CMD_HEADER1)
            {
                mSCI_State_B = SCI_SM_ID_HEADER2; // header match, to state 1
                mMsgRecv_B.Header1 = ReceivedChar;
            }
            else
                mSCI_State_B = SCI_SM_ID_IDLE; // SCI header doesn't match, to state 0

            break;
        case SCI_SM_ID_HEADER2: // SCI header matches, see the data length.
            if (ReceivedChar == SCI_CMD_HEADER2)
            {
                mSCI_State_B = SCI_SM_ID_DATA4; // header match, to DATA4
                mMsgRecv_B.Header2 = ReceivedChar;
            }
            else
                mSCI_State_B = SCI_SM_ID_IDLE; // SCI header doesn't match, to state 0

            break;

        case SCI_SM_ID_DATA4:
            mMsgRecv_B.DATA4 = ReceivedChar;
            mSCI_State_B = SCI_SM_ID_DATA3;
            break;
        case SCI_SM_ID_DATA3:
            mMsgRecv_B.DATA3 = ReceivedChar;
            mSCI_State_B = SCI_SM_ID_DATA2;
            break;
        case SCI_SM_ID_DATA2:
            mMsgRecv_B.DATA2 = ReceivedChar;
            mSCI_State_B = SCI_SM_ID_DATA1;
            break;
        case SCI_SM_ID_DATA1:
            mMsgRecv_B.DATA1 = ReceivedChar;
            mSCI_State_B = SCI_SM_ID_DATA0;
            break;
        case SCI_SM_ID_DATA0:
            mMsgRecv_B.DATA0 = ReceivedChar;
            mSCI_State_B = SCI_SM_ID_CHECKSUM;
            break;
        case SCI_SM_ID_CHECKSUM:
            mMsgRecv_B.CHECKSUM = ReceivedChar;

            if (((mMsgRecv_B.Header1 + mMsgRecv_B.Header2 + mMsgRecv_B.DATA4 + mMsgRecv_B.DATA3 + mMsgRecv_B.DATA2 + mMsgRecv_B.DATA1 + mMsgRecv_B.DATA0 + mMsgRecv_B.CHECKSUM) & 0xFF) == 0) // Check sum passed
            {
                SCIB_DataCheckFLG = 1; // 数据校验通过
                mMsgDATA_B.Volt_15V = mMsgRecv_B.DATA4;
                mMsgDATA_B.Volt_5V = mMsgRecv_B.DATA3;
                mMsgDATA_B.Temperature = mMsgRecv_B.DATA2;
                mMsgDATA_B.errcode.all = mMsgRecv_B.DATA1;
                mMsgDATA_B.Rsvd2 = mMsgRecv_B.DATA0;
            }
            else
            {

                SCIB_DataCheckFLG = 0; // 数据校验未通过
            }

            mSCI_State_B = SCI_SM_ID_IDLE; // Return to state 0

            break;

        default:
            break;
        }
    }
}

//=======================================================
// 函数名称:  ProcessSCIC()
// 功能:   处理SCIC数据----RXIN_3接口
// 输入:
// 返回:
// 备注:
//=======================================================
void ProcessSCIC(void)
{
    uint16_t ReceivedChar;

    // If there is a new data
    if ((ScicRegs.SCIRXST.bit.RXERROR == 1) && (SCIC_ResetEn == 0)) // 有故障则重启SCI,一次复位结束才可响应第二次
    {

        ScicRegs.SCICTL1.bit.SWRESET = 0; // If a SCI error is detected, reset SCI.
        SCIC_ResetEn = 1;
    }

    if (SCIC_ResetEn == 1)
    {

        SCIC_CheckErr_Cnt++; // 计数延时后复位

        if (SCIC_CheckErr_Cnt > 10000)
        {
            ScicRegs.SCICTL1.bit.SWRESET = 1; // Re-eanble SCI.
            SCIC_CheckErr_Cnt = 0;
            SCIC_ResetEn = 0;
        }
    }

    while (ScicRegs.SCIFFRX.bit.RXFFST > 0)
    {
        ReceivedChar = (ScicRegs.SCIRXBUF.all) & 0xff;
        switch (mSCI_State_C)
        {
        case SCI_SM_ID_IDLE: // SCI idle state
            if (ReceivedChar == SCI_CMD_HEADER1)
            {
                mSCI_State_C = SCI_SM_ID_HEADER2; // header match, to state 1
                mMsgRecv_C.Header1 = ReceivedChar;
            }
            else
                mSCI_State_C = SCI_SM_ID_IDLE; // SCI header doesn't match, to state 0

            break;
        case SCI_SM_ID_HEADER2: // SCI header matches, see the data length.
            if (ReceivedChar == SCI_CMD_HEADER2)
            {
                mSCI_State_C = SCI_SM_ID_DATA4; // header match, to DATA4
                mMsgRecv_C.Header2 = ReceivedChar;
            }
            else
                mSCI_State_C = SCI_SM_ID_IDLE; // SCI header doesn't match, to state 0

            break;

        case SCI_SM_ID_DATA4:
            mMsgRecv_C.DATA4 = ReceivedChar;
            mSCI_State_C = SCI_SM_ID_DATA3;
            break;
        case SCI_SM_ID_DATA3:
            mMsgRecv_C.DATA3 = ReceivedChar;
            mSCI_State_C = SCI_SM_ID_DATA2;
            break;
        case SCI_SM_ID_DATA2:
            mMsgRecv_C.DATA2 = ReceivedChar;
            mSCI_State_C = SCI_SM_ID_DATA1;
            break;
        case SCI_SM_ID_DATA1:
            mMsgRecv_C.DATA1 = ReceivedChar;
            mSCI_State_C = SCI_SM_ID_DATA0;
            break;
        case SCI_SM_ID_DATA0:
            mMsgRecv_C.DATA0 = ReceivedChar;
            mSCI_State_C = SCI_SM_ID_CHECKSUM;
            break;
        case SCI_SM_ID_CHECKSUM:
            mMsgRecv_C.CHECKSUM = ReceivedChar;

            if (((mMsgRecv_C.Header1 + mMsgRecv_C.Header2 + mMsgRecv_C.DATA4 + mMsgRecv_C.DATA3 + mMsgRecv_C.DATA2 + mMsgRecv_C.DATA1 + mMsgRecv_C.DATA0 + mMsgRecv_C.CHECKSUM) & 0xFF) == 0) // Check sum passed
            {
                SCIC_DataCheckFLG = 1; // 数据校验通过

                mMsgDATA_C.Volt_15V = mMsgRecv_C.DATA4;
                mMsgDATA_C.Volt_5V = mMsgRecv_C.DATA3;
                mMsgDATA_C.Temperature = mMsgRecv_C.DATA2;
                mMsgDATA_C.errcode.all = mMsgRecv_C.DATA1;
                mMsgDATA_C.Rsvd2 = mMsgRecv_C.DATA0;

                // SCI_SetPower_Watts=((float)(mMsgRecv.RXDATA1.bit.POWER))*100;//串口数据校验通过，计算触摸板串口设定输出功率值
            }
            else
            {

                SCIC_DataCheckFLG = 0; // 数据校验未通过
            }

            mSCI_State_C = SCI_SM_ID_IDLE; // Return to state 0

            break;

        default:
            break;
        }
    }
}

//=======================================================
// 函数名称:  ProcessSCID()
// 功能:   处理SCID数据----RXIN_2接口
// 输入:
// 返回:
// 备注:
//=======================================================
void ProcessSCID(void)
{
    uint16_t ReceivedChar;

    // If there is a new data
    //
    if ((ScidRegs.SCIRXST.bit.RXERROR == 1) && (SCID_ResetEn == 0)) // 有故障则重启SCI,一次复位结束才可响应第二次
    {

        ScidRegs.SCICTL1.bit.SWRESET = 0; // If a SCI error is detected, reset SCI.
        SCID_ResetEn = 1;
    }

    if (SCID_ResetEn == 1)
    {

        SCID_CheckErr_Cnt++; // 计数延时后复位

        if (SCID_CheckErr_Cnt > 10000)
        {
            ScidRegs.SCICTL1.bit.SWRESET = 1; // Re-eanble SCI.
            SCID_CheckErr_Cnt = 0;
            SCID_ResetEn = 0;
        }
    }

    while (ScidRegs.SCIFFRX.bit.RXFFST > 0)
    {
        ReceivedChar = (ScidRegs.SCIRXBUF.all) & 0xff;
        switch (mSCI_State_D)
        {
        case SCI_SM_ID_IDLE: // SCI idle state
            if (ReceivedChar == SCI_CMD_HEADER1)
            {
                mSCI_State_D = SCI_SM_ID_HEADER2; // header match, to state 1
                mMsgRecv_D.Header1 = ReceivedChar;
            }
            else
                mSCI_State_D = SCI_SM_ID_IDLE; // SCI header doesn't match, to state 0

            break;
        case SCI_SM_ID_HEADER2: // SCI header matches, see the data length.
            if (ReceivedChar == SCI_CMD_HEADER2)
            {
                mSCI_State_D = SCI_SM_ID_DATA4; // header match, to DATA4
                mMsgRecv_D.Header2 = ReceivedChar;
            }
            else
                mSCI_State_D = SCI_SM_ID_IDLE; // SCI header doesn't match, to state 0

            break;

        case SCI_SM_ID_DATA4:
            mMsgRecv_D.DATA4 = ReceivedChar;
            mSCI_State_D = SCI_SM_ID_DATA3;
            break;
        case SCI_SM_ID_DATA3:
            mMsgRecv_D.DATA3 = ReceivedChar;
            mSCI_State_D = SCI_SM_ID_DATA2;
            break;
        case SCI_SM_ID_DATA2:
            mMsgRecv_D.DATA2 = ReceivedChar;
            mSCI_State_D = SCI_SM_ID_DATA1;
            break;
        case SCI_SM_ID_DATA1:
            mMsgRecv_D.DATA1 = ReceivedChar;
            mSCI_State_D = SCI_SM_ID_DATA0;
            break;
        case SCI_SM_ID_DATA0:
            mMsgRecv_D.DATA0 = ReceivedChar;
            mSCI_State_D = SCI_SM_ID_CHECKSUM;
            break;
        case SCI_SM_ID_CHECKSUM:
            mMsgRecv_D.CHECKSUM = ReceivedChar;

            if (((mMsgRecv_D.Header1 + mMsgRecv_D.Header2 + mMsgRecv_D.DATA4 + mMsgRecv_D.DATA3 + mMsgRecv_D.DATA2 + mMsgRecv_D.DATA1 + mMsgRecv_D.DATA0 + mMsgRecv_D.CHECKSUM) & 0xFF) == 0) // Check sum passed
            {
                SCID_DataCheckFLG = 1; // 数据校验通过
                mMsgDATA_D.Volt_15V = mMsgRecv_D.DATA4;
                mMsgDATA_D.Volt_5V = mMsgRecv_D.DATA3;
                mMsgDATA_D.Temperature = mMsgRecv_D.DATA2;
                mMsgDATA_D.errcode.all = mMsgRecv_D.DATA1;
                mMsgDATA_D.Rsvd2 = mMsgRecv_D.DATA0;
                // SCI_SetPower_Watts=((float)(mMsgRecv.RXDATA1.bit.POWER))*100;//串口数据校验通过，计算触摸板串口设定输出功率值
            }
            else
            {

                SCID_DataCheckFLG = 0; // 数据校验未通过
                SCID_CheckErr_Cnt++;
            }

            mSCI_State_D = SCI_SM_ID_IDLE; // Return to state 0

            break;

        default:
            break;
        }
    }
}

//=======================================================
// 函数名称: SPWM_Vref()
// 功能:  产生SPWM
// 输入:
// 返回:
// 备注:
//=======================================================
void SPWM_Vref(void)
{
    dac_num++;
    Xtheta += Sinoperator;
    if (dac_num >= oper_num)
    {
        dac_num = 0;
        Xtheta = 0;
    }
    sinthetaA = sin(Xtheta);
    sinthetaB = sinthetaA * (-0.5) - costhetaA * 0.8660254; // sinthetaB=sin(A-120)
    sinthetaC = sinthetaA * (-0.5) + costhetaA * 0.8660254; // sinthetaC=sin(A+120)=sin(A-240)

    costhetaA = cos(Xtheta);
    costhetaB = costhetaA * (-0.5) + sinthetaA * 0.8660254; // costhetaB=cos(A-120)
    costhetaC = costhetaA * (-0.5) - sinthetaA * 0.8660254; // costhetaB=cos(A+120)=cos(A-240)
}
//=======================================================
// 函数名称: Spll_Control()
// 功能:  锁相环控制函数
// 输入:
// 返回:
// 备注:
//=======================================================
void Spll_Control(void)
{
    // TEST
    MCV.Va = sinthetaA * 48.0f;
    MCV.Vb = sinthetaB * 48.0f;
    MCV.Vc = sinthetaC * 48.0f;

    // Phase lock loop//
    abc_dq0_pos1.a = MCV.Va * 0.001f; // (GridMeas1);//Phase A voltage---缩小1000倍，防止Vq大于1或小于-1；
    abc_dq0_pos1.b = MCV.Vb * 0.001f; //(GridMeas2);//Phase B voltage
    abc_dq0_pos1.c = MCV.Vc * 0.001f; //(GridMeas3);//Phase C voltage

    abc_dq0_pos1.sin = (float)sin((spll1.theta[1] - (0.5f * 3.1415926f))); // 公式为余弦形式，需减去pi/2
    abc_dq0_pos1.cos = (float)cos((spll1.theta[1] - (0.5f * 3.1415926f))); // 参考：https://zhuanlan.zhihu.com/p/74763018

    ABC_DQ0_POS_F_MACRO(abc_dq0_pos1); // 求出Vd   Vq

    spll1.v_q[0] = (abc_dq0_pos1.q);
    // SPLL call
    SPLL_3ph_SRF_F_FUNC(&spll1);
}
//=======================================================
// 函数名称: Controlloops()
// 功能:  电压电流双环控制，控制直流母线输出电压。
// 输入:
// 返回:
// 备注:
//=======================================================
void Controlloops(void)
{
    float Temp_Ud, Temp_Uq;
    float Id_limit, Iq_limit;
    float Ud, Uq, Us, InvUs;
    float Ids_wk_new;
//    float L = 110e-6f; // 逆变阻感负载条件下的电感值。

    float mSpeed_abs;

    if (MCV.RampSet_Speed_Ref < MCV.Set_Speed_Ref) // 加速
    {
        MCV.RampSet_Speed_Ref = MCV.RampSet_Speed_Ref + 0.03125f; // 从0~2500r/min用时8s。
    }
    else if (MCV.RampSet_Speed_Ref > MCV.Set_Speed_Ref) //---减速
    {
        MCV.RampSet_Speed_Ref = MCV.RampSet_Speed_Ref - 0.03125f; // 从0~2500r/min用时8s
    }
    else
    {
        MCV.RampSet_Speed_Ref = MCV.Set_Speed_Ref;
    }

    // 速度控制环//

    ASR_PI.Ref = MCV.RampSet_Speed_Ref; // Set_Speed_Ref;   //参考值
    ASR_PI.Fdb = MCV.mSpeed;            // 更新反馈值。
    // ASR_PI.OutMax =  mVar_RAM.Motor_Limits.Motor_Iq_Max;    //更新Id=0模式
    // ASR_PI.OutMin  = mVar_RAM.Motor_Limits.Motor_Iq_Min;
    //

    MCV.mTe_ref = Controller_runPID(&ASR_PI);                                                                        //
    MCV.mTe_ref = fmaxf(fminf(mVar_RAM.Motor_Limits.Motor_Iq_Max, MCV.mTe_ref), mVar_RAM.Motor_Limits.Motor_Iq_Min); // IQ限幅

    Udcmax = MCV.Vbus_filter * 0.5774f; // 1/1.732f;

    mSpeed_abs = fabsf(MCV.RampSet_Speed_Ref);
    if (mSpeed_abs > 2000.0f)
    {
        Ids_wk_new = (2000.0f - mSpeed_abs) * 0.22f - fabsf(MCV.mTe_ref) * 0.15f + (MCV.Vbus_filter - 400.0f) * 1.0f; // [0.1 0.14 0.5]
    }
    else
    {
        Ids_wk_new = 0.0f;
    }

    if (Ids_wk_new > 0.0f)
    {
        Ids_wk_new = 0.0f;
    }

    if (Ids_wk_new < mVar_RAM.Motor_Limits.Motor_Id_Min)
        Ids_wk_new = mVar_RAM.Motor_Limits.Motor_Id_Min;

    if (ids > Ids_wk_new)
        ids = Ids_wk_new;
    else
        ids += 0.005f;

    if (mSpeedCtrlMode == LoSpeedMode)
        ids = 0.0f; // 低速模式，不使能弱磁控制

    iqs = MCV.mTe_ref;

    if (iqs > mVar_RAM.Motor_Limits.Motor_Iq_Max)
        iqs = mVar_RAM.Motor_Limits.Motor_Iq_Max;
    else if (iqs < mVar_RAM.Motor_Limits.Motor_Iq_Min)
        iqs = mVar_RAM.Motor_Limits.Motor_Iq_Min;

    // Test mode selection//

     if (SYS_OpertionMode == 1) // 电流环模式
     {
         if (abc_dq0_pos2.d + 2.0f < Iset_d_ref)
             Idref = abc_dq0_pos2.d + 2.0f;
         else
             Idref = Iset_d_ref;

         if (abc_dq0_pos2.q + 2.0f < MCV.Iset_q_ref)
             Iqref = abc_dq0_pos2.q + 2.0f;
         else
             Iqref = MCV.Iset_q_ref;
     }
     else if (SYS_OpertionMode == 2) // 速度环模式
    {
        //        Idref= MTPA_Idq1.Idref;
        //        Iqref= MTPA_Idq1.Iqref;
        //        if(mSpeed+2.0f < Set_Speed_Ref)
        //           Set_Speed_Ref = mSpeed+2.0f;
        //        else
        //            = Set_Speed_Ref;
        Idref = ids; // MTPA_Idq1.Idref; //Id=0模式控制MTPA_Idq1.Idref;//
        //        Iqref = Set_Speed_Ref*0.1;
        Iqref = iqs; // MTPA_Idq1.Iqref;//mTe_ref;//MTPA_Idq1.Iqref;
    }
    else
    {
        Idref = 0;
        Iqref = 0;
    }

    // 输出电流DQ解耦// 采用float型，电流值不再归一化
    abc_dq0_pos2.a = MCV.Ia; // costhetaA*48;// (GridMeas1);//Phase A Current---；
    abc_dq0_pos2.b = MCV.Ib; // costhetaB*48;//(GridMeas2);//Phase B Current
    abc_dq0_pos2.c = MCV.Ic; // costhetaC*48;//(GridMeas3);//Phase C Current

//    if (SYS_OpertionMode == 1) // 电流环模式
//    {
//        SPWM_Vref();    // 产生SPWM函数
//        Spll_Control(); // 锁相环控制函数
//
//        abc_dq0_pos2.sin = (float)sin(spll1.theta[1]); // 锁相环输出角度给dq_abc变换
//        abc_dq0_pos2.cos = (float)cos(spll1.theta[1]);
//    }
//    else if

    if (SYS_OpertionMode == 2 || SYS_OpertionMode == 1) // 速度/电流环模式
    {
        // abc_dq0_pos2.sin = (float)sin(mEthetaRad+mCalSpeed.Omega_Estimate*mCalSpeed.Sample_Time);//旋变解码器输出角度给dq_abc变换
        // abc_dq0_pos2.cos = (float)cos(mEthetaRad+mCalSpeed.Omega_Estimate*mCalSpeed.Sample_Time);

        abc_dq0_pos2.sin = (float)sin(MCV.mEthetaRad); // 旋变解码器输出角度给dq_abc变换，上个周期的电流值，对应上个周期的角度
        abc_dq0_pos2.cos = (float)cos(MCV.mEthetaRad);
    }

    ABC_DQ0_POS_F_MACRO(abc_dq0_pos2); // 求出Id   Iq

    // 根据电机方程，求出当前周期的电流值
    abc_dq0_pos2.d += (Motor_Control_Ud * mVar_RAM.Motor_Parameters.Motor_Ld_Inv - mVar_RAM.Motor_Parameters.Motor_Rs * mVar_RAM.Motor_Parameters.Motor_Ld_Inv * abc_dq0_pos2.d + MCV.we * abc_dq0_pos2.q * mVar_RAM.Motor_Parameters.Motor_Lq / mVar_RAM.Motor_Parameters.Motor_Ld) * mCalSpeed.Sample_Time * 0.6f;
    abc_dq0_pos2.q += (Motor_Control_Uq * mVar_RAM.Motor_Parameters.Motor_Lq_Inv - mVar_RAM.Motor_Parameters.Motor_Rs * mVar_RAM.Motor_Parameters.Motor_Lq_Inv * abc_dq0_pos2.q - MCV.we * abc_dq0_pos2.d * mVar_RAM.Motor_Parameters.Motor_Ld * mVar_RAM.Motor_Parameters.Motor_Lq_Inv - mVar_RAM.Motor_Parameters.Motor_Flux * mVar_RAM.Motor_Parameters.Motor_Lq_Inv * MCV.we) * mCalSpeed.Sample_Time * 0.6f;

    if (abc_dq0_pos2.q < 0)
        ASR_PI.OutMin = fmaxf(mVar_RAM.Motor_Limits.Motor_Iq_Min, abc_dq0_pos2.q - 50);
    if (abc_dq0_pos2.q > 0)
        ASR_PI.OutMax = fminf(mVar_RAM.Motor_Limits.Motor_Iq_Max, abc_dq0_pos2.q + 50);

    if (MCV.mSpeed > 50)
    {
        ASR_PI.OutMin = fmaxf(0, ASR_PI.OutMin);
        if (mSpeedCtrlMode == LoSpeedMode)
            ASR_PI.OutMax = fminf(mVar_RAM.Motor_Limits.Motor_Iq_Max_Lowspeed, ASR_PI.OutMax);
        else
            ASR_PI.OutMax = fminf(sqrtf(mVar_RAM.Motor_Limits.Motor_Iq_Max * mVar_RAM.Motor_Limits.Motor_Iq_Max - Idref * Idref), ASR_PI.OutMax);
    }
    else
    {
        if (MCV.mSpeed < -50)
        {
            ASR_PI.OutMax = fminf(0, ASR_PI.OutMax);
            if (mSpeedCtrlMode == LoSpeedMode)
                ASR_PI.OutMin = fmaxf(mVar_RAM.Motor_Limits.Motor_Iq_Min_Lowspeed, ASR_PI.OutMin);
            else
                ASR_PI.OutMin = fmaxf(-sqrtf(mVar_RAM.Motor_Limits.Motor_Iq_Max * mVar_RAM.Motor_Limits.Motor_Iq_Max - Idref * Idref), ASR_PI.OutMin);
        }
    }

    // 电流环--d轴//
    //    Idref = 0.0f;
    //    Iqref = 8.0f;
    ACR_d_PID.Ref = Idref;             // 参考值
    ACR_d_PID.Fdb = abc_dq0_pos2.d;    // dq解耦
    ACR_d_PID.OutMax = Udcmax * 0.75f; // 更新
    ACR_d_PID.OutMin = -Udcmax * 0.75f;
    Temp_Ud = Controller_runPID(&ACR_d_PID);

    // 电流环--q轴//
    ACR_q_PID.Ref = Iqref;            // 参考值
    ACR_q_PID.Fdb = abc_dq0_pos2.q;   // dq解耦。
    ACR_q_PID.OutMax = Udcmax * 1.0f; // 更新
    ACR_q_PID.OutMin = -Udcmax * 1.0f;
    Temp_Uq = Controller_runPID(&ACR_q_PID);

//    if (SYS_OpertionMode == 1) // 电流环模式
//    {
//        // 逆变器阻感负载电流控制
//        Ud = Temp_Ud - abc_dq0_pos2.q * 2 * 3.1415926 * 50 * L; // Id。
//        Uq = Temp_Uq + abc_dq0_pos2.d * 2 * 3.1415926 * 50 * L; // Iq。
//    }
//    else

    if (SYS_OpertionMode == 2 || SYS_OpertionMode == 1) // 速度环模式
    {
        // 电机控制//
        if (ACR_d_PID.Ref > 0)
        {
            Id_limit = fminf(abc_dq0_pos2.d, ACR_d_PID.Ref);
            Id_limit = fmaxf(0.0, Id_limit);
        }
        else
        {
            Id_limit = fmaxf(abc_dq0_pos2.d, ACR_d_PID.Ref);
            Id_limit = fminf(0.0, Id_limit);
        }

        if (ACR_q_PID.Ref > 0)
        {
            Iq_limit = fminf(abc_dq0_pos2.q, ACR_q_PID.Ref);
            Iq_limit = fmaxf(0.0, Iq_limit);
        }
        else
        {
            Iq_limit = fmaxf(abc_dq0_pos2.q, ACR_q_PID.Ref);
            Iq_limit = fminf(0.0, Iq_limit);
        }

        Ud = Temp_Ud;                                                        //-Iq_limit*we*Lq;//Id。
        Uq = Temp_Uq + MCV.we * mVar_RAM.Motor_Parameters.Motor_Flux * 0.8f; //+Id_limit*we*Ld; //Iq。（we*flux*0.735---此项限制到180左右）

        // Ud,Uq 限幅值。
        Us = sqrtf(Ud * Ud + Uq * Uq);
        InvUs = 1 / Us;

        if (Us > Udcmax)
        {
            Ud = Udcmax * Ud * InvUs;
            Uq = Udcmax * Uq * InvUs;
        }
    }

    // inverse park//
    // Test//

    //    Ud=0;
    //    Uq=20;

    ipark1.d = Ud; // ；
    ipark1.q = Uq;
    ipark1.zero = 0;

//    if (SYS_OpertionMode == 1) // 电流环模式
//    {
//        // 逆变器阻感负载电流控制
//        ipark1.sin = (float)sin(spll1.theta[1]); // 锁相环输出角度给dq_abc变换
//        ipark1.cos = (float)cos(spll1.theta[1]);
//    }
//    else

    if (SYS_OpertionMode == 2 || SYS_OpertionMode == 1) // 速度环模式
    {
        // 电机控制，角度需要根据上个周期的估算当前周期
        ipark1.sin = (float)sin(MCV.mEthetaRad + mCalSpeed.Omega_Estimate * mCalSpeed.Sample_Time); // 旋变解码器输出角度给dq_abc变换
        ipark1.cos = (float)cos(MCV.mEthetaRad + mCalSpeed.Omega_Estimate * mCalSpeed.Sample_Time);
    }

    iPARK_F_MACRO(ipark1); // 逆帕克变换
}

//=======================================================
// 函数名称:MTPA_Ctrl()
// 功能:   最大转矩控制
// 输入:
// 返回:
// 备注:();
//=======================================================
void MTPA_Ctrl(MTPA_F *m)
{
    float temp1, temp2, temp3, temp4, temp5;
    float pow_flux, pow_Ldq, pow_Iqref;

    // #define Ld      0.0001316
    // #define Lq      0.0001599
    // #define flux    0.1331
    // #define Rs      0.0117389
    // #define Pn   6

    pow_flux = mVar_RAM.Motor_Parameters.Motor_Flux * mVar_RAM.Motor_Parameters.Motor_Flux;
    pow_Ldq = (mVar_RAM.Motor_Parameters.Motor_Ld - mVar_RAM.Motor_Parameters.Motor_Lq) * (mVar_RAM.Motor_Parameters.Motor_Ld - mVar_RAM.Motor_Parameters.Motor_Lq);
    temp1 = 8 * (*m).mTe_ref * mVar_RAM.Motor_Parameters.Motor_Flux / 3 * mVar_RAM.Motor_Parameters.Motor_Pn_Inv;
    temp2 = pow_flux - 4 * pow_Ldq;
    temp3 = 4 * (*m).mTe_ref / 3 * mVar_RAM.Motor_Parameters.Motor_Pn_Inv;

    temp4 = sqrtf(temp1 * temp1 - 4 * temp2 * (temp3 * temp3 - pow_flux));

    (*m).Iqref = (temp1 + temp4) / (2 * temp2);
    pow_Iqref = (*m).Iqref * (*m).Iqref;

    temp5 = sqrtf(pow_flux + 4 * pow_Ldq * pow_Iqref);

    (*m).Idref = (-mVar_RAM.Motor_Parameters.Motor_Flux + temp5) / (2 * (mVar_RAM.Motor_Parameters.Motor_Ld - mVar_RAM.Motor_Parameters.Motor_Lq));
}

//=======================================================
// 函数名称:SVDPWM()
// 功能:   电压控制矢量调制，采用5段式
// 输入:
// 返回:
// 备注:();
//=======================================================
void SVDPWM(void)
{
    // SMDutysforPhaseA();//A相子模块占空比计算
    float inv_duty;
    svgendpwm1.Ualpha = ipark1.alpha * INV_Ubase; // inverse park对应,然后除以sqrt(3)*Vbus_filter/3。
    svgendpwm1.Ubeta = ipark1.beta * INV_Ubase;

    if (svgendpwm1.Ualpha > 1)
        svgendpwm1.Ualpha = 1; // 限幅
    if (svgendpwm1.Ubeta > 1)
        svgendpwm1.Ubeta = 1;

    MCV.mDuty = sqrtf(svgendpwm1.Ualpha * svgendpwm1.Ualpha + svgendpwm1.Ubeta * svgendpwm1.Ubeta); // 调制比计算
    Motor_Control_Ud = ipark1.d;
    Motor_Control_Uq = ipark1.q;
    if (MCV.mDuty > 0.98f)
    {
        inv_duty = 0.98f / MCV.mDuty;
        svgendpwm1.Ualpha *= inv_duty;
        svgendpwm1.Ubeta *= inv_duty;
        Motor_Control_Ud *= inv_duty;
        Motor_Control_Uq *= inv_duty;
    }

    runSVGENDPWM(&svgendpwm1);

    DutyA = svgendpwm1.Ta * mVar_RAM.EPWM_PERIOD_Base + DutyA_remain; /// 正三角计数法。
    if (DutyA < mVar_RAM.DutyMIN)
    {
        DutyA_remain = DutyA;
        DutyA = 0;
    }
    else if (DutyA > mVar_RAM.DutyMAX)
    {
        if (DutyA > mVar_RAM.EPWM_PERIOD_Base)
        {
            DutyA_remain = DutyA - mVar_RAM.EPWM_PERIOD_Base;
            DutyA = mVar_RAM.EPWM_PERIOD_Base;
        }
        else
        {
            DutyA_remain = DutyA - mVar_RAM.DutyMAX;
            DutyA = mVar_RAM.DutyMAX;
        }
    }
    else
    {
        DutyA_remain = 0.0f;
    }

    DutyB = svgendpwm1.Tb * mVar_RAM.EPWM_PERIOD_Base + DutyB_remain;
    if (DutyB < mVar_RAM.DutyMIN)
    {
        DutyB_remain = DutyB;
        DutyB = 0;
    }
    else if (DutyB > mVar_RAM.DutyMAX)
    {
        if (DutyB > mVar_RAM.EPWM_PERIOD_Base)
        {
            DutyB_remain = DutyB - mVar_RAM.EPWM_PERIOD_Base;
            DutyB = mVar_RAM.EPWM_PERIOD_Base;
        }
        else
        {
            DutyB_remain = DutyB - mVar_RAM.DutyMAX;
            DutyB = mVar_RAM.DutyMAX;
        }
    }
    else
    {
        DutyB_remain = 0.0f;
    }

    DutyC = svgendpwm1.Tc * mVar_RAM.EPWM_PERIOD_Base + DutyC_remain;
    if (DutyC < mVar_RAM.DutyMIN)
    {
        DutyC_remain = DutyC;
        DutyC = 0;
    }
    else if (DutyC > mVar_RAM.DutyMAX)
    {
        if (DutyC > mVar_RAM.EPWM_PERIOD_Base)
        {
            DutyC_remain = DutyC - mVar_RAM.EPWM_PERIOD_Base;
            DutyC = mVar_RAM.EPWM_PERIOD_Base;
        }
        else
        {
            DutyC_remain = DutyC - mVar_RAM.DutyMAX;
            DutyC = mVar_RAM.DutyMAX;
        }
    }
    else
    {
        DutyC_remain = 0.0f;
    }
}

//=======================================================
// 函数名称:PWMUpdate()
// 功能:   PWM更新
// 输入:
// 返回:
// 备注:
//=======================================================
void PWMUpdate(void)
{
    //------调制更新------//
    EPwm1Regs.CMPA.bit.CMPA = (Uint16)DutyA;
    EPwm2Regs.CMPA.bit.CMPA = (Uint16)DutyB;
    EPwm3Regs.CMPA.bit.CMPA = (Uint16)DutyC;
}

//=======================================================
void PWMSoftOutputOff() //  软件设计PWM关断----to do
{
    PWMOffFlag = 1; // 只有当无硬件错误时，才可软件置位此标志
    EALLOW;
    EPwm1Regs.TZFRC.bit.OST = 1; // Trip PWM
    EPwm2Regs.TZFRC.bit.OST = 1; // Trip PWM
    EPwm3Regs.TZFRC.bit.OST = 1; // Trip PWM

    EDIS;

    //    RunFlag=0;//运行标志位清零
    // SYS_RUN_LED_OFF=1;
    // PWM_Disable_Low=1;//驱动板PWM禁止
}

//=======================================================
// 函数名称:PWMSoftOutputOn()
// 功能:  PWM输出使能
// 输入:
// 返回:
// 备注:
//=======================================================
void PWMSoftOutputOn() //  软件设计PWM关断----to do
{

    EALLOW;
    EPwm1Regs.TZCLR.bit.OST = 1; // Clear Trip PWM
    EPwm2Regs.TZCLR.bit.OST = 1; // Clear Trip PWM
    EPwm3Regs.TZCLR.bit.OST = 1; // Clear Trip PWM
    EDIS;
    //    RunFlag=1;//运行标志位置位
    //    SYS_RUN_LED_ON=1;
    //    PWM_Disable_High=1;//驱动板PWM使能
    PWMOffFlag = 0; // 只有当无错误时，才可软件清零此标志
}

//=======================================================
// 函数名称:MotorDrive_Ctrl_PI_init()
// 功能:    电机驱动器PI控制参数初始化
// 输入:
// 返回:
// 备注:
//=======================================================
void MotorDrive_Ctrl_PI_init(void)
{
    // 速度控制环
    ASR_PI.Ref = 0;   // 200.0f
    ASR_PI.Kp = 1.5f; // 0.4f;             //0.6f
    ASR_PI.Kd = 0.0f;
    ASR_PI.Ki = 0.0001f; // 0.00002f;            //0.00003f
    ASR_PI.OutMax = 60;  // PI输出，最大值60
    ASR_PI.OutMin = -1.0f;

    // 输出电流压控制PI初始化//
    ACR_d_PID.Ref = 0;         // 200.0f
    ACR_d_PID.Kp = 0.5f;       // 0.75f
    ACR_d_PID.Ki = 0.0002f;    // 0.0002f
    ACR_d_PID.Kd = 0.0000001f; // Kd
    ACR_d_PID.Kd_Filter = 0.95f;
    ACR_d_PID.Fdb_last = 0.0f;
    ACR_d_PID.OutMax = 0.0f; // PI输出，最大值（上位机设定的恒流值）;
    ACR_d_PID.OutMin = 0.0f;

    ACR_q_PID.Ref = 0;         // 200.0f
    ACR_q_PID.Kp = 0.5f;       // 0.75f
    ACR_q_PID.Ki = 0.0002f;    // 0.0002f
    ACR_q_PID.Kd = 0.0000001f; // Kd
    ACR_q_PID.Kd_Filter = 0.95f;
    ACR_q_PID.Fdb_last = 0.0f;
    ACR_q_PID.OutMax = 0.0f; // PI输出，最大值（上位机设定的恒流值）;
    ACR_q_PID.OutMin = 0.0f;

    Sinoperator = 3.14f * 50.0f / mVar_RAM.EPWM_PERIOD_Base;                      // Init  Sinoperator
    oper_num = ((5000.0f / (float)mVar_RAM.EPWM_PERIOD_Base) * 10000.0f) / 50.0f; // 生成一个周期正弦波需要的中断次数
}

//=======================================================
// 函数名称:Process_SW1()
// 功能:  SW1按键处理
// 输入:
// 返回:
// 备注:使能位：输入电平0---使能，输入电平1----不使能
//=======================================================
void Process_SW1(void)
{

    SW1_STATUS = SW1_IO;
    PreCh1FB_STATUS = PreCh1FB;
    PreCh2FB_STATUS = PreCh2FB;
    if ((SW1_STATUS == 1)) //&&(SYS_RUN_RSEN==1)) //使能
    {

        Enable = 1; //
    }
    else
    {

        Enable = 0; // 不使能
    }

    // Test

    // Enable=1;
}

//=======================================================
// 函数名称:InitCANACANB(void)
// 功能:  所有CAN通信初始化
//
void InitCANACANB(void)
{
    //
    // Setup CAN to be clocked off the SYSCLKOUT
    //
    ClkCfgRegs.CLKSRCCTL2.bit.CANABCLKSEL = 0;
    ClkCfgRegs.CLKSRCCTL2.bit.CANBBCLKSEL = 0;
    // Set up the bit rate for the CAN bus.  This function sets up the CAN
    // bus timing for a nominal configuration.
    // In this example, the CAN bus is set to 500 kHz.
    //
    // Consult the data sheet for more information about
    // CAN peripheral clocking.
    //
    uint32_t status = setCANABitRate(200000000, 500000);
    status = setCANBBitRate(200000000, 500000);
    //
    // If values requested are too small or too large, catch error
    //
    if (status == 0)
    {
        errFlag++;
        ESTOP0; // Stop here and handle error
    }

    // Enable test mode and select external loopback
    //
    //    CanaRegs.CAN_CTL.bit.Test = 1;
    //    CanaRegs.CAN_TEST.bit.EXL = 1;

    // CAN-A
    //  Initialize the message object that will be used for sending CAN
    //  messages.
    //
    setupMessageObject(CANA_TX1_MSG_OBJ, CANA_Feedback1toCCU_MSG_ID, MSG_OBJ_TYPE_TRANSMIT);
    setupMessageObject(CANA_TX2_MSG_OBJ, CANA_Feedback2toCCU_MSG_ID, MSG_OBJ_TYPE_TRANSMIT);
    setupMessageObject(CANA_TX3_MSG_OBJ, CANA_Feedback3toCCU_MSG_ID, MSG_OBJ_TYPE_TRANSMIT);
    //
    // Type II
    setupMessageObjectTypeII(CANA_TX4_MSG_OBJ, CANA_Feedback4toCCU_MSG_ID, MSG_OBJ_TYPE_TRANSMIT);
    // Initialize the message object that will be used for receiving CAN
    // messages.
    //
    setupMessageObject(CANA_RX1_MSG_OBJ, CANA_CheckMotorStatus1_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    setupMessageObject(CANA_RX2_MSG_OBJ, CANA_SetMotorSpeed_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    setupMessageObject(CANA_RX3_MSG_OBJ, CANA_ResetMotorError_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    setupMessageObject(CANA_RX4_MSG_OBJ, CANA_CheckMotorStatus2_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    setupMessageObject(CANA_RX5_MSG_OBJ, CANA_SetRelayOpen_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    setupMessageObject(CANA_RX6_MSG_OBJ, CANA_CheckDrvStatus_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    //
    // Type II
    //
    setupMessageObjectTypeII(CANA_RX7_MSG_OBJ, CANA_SetASRPI_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    setupMessageObjectTypeII(CANA_RX8_MSG_OBJ, CANA_SetACR_d_PI_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    setupMessageObjectTypeII(CANA_RX9_MSG_OBJ, CANA_SetACR_q_PI_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    setupMessageObjectTypeII(CANA_RX10_MSG_OBJ, CANA_SetVbusIref_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    setupMessageObjectTypeII(CANA_RX11_MSG_OBJ, CANA_SetControlMode_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    setupMessageObjectTypeII(CANA_RX12_MSG_OBJ, CANA_DSPBOOTEN_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    // Enable the CAN for operation.

    // CAN-B
    setupCANBMessageObject(CANB_RX1_MSG_OBJ, CANB_DCtoDCStatus_MSG_ID, MSG_OBJ_TYPE_RECEIVE);
    setupCANBMessageObject(CANB_TX1_MSG_OBJ, AKCANB_DCtoDCStatus_MSG_ID, MSG_OBJ_TYPE_TRANSMIT);
    //
    CanaRegs.CAN_CTL.bit.Init = 0;
    CanbRegs.CAN_CTL.bit.Init = 0;
}
//=======================================================
// 函数名称:setCANABitRate(uint32_t sourceClock, uint32_t bitRate)
// 功能:  setCANABitRate - Set the CAN bit rate based on device clock (Hz)
//                      and desired bit rate (Hz)
// 输入:
// 返回:
// 备注:
//=======================================================

uint32_t setCANABitRate(uint32_t sourceClock, uint32_t bitRate)
{
    uint32_t desiredRatio;
    uint32_t canBits;
    uint32_t preDivide;
    uint32_t regValue;
    uint16_t canControlValue;

    //
    // Calculate the desired clock rate.
    //
    desiredRatio = sourceClock / bitRate;

    //
    // Make sure that the Desired Ratio is not too large.  This enforces the
    // requirement that the bit rate is larger than requested.
    //
    if ((sourceClock / desiredRatio) > bitRate)
    {
        desiredRatio += 1;
    }

    //
    // Check all possible values to find a matching value.
    //
    while (desiredRatio <= CAN_MAX_PRE_DIVISOR * CAN_MAX_BIT_DIVISOR)
    {
        //
        // Loop through all possible CAN bit divisors.
        //
        for (canBits = CAN_MAX_BIT_DIVISOR;
             canBits >= CAN_MIN_BIT_DIVISOR;
             canBits--)
        {
            //
            // For a given CAN bit divisor save the pre divisor.
            //
            preDivide = desiredRatio / canBits;

            //
            // If the calculated divisors match the desired clock ratio then
            // return these bit rate and set the CAN bit timing.
            //
            if ((preDivide * canBits) == desiredRatio)
            {
                //
                // Start building the bit timing value by adding the bit timing
                // in time quanta.
                //
                regValue = canBitValues[canBits - CAN_MIN_BIT_DIVISOR];

                //
                // To set the bit timing register, the controller must be
                // placed
                // in init mode (if not already), and also configuration change
                // bit enabled.  The state of the register should be saved
                // so it can be restored.
                //
                canControlValue = CanaRegs.CAN_CTL.all;
                CanaRegs.CAN_CTL.bit.Init = 1;
                CanaRegs.CAN_CTL.bit.CCE = 1;

                //
                // Now add in the pre-scalar on the bit rate.
                //
                regValue |= ((preDivide - 1) & CAN_BTR_BRP_M) |
                            (((preDivide - 1) << 10) & CAN_BTR_BRPE_M);

                //
                // Set the clock bits in the and the bits of the
                // pre-scalar.
                //
                CanaRegs.CAN_BTR.all = regValue;

                //
                // Restore the saved CAN Control register.
                //
                CanaRegs.CAN_CTL.all = canControlValue;

                //
                // Return the computed bit rate.
                //
                return (sourceClock / (preDivide * canBits));
            }
        }

        //
        // Move the divisor up one and look again.  Only in rare cases are
        // more than 2 loops required to find the value.
        //
        desiredRatio++;
    }
    return 0;
}

//=======================================================
// 函数名称:setCANBABitRate(uint32_t sourceClock, uint32_t bitRate)
// 功能:  setCANBBitRate - Set the CAN bit rate based on device clock (Hz)
//                      and desired bit rate (Hz)
// 输入:
// 返回:
// 备注:
//=======================================================

uint32_t setCANBBitRate(uint32_t sourceClock, uint32_t bitRate)
{
    uint32_t desiredRatio;
    uint32_t canBits;
    uint32_t preDivide;
    uint32_t regValue;
    uint16_t canControlValue;

    //
    // Calculate the desired clock rate.
    //
    desiredRatio = sourceClock / bitRate;

    //
    // Make sure that the Desired Ratio is not too large.  This enforces the
    // requirement that the bit rate is larger than requested.
    //
    if ((sourceClock / desiredRatio) > bitRate)
    {
        desiredRatio += 1;
    }

    //
    // Check all possible values to find a matching value.
    //
    while (desiredRatio <= CAN_MAX_PRE_DIVISOR * CAN_MAX_BIT_DIVISOR)
    {
        //
        // Loop through all possible CAN bit divisors.
        //
        for (canBits = CAN_MAX_BIT_DIVISOR;
             canBits >= CAN_MIN_BIT_DIVISOR;
             canBits--)
        {
            //
            // For a given CAN bit divisor save the pre divisor.
            //
            preDivide = desiredRatio / canBits;

            //
            // If the calculated divisors match the desired clock ratio then
            // return these bit rate and set the CAN bit timing.
            //
            if ((preDivide * canBits) == desiredRatio)
            {
                //
                // Start building the bit timing value by adding the bit timing
                // in time quanta.
                //
                regValue = canBitValues[canBits - CAN_MIN_BIT_DIVISOR];

                //
                // To set the bit timing register, the controller must be
                // placed
                // in init mode (if not already), and also configuration change
                // bit enabled.  The state of the register should be saved
                // so it can be restored.
                //
                canControlValue = CanbRegs.CAN_CTL.all;
                CanbRegs.CAN_CTL.bit.Init = 1;
                CanbRegs.CAN_CTL.bit.CCE = 1;

                //
                // Now add in the pre-scalar on the bit rate.
                //
                regValue |= ((preDivide - 1) & CAN_BTR_BRP_M) |
                            (((preDivide - 1) << 10) & CAN_BTR_BRPE_M);

                //
                // Set the clock bits in the and the bits of the
                // pre-scalar.
                //
                CanbRegs.CAN_BTR.all = regValue;

                //
                // Restore the saved CAN Control register.
                //
                CanbRegs.CAN_CTL.all = canControlValue;

                //
                // Return the computed bit rate.
                //
                return (sourceClock / (preDivide * canBits));
            }
        }

        //
        // Move the divisor up one and look again.  Only in rare cases are
        // more than 2 loops required to find the value.
        //
        desiredRatio++;
    }
    return 0;
}
//=======================================================
// 函数名称:setupMessageObject(uint32_t objID, uint32_t msgID, msgObjType msgType)
// 功能:  setupMessageObject - Setup message object as Transmit or Receive
//
// 输入:
// 返回:
// 备注:
//=======================================================
void setupMessageObject(uint32_t objID, uint32_t msgID, msgObjType msgType)
{
    //
    // Use Shadow variable for IF1CMD. IF1CMD should be written to in
    // single 32-bit write.
    //
    union CAN_IF1CMD_REG CAN_IF1CMD_SHADOW;

    //
    // Wait for busy bit to clear.
    //
    while (CanaRegs.CAN_IF1CMD.bit.Busy)
    {
    }

    //
    // Clear and Write out the registers to program the message object.
    //
    CAN_IF1CMD_SHADOW.all = 0;
    CanaRegs.CAN_IF1MSK.all = 0;
    CanaRegs.CAN_IF1ARB.all = 0;
    CanaRegs.CAN_IF1MCTL.all = 0;

    //
    // Set the Control, Mask, and Arb bit so that they get transferred to the
    // Message object.
    //
    CAN_IF1CMD_SHADOW.bit.Control = 1;
    CAN_IF1CMD_SHADOW.bit.Arb = 1;
    CAN_IF1CMD_SHADOW.bit.Mask = 1;
    CAN_IF1CMD_SHADOW.bit.DIR = 1;

    //
    // Set direction to transmit
    //
    if (msgType == MSG_OBJ_TYPE_TRANSMIT)
    {
        CanaRegs.CAN_IF1ARB.bit.Dir = 1;
        // CanaRegs.CAN_IF1MCTL.bit.UMask= 0;//不启用MASK
    }
    // else CanaRegs.CAN_IF1MCTL.bit.UMask= 1;//启用MASK
    //
    // Set Message ID (this example assumes 11 bit ID mask)
    //
    CanaRegs.CAN_IF1ARB.bit.Xtd = 1;      // Extend frame
    CanaRegs.CAN_IF1ARB.bit.ID = (msgID); //---SLu---直接设置仲裁位，MASK没有作用，采用扩展帧

    // CanaRegs.CAN_IF1MCTL.bit.UMask= 0;//启用MASK

    // msgID=msgID << CAN_MSG_ID_SHIFT;
    // CanaRegs.CAN_IF1MSK.all = msgID;//(msgID << CAN_MSG_ID_SHIFT);

    CanaRegs.CAN_IF1ARB.bit.MsgVal = 1;

    //
    // Set the data length since this is set for all transfers.  This is
    // also a single transfer and not a FIFO transfer so set EOB bit.
    //
    if (msgType == MSG_OBJ_TYPE_TRANSMIT)
    {
        CanaRegs.CAN_IF1MCTL.bit.DLC = TxmessageSize; // 发送数据8字节
    }
    else
        CanaRegs.CAN_IF1MCTL.bit.DLC = RxmessageSize; // 接收数据2字节

    CanaRegs.CAN_IF1MCTL.bit.EoB = 1;

    //
    // Transfer data to message object RAM
    //
    CAN_IF1CMD_SHADOW.bit.MSG_NUM = objID;
    CanaRegs.CAN_IF1CMD.all = CAN_IF1CMD_SHADOW.all;
}

//=======================================================
// 函数名称:setupMessageObjectTypeII(uint32_t objID, uint32_t msgID, msgObjType msgType)
// 功能:  setupMessageObject - Setup message object as Transmit or Receive
//
// 输入:
// 返回:
// 备注:
//=======================================================
void setupMessageObjectTypeII(uint32_t objID, uint32_t msgID, msgObjType msgType)
{
    //
    // Use Shadow variable for IF1CMD. IF1CMD should be written to in
    // single 32-bit write.
    //
    union CAN_IF1CMD_REG CAN_IF1CMD_SHADOW;

    //
    // Wait for busy bit to clear.
    //
    while (CanaRegs.CAN_IF1CMD.bit.Busy)
    {
    }

    //
    // Clear and Write out the registers to program the message object.
    //
    CAN_IF1CMD_SHADOW.all = 0;
    CanaRegs.CAN_IF1MSK.all = 0;
    CanaRegs.CAN_IF1ARB.all = 0;
    CanaRegs.CAN_IF1MCTL.all = 0;

    //
    // Set the Control, Mask, and Arb bit so that they get transferred to the
    // Message object.
    //
    CAN_IF1CMD_SHADOW.bit.Control = 1;
    CAN_IF1CMD_SHADOW.bit.Arb = 1;
    CAN_IF1CMD_SHADOW.bit.Mask = 1;
    CAN_IF1CMD_SHADOW.bit.DIR = 1;

    //
    // Set direction to transmit
    //
    if (msgType == MSG_OBJ_TYPE_TRANSMIT)
    {
        CanaRegs.CAN_IF1ARB.bit.Dir = 1;
        // CanaRegs.CAN_IF1MCTL.bit.UMask= 0;//不启用MASK
    }
    // else CanaRegs.CAN_IF1MCTL.bit.UMask= 1;//启用MASK
    //
    // Set Message ID (this example assumes 11 bit ID mask)
    //
    CanaRegs.CAN_IF1ARB.bit.Xtd = 1;      // Extend frame
    CanaRegs.CAN_IF1ARB.bit.ID = (msgID); //---SLu---直接设置仲裁位，MASK没有作用，采用扩展帧

    // CanaRegs.CAN_IF1MCTL.bit.UMask= 0;//启用MASK

    // msgID=msgID << CAN_MSG_ID_SHIFT;
    // CanaRegs.CAN_IF1MSK.all = msgID;//(msgID << CAN_MSG_ID_SHIFT);

    CanaRegs.CAN_IF1ARB.bit.MsgVal = 1;

    //
    // Set the data length since this is set for all transfers.  This is
    // also a single transfer and not a FIFO transfer so set EOB bit.
    //
    if (msgType == MSG_OBJ_TYPE_TRANSMIT)
    {
        CanaRegs.CAN_IF1MCTL.bit.DLC = 4; // 发送数据4字节
    }
    else
        CanaRegs.CAN_IF1MCTL.bit.DLC = 4; // 接收数据4字节

    CanaRegs.CAN_IF1MCTL.bit.EoB = 1;

    //
    // Transfer data to message object RAM
    //
    CAN_IF1CMD_SHADOW.bit.MSG_NUM = objID;
    CanaRegs.CAN_IF1CMD.all = CAN_IF1CMD_SHADOW.all;
}

//=======================================================
// 函数名称:setupCANBMessageObject(uint32_t objID, uint32_t msgID, msgObjType msgType)
// 功能:  setupCANBMessageObject - Setup message object as Transmit or Receive
//
// 输入:
// 返回:
// 备注:
//=======================================================
void setupCANBMessageObject(uint32_t objID, uint32_t msgID, msgObjType msgType)
{
    //
    // Use Shadow variable for IF1CMD. IF1CMD should be written to in
    // single 32-bit write.
    //
    union CAN_IF1CMD_REG CAN_IF1CMD_SHADOW;

    //
    // Wait for busy bit to clear.
    //
    while (CanbRegs.CAN_IF1CMD.bit.Busy)
    {
    }

    //
    // Clear and Write out the registers to program the message object.
    //
    CAN_IF1CMD_SHADOW.all = 0;
    CanbRegs.CAN_IF1MSK.all = 0;
    CanbRegs.CAN_IF1ARB.all = 0;
    CanbRegs.CAN_IF1MCTL.all = 0;

    //
    // Set the Control, Mask, and Arb bit so that they get transferred to the
    // Message object.
    //
    CAN_IF1CMD_SHADOW.bit.Control = 1;
    CAN_IF1CMD_SHADOW.bit.Arb = 1;
    CAN_IF1CMD_SHADOW.bit.Mask = 1;
    CAN_IF1CMD_SHADOW.bit.DIR = 1;

    //
    // Set direction to transmit
    //
    if (msgType == MSG_OBJ_TYPE_TRANSMIT)
    {
        CanbRegs.CAN_IF1ARB.bit.Dir = 1;
        // CanaRegs.CAN_IF1MCTL.bit.UMask= 0;//不启用MASK
    }
    // else CanaRegs.CAN_IF1MCTL.bit.UMask= 1;//启用MASK
    //
    // Set Message ID (this example assumes 11 bit ID mask)
    //
    CanbRegs.CAN_IF1ARB.bit.Xtd = 1;      // Extend frame
    CanbRegs.CAN_IF1ARB.bit.ID = (msgID); //---SLu---直接设置仲裁位，MASK没有作用，采用扩展帧

    // CanaRegs.CAN_IF1MCTL.bit.UMask= 0;//启用MASK

    // msgID=msgID << CAN_MSG_ID_SHIFT;
    // CanaRegs.CAN_IF1MSK.all = msgID;//(msgID << CAN_MSG_ID_SHIFT);

    CanbRegs.CAN_IF1ARB.bit.MsgVal = 1;

    //
    // Set the data length since this is set for all transfers.  This is
    // also a single transfer and not a FIFO transfer so set EOB bit.
    //
    if (msgType == MSG_OBJ_TYPE_TRANSMIT)
    {
        CanbRegs.CAN_IF1MCTL.bit.DLC = TxmessageSize; // 发送数据8字节
    }
    else
        CanbRegs.CAN_IF1MCTL.bit.DLC = RxmessageSize; // 接收数据2字节

    CanbRegs.CAN_IF1MCTL.bit.EoB = 1;

    //
    // Transfer data to message object RAM
    //
    CAN_IF1CMD_SHADOW.bit.MSG_NUM = objID;
    CanbRegs.CAN_IF1CMD.all = CAN_IF1CMD_SHADOW.all;
}

//=======================================================
// 函数名称:sendCANAMessage(uint32_t objID)
// 功能:  sendCANAMessage - Transmit data from the specified message object
//
// 输入:
// 返回:
// 备注:
//=======================================================
void sendCANAMessage(uint32_t objID)
{
    //
    // Use Shadow variable for IF1CMD. IF1CMD should be written to in
    // single 32-bit write.
    //
    union CAN_IF1CMD_REG CAN_IF1CMD_SHADOW;
    //
    // Wait for busy bit to clear.
    //
    while (CanaRegs.CAN_IF1CMD.bit.Busy)
    {
    }

    //
    // Write data to transfer into DATA-A and DATA-B interface registers
    //
    uint16_t index;
    for (index = 0; index < TxmessageSize; index++)
    {
        switch (index)
        {
        case 0:
            CanaRegs.CAN_IF1DATA.bit.Data_0 = CANmMsgTran.DATA0;
            break;
        case 1:
            CanaRegs.CAN_IF1DATA.bit.Data_1 = CANmMsgTran.DATA1;
            break;
        case 2:
            CanaRegs.CAN_IF1DATA.bit.Data_2 = CANmMsgTran.DATA2;
            break;
        case 3:
            CanaRegs.CAN_IF1DATA.bit.Data_3 = CANmMsgTran.DATA3;
            break;
        case 4:
            CanaRegs.CAN_IF1DATB.bit.Data_4 = CANmMsgTran.DATA4;
            break;
        case 5:
            CanaRegs.CAN_IF1DATB.bit.Data_5 = CANmMsgTran.DATA5;
            break;
        case 6:
            CanaRegs.CAN_IF1DATB.bit.Data_6 = CANmMsgTran.DATA6;
            break;
        case 7:
            CanaRegs.CAN_IF1DATB.bit.Data_7 = CANmMsgTran.DATA7;
            break;
        }
    }

    //
    // Set Direction to write and set DATA-A/DATA-B to be transfered to
    // message object
    //
    CAN_IF1CMD_SHADOW.all = 0;
    CAN_IF1CMD_SHADOW.bit.DIR = 1;
    CAN_IF1CMD_SHADOW.bit.DATA_A = 1;
    CAN_IF1CMD_SHADOW.bit.DATA_B = 1;

    //
    // Set Tx Request Bit
    //
    CAN_IF1CMD_SHADOW.bit.TXRQST = 1;

    //
    // Transfer the message object to the message object specified by
    // objID.
    //
    CAN_IF1CMD_SHADOW.bit.MSG_NUM = objID;
    CanaRegs.CAN_IF1CMD.all = CAN_IF1CMD_SHADOW.all;
}

//=======================================================
// 函数名称:sendCANBMessage(uint32_t objID)
// 功能:  sendCANAMessage - Transmit data from the specified message object
//
// 输入:
// 返回:
// 备注:
//=======================================================
void sendCANBMessage(uint32_t objID)
{
    //
    // Use Shadow variable for IF1CMD. IF1CMD should be written to in
    // single 32-bit write.
    //
    union CAN_IF1CMD_REG CAN_IF1CMD_SHADOW;

    //
    // Wait for busy bit to clear.
    //
    while (CanbRegs.CAN_IF1CMD.bit.Busy)
    {
    }

    //
    // Write data to transfer into DATA-A and DATA-B interface registers
    //
    uint16_t index;
    for (index = 0; index < TxmessageSize; index++)
    {
        switch (index)
        {
        case 0:
            CanbRegs.CAN_IF1DATA.bit.Data_0 = CANmMsgTran.DATA0;
            break;
        case 1:
            CanbRegs.CAN_IF1DATA.bit.Data_1 = CANmMsgTran.DATA1;
            break;
        case 2:
            CanbRegs.CAN_IF1DATA.bit.Data_2 = CANmMsgTran.DATA2;
            break;
        case 3:
            CanbRegs.CAN_IF1DATA.bit.Data_3 = CANmMsgTran.DATA3;
            break;
        case 4:
            CanbRegs.CAN_IF1DATB.bit.Data_4 = CANmMsgTran.DATA4;
            break;
        case 5:
            CanbRegs.CAN_IF1DATB.bit.Data_5 = CANmMsgTran.DATA5;
            break;
        case 6:
            CanbRegs.CAN_IF1DATB.bit.Data_6 = CANmMsgTran.DATA6;
            break;
        case 7:
            CanbRegs.CAN_IF1DATB.bit.Data_7 = CANmMsgTran.DATA7;
            break;
        }
    }

    //
    // Set Direction to write and set DATA-A/DATA-B to be transfered to
    // message object
    //
    CAN_IF1CMD_SHADOW.all = 0;
    CAN_IF1CMD_SHADOW.bit.DIR = 1;
    CAN_IF1CMD_SHADOW.bit.DATA_A = 1;
    CAN_IF1CMD_SHADOW.bit.DATA_B = 1;

    //
    // Set Tx Request Bit
    //
    CAN_IF1CMD_SHADOW.bit.TXRQST = 1;

    //
    // Transfer the message object to the message object specified by
    // objID.
    //
    CAN_IF1CMD_SHADOW.bit.MSG_NUM = objID;
    CanbRegs.CAN_IF1CMD.all = CAN_IF1CMD_SHADOW.all;
}

//=======================================================
// 函数名称:getCANAMessage(uint32_t objID)
// 功能:  getCANAMessage - Check the message object for new data.
//      If new data, data written into array and return true.
//                 If no new data, return false.
// 输入:
// 返回:
// 备注:
//=======================================================
bool getCANAMessage(uint32_t objID)
{
    bool status;

    //
    // Use Shadow variable for IF2CMD. IF2CMD should be written to in
    // single 32-bit write.
    //
    union CAN_IF2CMD_REG CAN_IF2CMD_SHADOW;

    //
    // Set the Message Data A, Data B, and control values to be read
    // on request for data from the message object.
    //
    CAN_IF2CMD_SHADOW.all = 0;
    CAN_IF2CMD_SHADOW.bit.Control = 1;
    CAN_IF2CMD_SHADOW.bit.DATA_A = 1;
    CAN_IF2CMD_SHADOW.bit.DATA_B = 1;

    //
    // Transfer the message object to the message object IF register.
    //
    CAN_IF2CMD_SHADOW.bit.MSG_NUM = objID;
    CanaRegs.CAN_IF2CMD.all = CAN_IF2CMD_SHADOW.all;

    //
    // Wait for busy bit to clear.
    //
    while (CanaRegs.CAN_IF2CMD.bit.Busy)
    {
    }

    //
    // See if there is new data available.
    //
    if (CanaRegs.CAN_IF2MCTL.bit.NewDat == 1)
    {
        //
        // Read out the data from the CAN registers.
        //
        uint16_t index;
        for (index = 0; index < RxmessageSize; index++)
        {
            switch (index)
            {
            case 0:
                CANmMsgRecv.DATA0 = CanaRegs.CAN_IF2DATA.bit.Data_0;
                break;
            case 1:
                CANmMsgRecv.DATA1 = CanaRegs.CAN_IF2DATA.bit.Data_1;
                break;
                //                case 2:
                //                    mMsgRecv.DATA2 = CanaRegs.CAN_IF2DATA.bit.Data_2;
                //                break;
                //                case 3:
                //                    mMsgRecv.DATA3 = CanaRegs.CAN_IF2DATA.bit.Data_3;
                //                break;
                //                case 4:
                //                    mMsgRecv.DATA4 = CanaRegs.CAN_IF2DATB.bit.Data_4;
                //                break;
                //                case 5:
                //                    mMsgRecv.DATA5 = CanaRegs.CAN_IF2DATB.bit.Data_5;
                //                break;
                //                case 6:
                //                    mMsgRecv.DATA6 = CanaRegs.CAN_IF2DATB.bit.Data_6;
                //                break;
                //                case 7:
                //                    mMsgRecv.DATA7 = CanaRegs.CAN_IF2DATB.bit.Data_7;
                //                break;
            }
        }

        //
        // Populate Shadow Variable
        //
        CAN_IF2CMD_SHADOW.all = CanaRegs.CAN_IF2CMD.all;

        //
        // Clear New Data Flag
        //
        CAN_IF2CMD_SHADOW.bit.TxRqst = 1;

        //
        // Transfer the message object to the message object IF register.
        //
        CAN_IF2CMD_SHADOW.bit.MSG_NUM = objID;
        CanaRegs.CAN_IF2CMD.all = CAN_IF2CMD_SHADOW.all;

        status = true;
    }
    else
    {
        status = false;
    }

    return (status);
}

//=======================================================
// 函数名称:getCANBMessage(uint32_t objID)
// 功能:  getCANBMessage - Check the message object for new data.
//      If new data, data written into array and return true.
//                 If no new data, return false.
// 输入:
// 返回:
// 备注:
//=======================================================
bool getCANBMessage(uint32_t objID)
{
    bool status;

    //
    // Use Shadow variable for IF2CMD. IF2CMD should be written to in
    // single 32-bit write.
    //
    union CAN_IF2CMD_REG CAN_IF2CMD_SHADOW;

    //
    // Set the Message Data A, Data B, and control values to be read
    // on request for data from the message object.
    //
    CAN_IF2CMD_SHADOW.all = 0;
    CAN_IF2CMD_SHADOW.bit.Control = 1;
    CAN_IF2CMD_SHADOW.bit.DATA_A = 1;
    CAN_IF2CMD_SHADOW.bit.DATA_B = 1;

    //
    // Transfer the message object to the message object IF register.
    //
    CAN_IF2CMD_SHADOW.bit.MSG_NUM = objID;
    CanbRegs.CAN_IF2CMD.all = CAN_IF2CMD_SHADOW.all;

    //
    // Wait for busy bit to clear.
    //
    while (CanbRegs.CAN_IF2CMD.bit.Busy)
    {
    }

    //
    // See if there is new data available.
    //
    if (CanbRegs.CAN_IF2MCTL.bit.NewDat == 1)
    {
        //
        // Read out the data from the CAN registers.
        //
        uint16_t index;
        for (index = 0; index < RxmessageSize; index++)
        {
            switch (index)
            {
            case 0:
                CANBmMsgRecv.DATA0 = CanbRegs.CAN_IF2DATA.bit.Data_0;
                break;
            case 1:
                CANBmMsgRecv.DATA1 = CanbRegs.CAN_IF2DATA.bit.Data_1;
                break;
            }
        }

        //
        // Populate Shadow Variable
        //
        CAN_IF2CMD_SHADOW.all = CanbRegs.CAN_IF2CMD.all;

        //
        // Clear New Data Flag
        //
        CAN_IF2CMD_SHADOW.bit.TxRqst = 1;

        //
        // Transfer the message object to the message object IF register.
        //
        CAN_IF2CMD_SHADOW.bit.MSG_NUM = objID;
        CanbRegs.CAN_IF2CMD.all = CAN_IF2CMD_SHADOW.all;

        status = true;
    }
    else
    {
        status = false;
    }

    return (status);
}

//=======================================================
// 函数名称:sendCANAMessageTypeII(uint32_t objID)
// 功能:  sendCANMessage - Transmit data from the specified message object
//
// 输入:
// 返回:
// 备注:
//=======================================================
void sendCANAMessageTypeII(uint32_t objID)
{
    //
    // Use Shadow variable for IF1CMD. IF1CMD should be written to in
    // single 32-bit write.
    //
    union CAN_IF1CMD_REG CAN_IF1CMD_SHADOW;

    //
    // Wait for busy bit to clear.
    //
    while (CanaRegs.CAN_IF1CMD.bit.Busy)
    {
    }

    //
    // Write data to transfer into DATA-A and DATA-B interface registers
    //
    uint16_t index;
    for (index = 0; index < 4; index++) // 发送4个字节
    {
        switch (index)
        {
        case 0:
            CanaRegs.CAN_IF1DATA.bit.Data_0 = CANmMsgTran.DATA0;
            break;
        case 1:
            CanaRegs.CAN_IF1DATA.bit.Data_1 = CANmMsgTran.DATA1;
            break;
        case 2:
            CanaRegs.CAN_IF1DATA.bit.Data_2 = CANmMsgTran.DATA2;
            break;
        case 3:
            CanaRegs.CAN_IF1DATA.bit.Data_3 = CANmMsgTran.DATA3;
            break;
            //            case 4:
            //                CanaRegs.CAN_IF1DATB.bit.Data_4 = CANmMsgTran.DATA4;
            //                break;
            //            case 5:
            //                CanaRegs.CAN_IF1DATB.bit.Data_5 = CANmMsgTran.DATA5;
            //                break;
            //            case 6:
            //                CanaRegs.CAN_IF1DATB.bit.Data_6 = CANmMsgTran.DATA6;
            //                break;
            //            case 7:
            //                CanaRegs.CAN_IF1DATB.bit.Data_7 = CANmMsgTran.DATA7;
            //                break;
        }
    }

    //
    // Set Direction to write and set DATA-A/DATA-B to be transfered to
    // message object
    //
    CAN_IF1CMD_SHADOW.all = 0;
    CAN_IF1CMD_SHADOW.bit.DIR = 1;
    CAN_IF1CMD_SHADOW.bit.DATA_A = 1;
    CAN_IF1CMD_SHADOW.bit.DATA_B = 1;

    //
    // Set Tx Request Bit
    //
    CAN_IF1CMD_SHADOW.bit.TXRQST = 1;

    //
    // Transfer the message object to the message object specified by
    // objID.
    //
    CAN_IF1CMD_SHADOW.bit.MSG_NUM = objID;
    CanaRegs.CAN_IF1CMD.all = CAN_IF1CMD_SHADOW.all;
}

//=======================================================
// 函数名称:sendCANAMessageTypeII(uint32_t objID)
// 功能:  getCANMessage - Check the message object for new data.
//      If new data, data written into array and return true.
//                 If no new data, return false.
// 输入:
// 返回:
// 备注:
//=======================================================
bool getCANAMessageTypeII(uint32_t objID)
{
    bool status;

    //
    // Use Shadow variable for IF2CMD. IF2CMD should be written to in
    // single 32-bit write.
    //
    union CAN_IF2CMD_REG CAN_IF2CMD_SHADOW;

    //
    // Set the Message Data A, Data B, and control values to be read
    // on request for data from the message object.
    //
    CAN_IF2CMD_SHADOW.all = 0;
    CAN_IF2CMD_SHADOW.bit.Control = 1;
    CAN_IF2CMD_SHADOW.bit.DATA_A = 1;
    CAN_IF2CMD_SHADOW.bit.DATA_B = 1;

    //
    // Transfer the message object to the message object IF register.
    //
    CAN_IF2CMD_SHADOW.bit.MSG_NUM = objID;
    CanaRegs.CAN_IF2CMD.all = CAN_IF2CMD_SHADOW.all;

    //
    // Wait for busy bit to clear.
    //
    while (CanaRegs.CAN_IF2CMD.bit.Busy)
    {
    }

    //
    // See if there is new data available.
    //
    if (CanaRegs.CAN_IF2MCTL.bit.NewDat == 1)
    {
        //
        // Read out the data from the CAN registers.
        //
        uint16_t index;
        for (index = 0; index < 4; index++) // 接收4个字节
        {
            switch (index)
            {
            case 0:
                CANmMsgRecv.DATA0 = CanaRegs.CAN_IF2DATA.bit.Data_0;
                break;
            case 1:
                CANmMsgRecv.DATA1 = CanaRegs.CAN_IF2DATA.bit.Data_1;
                break;
            case 2:
                CANmMsgRecv.DATA2 = CanaRegs.CAN_IF2DATA.bit.Data_2;
                break;
            case 3:
                CANmMsgRecv.DATA3 = CanaRegs.CAN_IF2DATA.bit.Data_3;
                break;
                //                case 4:
                //                    mMsgRecv.DATA4 = CanaRegs.CAN_IF2DATB.bit.Data_4;
                //                break;
                //                case 5:
                //                    mMsgRecv.DATA5 = CanaRegs.CAN_IF2DATB.bit.Data_5;
                //                break;
                //                case 6:
                //                    mMsgRecv.DATA6 = CanaRegs.CAN_IF2DATB.bit.Data_6;
                //                break;
                //                case 7:
                //                    mMsgRecv.DATA7 = CanaRegs.CAN_IF2DATB.bit.Data_7;
                //                break;
            }
        }

        //
        // Populate Shadow Variable
        //
        CAN_IF2CMD_SHADOW.all = CanaRegs.CAN_IF2CMD.all;

        //
        // Clear New Data Flag
        //
        CAN_IF2CMD_SHADOW.bit.TxRqst = 1;

        //
        // Transfer the message object to the message object IF register.
        //
        CAN_IF2CMD_SHADOW.bit.MSG_NUM = objID;
        CanaRegs.CAN_IF2CMD.all = CAN_IF2CMD_SHADOW.all;

        status = true;
    }
    else
    {
        status = false;
    }

    return (status);
}

//=======================================================
//                End of file.
//=======================================================
