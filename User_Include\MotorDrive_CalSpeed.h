/*
 * MotorDrive_150kW_CalSpeed.h
 *
 *  Created on: 2023年3月4日
 *      Author: Lusz10
 */

#ifndef USER_INCLUDE_MOTORDRIVE_CALSPEED_H_
#define USER_INCLUDE_MOTORDRIVE_CALSPEED_H_
#include "F28x_Project.h"
#ifdef _FLASH

#pragma CODE_SECTION(CalSpeed, ".TI.ramfunc");
#pragma CODE_SECTION(CalSpeed_Model_Simple,".TI.ramfunc");
#pragma CODE_SECTION(AngleDACOut, ".TI.ramfunc");


#endif



// 新数据占比系数，此数越小，噪声滤除效果越好但延迟也越大
#define CAL_SPEED_OMEGA_KP        1.0f
#define CAL_SPEED_OMEGA_KI        0.001f
#define CAL_SPEED_OMEGA_KD        0.01f

#define CAL_SPEED_MAX_OMEGA       3000.0f
// 定义PI值
#define CAL_SPEED_PI              3.1415926f


// 转速增量滤波
#define SPEED_CHANGING_FILTER_ORDER  512

typedef struct
{
    float Omega_Estimate;         // 转速观测值, rad/s
    float Omega_Estimate_Filtered;
    float Omega_Estimate_Last;
    float Omega_Estimate_Integral; // 转速观测值积分
    float Theta_Estimate;         // 角度观测值
    float Theta_Last;             // 上个周期的角度实测值
    float Theta_Virtual;          // 由实测角度差值累积得到的虚拟角度
    float Theta_diff;             // 实测两个采样周期的角度变化
    float Sample_Time;            // 用于由转速计算一个控制周期角度变化
    float Inv_Sample_Time;
    float Theta_Err;
    float Omega_Kp;
    float Omega_Ki;
    float Omega_Kd;                 // 用于改善系统的稳定性裕量
}Struct_Cal_Speed;

float CalSpeed_Model(float theta);
float CalSpeed_Model_Simple(float theta);
float CalSpeed(float *theta);
void InitCalSpeed(float sample_time);

void AngleDACOut(float theta);
void CurrentDACOut(Uint16 current);

extern Struct_Cal_Speed mCalSpeed;


#endif /* USER_INCLUDE_MOTORDRIVE_250KW_CALSPEED_H_ */
