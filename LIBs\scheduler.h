/**
 * @file scheduler.h
 * <AUTHOR> (l<PERSON><PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2022-10-24
 *
 * @copyright Copyright (c) 2022 Liu <PERSON> Personal.
 *
 */

#ifndef SCHEDULER_H
#define SCHEDULER_H
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <limits.h>
#include "mtime.h"
typedef void (*Function_t)(void *arg);

typedef struct tagFunctional
{
    Function_t fun;
    void *arg;
} Functional_t;

typedef uint32_t PeriodREC_t;

/**
 * @brief Similar to period_query_user, but the time record is stored in a variable pointed to by a uint32_t*.
 *
 * @param period_recorder Pointer to the variable that records the running time.
 * @param period The period.
 * @return true The period has elapsed.
 * @return false The period has not elapsed.
 */
bool period_query_user(PeriodREC_t *period_recorder, PeriodREC_t period);

typedef struct
{
    PeriodREC_t start; // Record the time
    bool isFinished;   // Whether it is finished
    bool isStarted;    // Whether it has started
} DelayREC_t;

/**
 * @brief Whether the system has been running for the delay time since it started.
 *
 * @param delay_recorder
 * @param delay
 * @return true The delay condition is met.
 * @return false
 */
bool delay_one_times(DelayREC_t *delay_rec, uint32_t delay);
#endif //! SCHEDULER_H
