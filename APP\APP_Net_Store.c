/**
 * @file APP_Net_Store.c
 * <AUTHOR> (l<PERSON><PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2025-08-26
 * @last modified 2025-08-26
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#include "APP_Net_Store.h"
#include "APP_Net_Regs.h"
#include "APP_Net.h"
#include "NetPacket.h"
#include "GSxRAM_Queue.h"
#include "GSxRAM_Define.h"
#include <string.h>
#include "crc.h"

#if defined(CPU2)
#include "F021_F2837xD_C28x.h"
#ifdef _FLASH
#pragma CODE_SECTION(APP_Net_Regs_CPU2_Flash_API_Init, ".TI.ramfunc");
#endif
void APP_Net_Regs_CPU2_Flash_API_Init()
{
#define CPUCLK_FREQUENCY 200 /* 200 MHz System frequency */
    Fapi_StatusType oReturnCheck;

    EALLOW;
    oReturnCheck = Fapi_initializeAPI(F021_CPU0_BASE_ADDRESS, CPUCLK_FREQUENCY);
    if (oReturnCheck != Fapi_Status_Success)
    {
        __asm("    ESTOP0");
    }

    //
    // Initialize the Flash Memory Controller (FMC) and banks for an erase or
    // program command
    //
    oReturnCheck = Fapi_setActiveFlashBank(Fapi_FlashBank0);

    //
    // Check the status of the flash API for an error
    //
    if (oReturnCheck != Fapi_Status_Success)
    {
        __asm("    ESTOP0");
    }
}

#define SYS_REGS_SAVE_FLASH_ADDR 0x0BE000
#define SYS_REGS_SAVE_FLASH_ADDR_END (SYS_REGS_SAVE_FLASH_ADDR + sizeof(SysRegs_t) - 1)

void APP_Net_Regs_Reload_From_CPU2_Flash()
{
    const SysRegs_t *src_regs = (SysRegs_t *)&g_sys_regs_for_save;
    const uint16_t *au16DataBuffer = (const uint16_t *)(src_regs);
    uint16_t crc_len = sizeof(SysRegs_t) - sizeof(src_regs->_crc);
    uint16_t crc_calc =
        CRC16_Modbus_U16_LE(au16DataBuffer, crc_len);

    if (src_regs->_crc != crc_calc)
    {
        SeizeFlashPump();
        APP_Net_Regs_CPU2_Flash_API_Init();
        APP_Net_Regs_Save_To_CPU2_Flash(CPU1_ID);
        ReleaseFlashPump();
        // Using default value
        // Assume has been synced from CPU1
        return;
    }
    else
    {
        g_cpu2_to_cpu1_info.param_load_success = true;
        // Load the saved data to cpu2's ram
        SysRegs_t *dst_regs = (SysRegs_t *)&g_cpu2_to_cpu1_info.sys_regs_for_read;
        *dst_regs = *src_regs;
    }
}

#ifdef _FLASH
#pragma CODE_SECTION(APP_Net_Regs_Save_To_CPU2_Flash, ".TI.ramfunc");
#endif
void APP_Net_Regs_Save_To_CPU2_Flash(CPU_ID_t src_cpu_id)
{
    Fapi_StatusType oReturnCheck;
    Fapi_FlashStatusType oFlashStatus;

    // Erase a Sector
    oReturnCheck = Fapi_issueAsyncCommandWithAddress(Fapi_EraseSector, (uint32 *)SYS_REGS_SAVE_FLASH_ADDR);
    // Wait until the erase operation is over
    while (Fapi_checkFsmForReady() != Fapi_Status_FsmReady)
    {
    }
    if (oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for possible errors
        __asm("    ESTOP0");
    }
    // Read FMSTAT register contents to know the status of FSM
    // after erase command to see if there are any erase operation
    // related errors
    oFlashStatus = Fapi_getFsmStatus();
    if (oFlashStatus != 0)
    {
        __asm("    ESTOP0");
    }

    // Do blank check.
    // Verify that the sector is erased.
    // oReturnCheck = Fapi_doBlankCheck((uint32 *)SYS_REGS_SAVE_FLASH_ADDR,
    //                                  Bzero_16KSector_u32length,
    //                                  &oFlashStatusWord);
    // if (oReturnCheck != Fapi_Status_Success)
    // {
    //     // Check Flash API documentation for error info
    //     __asm("    ESTOP0");
    // }

    SysRegs_t *src_regs = (SysRegs_t *)&g_cpu2_to_cpu1_info.sys_regs_for_read;
    if (src_cpu_id == CPU1_ID)
    {
        src_regs = (SysRegs_t *)&g_cpu1_to_cpu2_info.sys_regs_for_send;
    }

    src_regs->_write_cnt++;
    uint16_t *au16DataBuffer = (uint16_t *)(src_regs);
    uint16_t crc_len = sizeof(SysRegs_t) - sizeof(src_regs->_crc);
    src_regs->_crc = CRC16_Modbus_U16_LE(au16DataBuffer, crc_len);
    // uint32 *DataBuffer32 = (uint32 *)au16DataBuffer;
    for (uint32_t u32Index = SYS_REGS_SAVE_FLASH_ADDR; (u32Index <= SYS_REGS_SAVE_FLASH_ADDR_END) &&
                                                       (oReturnCheck == Fapi_Status_Success);
         u32Index += 8)
    {
        // Issue program command
        oReturnCheck = Fapi_issueProgrammingCommand((uint32 *)u32Index, au16DataBuffer, 8,
                                                    0, 0, Fapi_AutoEccGeneration);
        // Wait until the Flash program operation is over
        while (Fapi_checkFsmForReady() != Fapi_Status_FsmReady)
        {
        }

        if (oReturnCheck != Fapi_Status_Success)
        {
            // Check Flash API documentation for possible errors
            __asm("    ESTOP0");
        }
        // Read FMSTAT register contents to know the status of FSM after
        // program command to see if there are any program operation related errors
        oFlashStatus = Fapi_getFsmStatus();
        if (oFlashStatus != 0)
        {
            // Check FMSTAT and debug accordingly
            __asm("    ESTOP0");
        }

        au16DataBuffer += 8;
        // Verify the programmed values
        // oReturnCheck = Fapi_doVerify((uint32 *)u32Index, 4, DataBuffer32, &oFlashStatusWord);
        // if (oReturnCheck != Fapi_Status_Success)
        // {
        //     // Check Flash API documentation for possible errors
        //     __asm("    ESTOP0");
        // }
    }
}

void APP_Net_Regs_Save_Poll_CPU2()
{
    if (APP_Sys_Regs_HasChanged())
    {
        SeizeFlashPump();
        APP_Net_Regs_CPU2_Flash_API_Init();
        APP_Net_Regs_Save_To_CPU2_Flash(CPU1_ID);
        ReleaseFlashPump();
        APP_Sys_Regs_ChangeNoticeClear();
    }
}

void APP_Net_Notify_CPU2_Store_Regs_In_Flash()
{
    uint16_t freeSize = GSxRam_Queue_FreeSize_1_2_FD1();
    if(freeSize > 0)
    {
        GSxRam_Queue_Enqueue_1_2_FD1(0xA);
    }
}
#endif // CPU2
