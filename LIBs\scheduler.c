/**
 * @file scheduler.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2022-11-08
 *
 * @copyright Copyright (c) 2022 Liu <PERSON> Personal.
 *
 */
#include "scheduler.h"
#include "HDL_CPU_Time.h"

/**
 * @brief Get the CPU tick count
 *
 * @return uint32_t
 */
uint32_t getCPUTick()
{
    return HDL_CPU_Time_GetUsTick();
}

/**
 * @brief Similar to period_query_user, but the time record is stored in a variable pointed to by a uint32_t*.
 *
 * @param period_recorder Pointer to the variable that records the running time.
 * @param period The period, in ticks.
 * @return true The period has elapsed.
 * @return false The period has not elapsed.
 */
bool period_query_user(PeriodREC_t *period_recorder, PeriodREC_t period)
{
    bool ret = false;
    // Here it must be >=. If it were >, then with a 1 CPU tick interval, the execution would be every 2 CPU ticks.
    // A period of 0 is not allowed here, as it would lose its scheduling function.
    if ((getCPUTick() - *period_recorder) >= period)
    {
        *period_recorder = getCPUTick();
        ret = true;
    }
    return ret;
}

/**
 * @brief Whether the delay time has elapsed since this method was first called to handle the delay_rec object.
 *
 * @param delay_recorder
 * @param delay
 * @return true The delay condition is met.
 * @return false
 */
bool delay_one_times(DelayREC_t *delay_rec, uint32_t delay)
{
    if (delay_rec->isStarted == false)
    {
        delay_rec->start = getCPUTick();
        delay_rec->isStarted = true;
        delay_rec->isFinished = false;
    }
    else if (delay_rec->isFinished == false)
    {
        if ((getCPUTick() - delay_rec->start) >= delay)
        {
            delay_rec->isFinished = true;
        }
    }

    return delay_rec->isFinished;
}
