/**
 * @file crc.h
 * <AUTHOR> (l<PERSON><PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2022-11-08
 *
 * @copyright Copyright (c) 2022 Liu <PERSON> Personal.
 *
 */
#ifndef CRC_H
#define CRC_H
#include <stdint.h>
#include "ccommon.h"
// Polynomials
#define CRC16_IBM_POLYNOMIAL    0x8005UL
#define CRC16_MODBUS_POLYNOMIAL 0x8005UL
#define CRC16_CCITT_POLYNOMIAL  0x1021UL
#define CRC16_XMODEM_POLYNOMIAL 0x1021UL
#define CRC16_USB_POLYNOMIAL    0x8005UL
#define CRC32_POLYNOMIAL        0x04C11DB7UL
// Initial values
#define CRC16_IBM_INIT    0x0000UL
#define CRC16_MODBUS_INIT 0xFFFFUL
#define CRC32_INIT        0xFFFFFFFFUL
// Result XOR values
#define CRC16_IBM_XOR    0x0000UL
#define CRC16_MODBUS_X<PERSON> 0x0000UL
#define CRC32_XOR        0xFFFFFFFFUL
// Whether to reverse input data
#define CRC16_IBM_REFIN    1UL
#define CRC16_MODBUS_REFIN 1UL
#define CRC32_REFIN        1UL
// Whether to reverse output data
#define CRC16_IBM_REFOUT    1UL
#define CRC16_MODBUS_REFOUT 1UL
#define CRC32_REFOUT        1UL

uint16_t CRC16_Modbus(const byte_t *_pBuf, uint16_t _usLen);
#define CRC16_Modbus_Start() (CRC16_MODBUS_INIT)
uint16_t CRC16_Modbus_With(uint16_t crc, const byte_t *_pBuf, uint16_t _usLen);
uint16_t CRC16_Modbus_U16_LE(const uint16_t *_pBuf, uint16_t _usLen); 
uint16_t CRC16_Modbus_U16_BE(const uint16_t *_pBuf, uint16_t _usLen);
#endif // !CRC_H
