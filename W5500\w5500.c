//*****************************************************************************
//
//! \file w5500.c
//! \brief W5500 HAL Interface.
//! \version 1.0.2
//! \date 2013/10/21
//! \par  Revision history
//!       <2015/02/05> Notice
//!        The version history is not updated after this point.
//!        Download the latest version directly from GitHub. Please visit the our GitHub repository for ioLibrary.
//!        >> https://github.com/Wiznet/ioLibrary_Driver
//!       <2014/05/01> V1.0.2
//!         1. Implicit type casting -> Explicit type casting. Refer to *********
//!            Fixed the problem on porting into under 32bit MCU
//!            Issued by <PERSON>, wizwiki forum ID Think01 and bobh
//!            Thank for your interesting and serious advices.
//!       <2013/12/20> V1.0.1
//!         1. Remove warning
//!         2. WIZCHIP_READ_BUF WIZCHIP_WRITE_BUF in case _WIZCHIP_IO_MODE_SPI_FDM_
//!            for loop optimized(removed). refer to *********
//!       <2013/10/21> 1st Release
//! \author MidnightCow
//! \copyright
//!
//! Copyright (c)  2013, WIZnet Co., LTD.
//! All rights reserved.
//!
//! Redistribution and use in source and binary forms, with or without
//! modification, are permitted provided that the following conditions
//! are met:
//!
//!     * Redistributions of source code must retain the above copyright
//! notice, this list of conditions and the following disclaimer.
//!     * Redistributions in binary form must reproduce the above copyright
//! notice, this list of conditions and the following disclaimer in the
//! documentation and/or other materials provided with the distribution.
//!     * Neither the name of the <ORGANIZATION> nor the names of its
//! contributors may be used to endorse or promote products derived
//! from this software without specific prior written permission.
//!
//! THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
//! AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
//! IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
//! ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
//! LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
//! CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
//! SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
//! INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
//! CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
//! ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
//! THE POSSIBILITY OF SUCH DAMAGE.
//
//*****************************************************************************
// #include <stdio.h>
#include "w5500.h"
#include "HDL_SPI.h"
#include "F28x_Project.h"

#define _W5500_SPI_VDM_OP_ 0x00
#define _W5500_SPI_FDM_OP_LEN1_ 0x01
#define _W5500_SPI_FDM_OP_LEN2_ 0x02
#define _W5500_SPI_FDM_OP_LEN4_ 0x03

#if (_WIZCHIP_ == 5500)
////////////////////////////////////////////////////

uint16_t WIZCHIP_READ(uint32_t AddrSel)
{
   uint16_t tx_data[4];
   uint16_t rx_data[4];

   HDL_SPI_SELECT();

   AddrSel |= (_W5500_SPI_READ_ | _W5500_SPI_VDM_OP_);

   tx_data[0] = (AddrSel & 0x00FF0000) >> 16;
   tx_data[1] = (AddrSel & 0x0000FF00) >> 8;
   tx_data[2] = (AddrSel & 0x000000FF) >> 0;
   HDL_SPI_WriteRead(tx_data, rx_data, 4);

   HDL_SPI_DESELECT();

   return rx_data[3];
}

void WIZCHIP_WRITE(uint32_t AddrSel, uint16_t wb)
{
   uint16_t tx_data[4];

   HDL_SPI_SELECT();

   AddrSel |= (_W5500_SPI_WRITE_ | _W5500_SPI_VDM_OP_);

   tx_data[0] = (AddrSel & 0x00FF0000) >> 16;
   tx_data[1] = (AddrSel & 0x0000FF00) >> 8;
   tx_data[2] = (AddrSel & 0x000000FF) >> 0;
   tx_data[3] = wb;
   HDL_SPI_WriteRead(tx_data, NULL, 4);

   HDL_SPI_DESELECT();
}

void WIZCHIP_READ_BUF(uint32_t AddrSel, uint16_t *pBuf, uint16_t len)
{
   uint16_t tx_data[3];

   HDL_SPI_SELECT();

   AddrSel |= (_W5500_SPI_READ_ | _W5500_SPI_VDM_OP_);

   tx_data[0] = (AddrSel & 0x00FF0000) >> 16;
   tx_data[1] = (AddrSel & 0x0000FF00) >> 8;
   tx_data[2] = (AddrSel & 0x000000FF) >> 0;
   HDL_SPI_WriteRead(tx_data, NULL, 3);
   HDL_SPI_WriteRead(NULL, pBuf, len);
   HDL_SPI_DESELECT();
}

void WIZCHIP_WRITE_BUF(uint32_t AddrSel, uint16_t *pBuf, uint16_t len)
{
   uint16_t tx_data[3];
   HDL_SPI_SELECT();
   AddrSel |= (_W5500_SPI_WRITE_ | _W5500_SPI_VDM_OP_);

   tx_data[0] = (AddrSel & 0x00FF0000) >> 16;
   tx_data[1] = (AddrSel & 0x0000FF00) >> 8;
   tx_data[2] = (AddrSel & 0x000000FF) >> 0;
   HDL_SPI_WriteRead(tx_data, NULL, 3);
   HDL_SPI_WriteRead(pBuf, NULL, len);
   HDL_SPI_DESELECT();
}

uint16_t getSn_TX_FSR(uint16_t sn)
{
   uint16_t val = 0, val1 = 0;

   do
   {
      val1 = WIZCHIP_READ(Sn_TX_FSR(sn));
      val1 = (val1 << 8) + WIZCHIP_READ(WIZCHIP_OFFSET_INC(Sn_TX_FSR(sn), 1));
      if (val1 != 0)
      {
         val = WIZCHIP_READ(Sn_TX_FSR(sn));
         val = (val << 8) + WIZCHIP_READ(WIZCHIP_OFFSET_INC(Sn_TX_FSR(sn), 1));
      }
   } while (val != val1);
   return val;
}

uint16_t getSn_RX_RSR(uint16_t sn)
{
   uint16_t val = 0, val1 = 0;

   do
   {
      val1 = WIZCHIP_READ(Sn_RX_RSR(sn));
      val1 = (val1 << 8) + WIZCHIP_READ(WIZCHIP_OFFSET_INC(Sn_RX_RSR(sn), 1));
      if (val1 != 0)
      {
         val = WIZCHIP_READ(Sn_RX_RSR(sn));
         val = (val << 8) + WIZCHIP_READ(WIZCHIP_OFFSET_INC(Sn_RX_RSR(sn), 1));
      }
   } while (val != val1);
   return val;
}

void wiz_send_data(uint16_t sn, uint16_t *wizdata, uint16_t len)
{
   uint16_t ptr = 0;
   uint32_t addrsel = 0;

   if (len == 0)
      return;
   ptr = getSn_TX_WR(sn);
   // ********* : implict type casting -> explict type casting
   // addrsel = (ptr << 8) + (WIZCHIP_TXBUF_BLOCK(sn) << 3);
   addrsel = ((uint32_t)ptr << 8) + (WIZCHIP_TXBUF_BLOCK(sn) << 3);
   //
   WIZCHIP_WRITE_BUF(addrsel, wizdata, len);

   ptr += len;
   setSn_TX_WR(sn, ptr);
}

void wiz_send_data_ex(uint16_t sn, uint16_t *ptr, uint16_t *wizdata, uint16_t len)
{
   uint32_t addrsel = 0;

   if (len == 0)
      return;
   // ********* : implict type casting -> explict type casting
   // addrsel = (ptr << 8) + (WIZCHIP_TXBUF_BLOCK(sn) << 3);
   addrsel = ((uint32_t)ptr << 8) + (WIZCHIP_TXBUF_BLOCK(sn) << 3);
   //
   WIZCHIP_WRITE_BUF(addrsel, wizdata, len);

   *ptr += len;
   setSn_TX_WR(sn, *ptr);
}

void wiz_send_data_ex_sn0(uint16_t *ptr, uint16_t *wizdata, uint16_t len)
{
   uint32_t addrsel = 0;

   if (len == 0)
      return;
   // ********* : implict type casting -> explict type casting
   // addrsel = (ptr << 8) + (WIZCHIP_TXBUF_BLOCK(sn) << 3);
   addrsel = ((uint32_t)(*ptr) << 8) + (WIZCHIP_TXBUF_BLOCK(0) << 3);
   //
   WIZCHIP_WRITE_BUF(addrsel, wizdata, len);

   *ptr += len;
   setSn_TX_WR(0, *ptr);
}

void wiz_recv_data(uint16_t sn, uint16_t *wizdata, uint16_t len)
{
   uint16_t ptr = 0;
   uint32_t addrsel = 0;

   if (len == 0)
      return;
   ptr = getSn_RX_RD(sn);
   // ********* : implict type casting -> explict type casting
   // addrsel = ((ptr << 8) + (WIZCHIP_RXBUF_BLOCK(sn) << 3);
   addrsel = ((uint32_t)ptr << 8) + (WIZCHIP_RXBUF_BLOCK(sn) << 3);
   //
   WIZCHIP_READ_BUF(addrsel, wizdata, len);
   ptr += len;

   setSn_RX_RD(sn, ptr);
}

void wiz_recv_ignore(uint16_t sn, uint16_t len)
{
   uint16_t ptr = 0;

   ptr = getSn_RX_RD(sn);
   ptr += len;
   setSn_RX_RD(sn, ptr);
}

#include "socket.h"

// M20150401 : Typing Error
// #define SOCK_ANY_PORT_NUM  0xC000;
#define SOCK_ANY_PORT_NUM 0xC000

static uint16_t sock_any_port = SOCK_ANY_PORT_NUM;
static uint16_t sock_io_mode = 0;
static uint16_t sock_is_sending = 0;

static uint16_t sock_remained_size[_WIZCHIP_SOCK_NUM_] = {
    0,
    0,
};

// M20150601 : For extern decleation
// static uint16_t  sock_pack_info[_WIZCHIP_SOCK_NUM_] = {0,};
uint16_t sock_pack_info[_WIZCHIP_SOCK_NUM_] = {
    0,
};
//

#define CHECK_SOCKNUM()            \
   do                              \
   {                               \
      if (sn > _WIZCHIP_SOCK_NUM_) \
         return SOCKERR_SOCKNUM;   \
   } while (0);

#define CHECK_SOCKMODE(mode)             \
   do                                    \
   {                                     \
      if ((getSn_MR(sn) & 0x0F) != mode) \
         return SOCKERR_SOCKMODE;        \
   } while (0);

#define CHECK_SOCKINIT()               \
   do                                  \
   {                                   \
      if ((getSn_SR(sn) != SOCK_INIT)) \
         return SOCKERR_SOCKINIT;      \
   } while (0);

#define CHECK_SOCKDATA()         \
   do                            \
   {                             \
      if (len == 0)              \
         return SOCKERR_DATALEN; \
   } while (0);

int16_t socket(uint16_t sn, uint16_t protocol, uint16_t port, uint16_t flag)
{
   CHECK_SOCKNUM();
   switch (protocol)
   {
   case Sn_MR_TCP:
   {
      // M20150601 : Fixed the warning - taddr will never be NULL
      /*
      uint16_t taddr[4];
      getSIPR(taddr);
      */
      uint32_t taddr;
      getSIPR((uint16_t *)&taddr);
      if (taddr == 0)
         return SOCKERR_SOCKINIT;
      break;
   }
   case Sn_MR_UDP:
   case Sn_MR_MACRAW:
   case Sn_MR_IPRAW:
      break;
#if (_WIZCHIP_ < 5200)
   case Sn_MR_PPPoE:
      break;
#endif
   default:
      return SOCKERR_SOCKMODE;
   }
   // M20150601 : For SF_TCP_ALIGN & W5300
   // if((flag & 0x06) != 0) return SOCKERR_SOCKFLAG;
   if ((flag & 0x04) != 0)
      return SOCKERR_SOCKFLAG;
#if _WIZCHIP_ == 5200
   if (flag & 0x10)
      return SOCKERR_SOCKFLAG;
#endif

   if (flag != 0)
   {
      switch (protocol)
      {
      case Sn_MR_TCP:
// M20150601 :  For SF_TCP_ALIGN & W5300
#if _WIZCHIP_ == 5300
         if ((flag & (SF_TCP_NODELAY | SF_IO_NONBLOCK | SF_TCP_ALIGN)) == 0)
            return SOCKERR_SOCKFLAG;
#else
         if ((flag & (SF_TCP_NODELAY | SF_IO_NONBLOCK)) == 0)
            return SOCKERR_SOCKFLAG;
#endif

         break;
      case Sn_MR_UDP:
         if (flag & SF_IGMP_VER2)
         {
            if ((flag & SF_MULTI_ENABLE) == 0)
               return SOCKERR_SOCKFLAG;
         }
#if _WIZCHIP_ == 5500
         if (flag & SF_UNI_BLOCK)
         {
            if ((flag & SF_MULTI_ENABLE) == 0)
               return SOCKERR_SOCKFLAG;
         }
#endif
         break;
      default:
         break;
      }
   }
   close_sn(sn);
// M20150601
#if _WIZCHIP_ == 5300
   setSn_MR(sn, ((uint16_t)(protocol | (flag & 0xF0))) | (((uint16_t)(flag & 0x02)) << 7));
#else
   setSn_MR(sn, (protocol | (flag & 0xF0)));
#endif
   if (!port)
   {
      port = sock_any_port++;
      if (sock_any_port == 0xFFF0)
         sock_any_port = SOCK_ANY_PORT_NUM;
   }
   setSn_PORT(sn, port);
   setSn_CR(sn, Sn_CR_OPEN);
   while (getSn_CR(sn))
      ;
   // A20150401 : For release the previous sock_io_mode
   sock_io_mode &= ~(1 << sn);
   //
   sock_io_mode |= ((flag & SF_IO_NONBLOCK) << sn);
   sock_is_sending &= ~(1 << sn);
   sock_remained_size[sn] = 0;
   // M20150601 : repalce 0 with PACK_COMPLETED
   // sock_pack_info[sn] = 0;
   sock_pack_info[sn] = PACK_COMPLETED;
   //
//   while (getSn_SR(sn) == SOCK_CLOSED)
//      ;
   return (int16_t)sn;
}

int32_t sendto_udp(uint16_t sn, uint16_t *buf, uint16_t len)
{
   uint16_t tmp = 0;
   uint16_t freesize = 0;

   switch (getSn_MR(sn) & 0x0F)
   {
   case Sn_MR_UDP:
   case Sn_MR_MACRAW:
   case Sn_MR_IPRAW:
      break;
   default:
      return SOCKERR_SOCKMODE;
   }

   tmp = getSn_SR(sn);
   if ((tmp != SOCK_MACRAW) && (tmp != SOCK_UDP) && (tmp != SOCK_IPRAW))
      return SOCKERR_SOCKSTATUS;

   if (len > W5500_MAX_TX_BUF_SIZE)
      len = W5500_MAX_TX_BUF_SIZE; // check size not to exceed MAX size.
   while (1)
   {
      freesize = getSn_TX_FSR(sn);
      if (len > freesize)
         return SOCK_BUSY;
      if (len <= freesize)
         break;
   };
   wiz_send_data(sn, buf, len);

   setSn_CR(sn, Sn_CR_SEND);
   /* wait to process the command... */
   while (getSn_CR(sn))
      ;
   while (1)
   {
      tmp = getSn_IR(sn);
      if (tmp & Sn_IR_SENDOK)
      {
         setSn_IR(sn, Sn_IR_SENDOK);
         break;
      }
      // M:20131104
      // else if(tmp & Sn_IR_TIMEOUT) return SOCKERR_TIMEOUT;
      else if (tmp & Sn_IR_TIMEOUT)
      {
         setSn_IR(sn, Sn_IR_TIMEOUT);
         return SOCKERR_TIMEOUT;
      }
   }

   // M20150409 : Explicit Type Casting
   // return len;
   return (int32_t)len;
}

int32_t sendto_udp_ex(uint16_t sn, uint16_t *buf, uint16_t len)
{
   uint16_t freesize = 0;

   if (len > W5500_MAX_TX_BUF_SIZE)
      len = W5500_MAX_TX_BUF_SIZE; // check size not to exceed MAX size.
   while (1)
   {
      freesize = getSn_TX_FSR(sn);
      if (len > freesize)
         return SOCK_BUSY;
      if (len <= freesize)
         break;
   };
   wiz_send_data(sn, buf, len);

   setSn_CR(sn, Sn_CR_SEND);
   /* wait to process the command... */
   while (getSn_CR(sn))
      ;
   //   while (1)
   //   {
   //      tmp = getSn_IR(sn);
   //      if (tmp & Sn_IR_SENDOK)
   //      {
   //         setSn_IR(sn, Sn_IR_SENDOK);
   //         break;
   //      }
   //      // M:20131104
   //      // else if(tmp & Sn_IR_TIMEOUT) return SOCKERR_TIMEOUT;
   //      else if (tmp & Sn_IR_TIMEOUT)
   //      {
   //         setSn_IR(sn, Sn_IR_TIMEOUT);
   //         return SOCKERR_TIMEOUT;
   //      }
   //   }

   // M20150409 : Explicit Type Casting
   // return len;
   return (int32_t)len;
}

int32_t recvfrom(uint16_t sn, uint16_t *buf, uint16_t len, uint16_t *addr, uint16_t *port)
{
// M20150601 : For W5300
#if _WIZCHIP_ == 5300
   uint16_t mr;
   uint16_t mr1;
#else
   uint16_t mr;
#endif
   //
   uint16_t head[8];
   uint16_t pack_len = 0;

   CHECK_SOCKNUM();
   // CHECK_SOCKMODE(Sn_MR_UDP);
// A20150601
#if _WIZCHIP_ == 5300
   mr1 = getMR();
#endif

   switch ((mr = getSn_MR(sn)) & 0x0F)
   {
   case Sn_MR_UDP:
   case Sn_MR_IPRAW:
   case Sn_MR_MACRAW:
      break;
#if (_WIZCHIP_ < 5200)
   case Sn_MR_PPPoE:
      break;
#endif
   default:
      return SOCKERR_SOCKMODE;
   }
   CHECK_SOCKDATA();
   if (sock_remained_size[sn] == 0)
   {
      while (1)
      {
         pack_len = getSn_RX_RSR(sn);
         if (getSn_SR(sn) == SOCK_CLOSED)
            return SOCKERR_SOCKCLOSED;
         if ((sock_io_mode & (1 << sn)) && (pack_len == 0))
            return SOCK_BUSY;
         if (pack_len != 0)
            break;
      };
   }
   // D20150601 : Move it to bottom
   //  sock_pack_info[sn] = PACK_COMPLETED;
   switch (mr & 0x07)
   {
   case Sn_MR_UDP:
      if (sock_remained_size[sn] == 0)
      {
         wiz_recv_data(sn, head, 8);
         setSn_CR(sn, Sn_CR_RECV);
         while (getSn_CR(sn))
            ;
         // read peer's IP address, port number & packet length
// A20150601 : For W5300
#if _WIZCHIP_ == 5300
         if (mr1 & MR_FS)
         {
            addr[0] = head[1];
            addr[1] = head[0];
            addr[2] = head[3];
            addr[3] = head[2];
            *port = head[5];
            *port = (*port << 8) + head[4];
            sock_remained_size[sn] = head[7];
            sock_remained_size[sn] = (sock_remained_size[sn] << 8) + head[6];
         }
         else
         {
#endif
            addr[0] = head[0];
            addr[1] = head[1];
            addr[2] = head[2];
            addr[3] = head[3];
            *port = head[4];
            *port = (*port << 8) + head[5];
            sock_remained_size[sn] = head[6];
            sock_remained_size[sn] = (sock_remained_size[sn] << 8) + head[7];
#if _WIZCHIP_ == 5300
         }
#endif
         sock_pack_info[sn] = PACK_FIRST;
      }
      if (len < sock_remained_size[sn])
         pack_len = len;
      else
         pack_len = sock_remained_size[sn];
      // A20150601 : For W5300
      len = pack_len;
#if _WIZCHIP_ == 5300
      if (sock_pack_info[sn] & PACK_FIFOBYTE)
      {
         *buf++ = sock_remained_byte[sn];
         pack_len -= 1;
         sock_remained_size[sn] -= 1;
         sock_pack_info[sn] &= ~PACK_FIFOBYTE;
      }
#endif
      //
      // Need to packet length check (default 1472)
      //
      wiz_recv_data(sn, buf, pack_len); // data copy.
      break;
   case Sn_MR_MACRAW:
      if (sock_remained_size[sn] == 0)
      {
         wiz_recv_data(sn, head, 2);
         setSn_CR(sn, Sn_CR_RECV);
         while (getSn_CR(sn))
            ;
         // read peer's IP address, port number & packet length
         sock_remained_size[sn] = head[0];
         sock_remained_size[sn] = (sock_remained_size[sn] << 8) + head[1] - 2;
#if _WIZCHIP_ == W5300
         if (sock_remained_size[sn] & 0x01)
            sock_remained_size[sn] = sock_remained_size[sn] + 1 - 4;
         else
            sock_remained_size[sn] -= 4;
#endif
         if (sock_remained_size[sn] > 1514)
         {
            close_sn(sn);
            return SOCKFATAL_PACKLEN;
         }
         sock_pack_info[sn] = PACK_FIRST;
      }
      if (len < sock_remained_size[sn])
         pack_len = len;
      else
         pack_len = sock_remained_size[sn];
      wiz_recv_data(sn, buf, pack_len);
      break;
      // #if ( _WIZCHIP_ < 5200 )
   case Sn_MR_IPRAW:
      if (sock_remained_size[sn] == 0)
      {
         wiz_recv_data(sn, head, 6);
         setSn_CR(sn, Sn_CR_RECV);
         while (getSn_CR(sn))
            ;
         addr[0] = head[0];
         addr[1] = head[1];
         addr[2] = head[2];
         addr[3] = head[3];
         sock_remained_size[sn] = head[4];
         // M20150401 : For Typing Error
         // sock_remaiend_size[sn] = (sock_remained_size[sn] << 8) + head[5];
         sock_remained_size[sn] = (sock_remained_size[sn] << 8) + head[5];
         sock_pack_info[sn] = PACK_FIRST;
      }
      //
      // Need to packet length check
      //
      if (len < sock_remained_size[sn])
         pack_len = len;
      else
         pack_len = sock_remained_size[sn];
      wiz_recv_data(sn, buf, pack_len); // data copy.
      break;
      // #endif
   default:
      wiz_recv_ignore(sn, pack_len); // data copy.
      sock_remained_size[sn] = pack_len;
      break;
   }
   setSn_CR(sn, Sn_CR_RECV);
   /* wait to process the command... */
   while (getSn_CR(sn))
      ;
   sock_remained_size[sn] -= pack_len;
   // M20150601 :
   // if(sock_remained_size[sn] != 0) sock_pack_info[sn] |= 0x01;
   if (sock_remained_size[sn] != 0)
   {
      sock_pack_info[sn] |= PACK_REMAINED;
#if _WIZCHIP_ == 5300
      if (pack_len & 0x01)
         sock_pack_info[sn] |= PACK_FIFOBYTE;
#endif
   }
   else
      sock_pack_info[sn] = PACK_COMPLETED;
#if _WIZCHIP_ == 5300
   pack_len = len;
#endif
   //
   // M20150409 : Explicit Type Casting
   // return pack_len;
   return (int32_t)pack_len;
}

int16_t close_sn(uint16_t sn)
{
    CHECK_SOCKNUM();
//A20160426 : Applied the erratum 1 of W5300
#if   (_WIZCHIP_ == 5300)
   //M20160503 : Wrong socket parameter. s -> sn
   //if( ((getSn_MR(s)& 0x0F) == Sn_MR_TCP) && (getSn_TX_FSR(s) != getSn_TxMAX(s)) )
   if( ((getSn_MR(sn)& 0x0F) == Sn_MR_TCP) && (getSn_TX_FSR(sn) != getSn_TxMAX(sn)) )
   {
      uint8_t destip[4] = {0, 0, 0, 1};
      // TODO
      // You can wait for completing to sending data;
      // wait about 1 second;
      // if you have completed to send data, skip the code of erratum 1
      // ex> wait_1s();
      //     if (getSn_TX_FSR(s) == getSn_TxMAX(s)) continue;
      //
      //M20160503 : The socket() of close() calls close() itself again. It occures a infinite loop - close()->socket()->close()->socket()-> ~
      //socket(s,Sn_MR_UDP,0x3000,0);
      //sendto(s,destip,1,destip,0x3000); // send the dummy data to an unknown destination(*******).
      setSn_MR(sn,Sn_MR_UDP);
      setSn_PORTR(sn, 0x3000);
      setSn_CR(sn,Sn_CR_OPEN);
      while(getSn_CR(sn) != 0);
      while(getSn_SR(sn) != SOCK_UDP);
      sendto(sn,destip,1,destip,0x3000); // send the dummy data to an unknown destination(*******).
   };
#endif
    setSn_CR(sn,Sn_CR_CLOSE);
   /* wait to process the command... */
    while( getSn_CR(sn) );
    /* clear all interrupt of the socket. */
    setSn_IR(sn, 0xFF);
    //A20150401 : Release the sock_io_mode of socket n.
    sock_io_mode &= ~(1<<sn);
    //
    sock_is_sending &= ~(1<<sn);
    sock_remained_size[sn] = 0;
    sock_pack_info[sn] = 0;
    while(getSn_SR(sn) != SOCK_CLOSED);
    return SOCK_OK;
}
#endif
