/**
* @file APP_Net_Store.h
* <AUTHOR> (liu<PERSON><PERSON>@outlook.com)
* @brief
* @version 0.1
* @date 2025-08-26
* @last modified 2025-08-26
*
* @copyright Copyright (c) 2025 Liu <PERSON> Personal.
*
*/
#ifndef APP_NET_STORE_H
#define APP_NET_STORE_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "CPU_Define.h"
void APP_Net_Regs_CPU2_Flash_API_Init();
void APP_Net_Regs_Reload_From_CPU2_Flash();
void APP_Net_Regs_Save_To_CPU2_Flash(CPU_ID_t src_cpu_id);
void APP_Net_Regs_Save_Poll_CPU2();
void APP_Net_Notify_CPU2_Store_Regs_In_Flash();
#ifdef __cplusplus
}
#endif
#endif //!APP_NET_STORE_H
