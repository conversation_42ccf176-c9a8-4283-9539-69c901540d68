/*
 * MotorDrive_Variables.h
 *
 *  Created on: 2024年9月24日
 *      Author: lsqba
 */

#ifndef USER_INCLUDE_USER_VARIABLES_STORE_H_
#define USER_INCLUDE_USER_VARIABLES_STORE_H_

#define UID0_ADDRESS ((Uint32*)0x703CCL)
#define UID1_ADDRESS ((Uint32*)0x703CDL)

#define ENCRYPT_INIT_VALUE  19831004L

typedef struct {
    float PID_Kp;
    float PID_Ki;
    float PID_Kd;
    float PID_Kd_Filter;
}MOTOR_PID_PARAM;

typedef struct {
    MOTOR_PID_PARAM PID_Speed;
    MOTOR_PID_PARAM PID_Id;
    MOTOR_PID_PARAM PID_Iq;
}MOTOR_CONTROL_PARAM;

typedef struct {
    float Motor_Id_Max;
    float Motor_Id_Min;
    float Motor_Id_Max_Lowspeed;
    float Motor_Id_Min_Lowspeed;
    
    float Motor_Iq_Max;
    float Motor_Iq_Min;
    float Motor_Iq_Max_Lowspeed;
    float Motor_Iq_Min_Lowspeed;

    float Motor_Speed_Max;
    float Motor_Speed_Min;
    float Reserved1;
    float Reserved2;
}MOTOR_CONTROL_LIMITS;


// *_Inv should be precalculated for fast calculation of 1/parameters
typedef struct {
    float Motor_Ld;
    float Motor_Ld_Inv;
    float Motor_Lq;
    float Motor_Lq_Inv;
    float Motor_Flux;
    float Motor_Rs;
    float Motor_Pn;
    float Motor_Pn_Inv;
    float Motor_Resolver_Zero;
    float Motor_RpstoRpm_COEF;
    
    float Driver_Low_Speed_Ref;
    float Driver_Low_Speed_VBus_Min;
    float Driver_High_VBus_Threshold;
    float Driver_High_VBus_Diff;
}MOTOR_CONFIG_PARAM;

typedef struct {
    float OC_Phase_Positive;        // 相电流正保护值
    float OC_Phase_Negative;        // 相电流负保护值
    float OC_IBUS_Positive;         // 直流母线正保护值
    float OC_IBUS_Negative;         // 直流母线负保护值
    float OV_INV_BUS;               // 直流电压逆变器电容过压保护值
    float OV_Input;                 // 直流电压输入过压保护值
    float UV_INV_BUS;               // 直流电压逆变器电容欠压保护值
    float UV_Input;                 // 直流电压输入欠压保护值
    float OT_IGBT;                  // IGBT过温保护值
    float OT_IGBT_Recovery;         // IGBT过温恢复值
    float OT_MOTOR;                 // 电机过温保护值
    float OT_MOTOR_Recovery;        // 电机过温恢复值
}MOTOR_PROTECT_VALUE;

typedef struct {
    uint32_t dev_ip;
    uint32_t gw_ip;
    uint32_t pc_ip;
    uint32_t network_mask;
    uint32_t port;
    Uint16 EPWM_PERIOD_Base;        // PWM周期基准，5000对应10kHz,3500对应14.28kHz
    Uint16 EPWM_DB;                 // 死区值, 250u对应1us
    Uint32 TIMER0_PRD;              // Timer0 PRD define, 100000000ul, 定时器0周期值：500mS
    Uint32 TIMER1_PRD;              // Timer1 PRD define, 20000000ul, 定时器1周期值：100mS
    float DutyMAX;                  // 0.94f * (float)EPWM_PERIOD_Base
    float DutyMIN;                  // 0.06f * (float)EPWM_PERIOD_Base
    float Rottx_Zero_Current;
    MOTOR_CONTROL_PARAM PID_Parameters;
    MOTOR_CONTROL_LIMITS Motor_Limits;
    MOTOR_CONFIG_PARAM  Motor_Parameters;
    MOTOR_PROTECT_VALUE Motor_Protect_Values;
    Uint32 mEncrypt;        // 加密用变量，初始化为19831004L
    Uint32 mFinishFlag;     // 初始化为0xAA55AA55ul，擦除后为0xFFFFFFFFuL，若读取设置和初始化值不同，则说明上次擦除后未完成写操作，从备份位置恢复数据
    // Uint32 Reserved;        // 字节对齐预留
}USER_VAR_STORE;

extern USER_VAR_STORE mVar_RAM;
// 出厂后初始化状态
enum FIRST_INIT_ID {
    FIRST_INIT_ID_NULL = 0x0,              // 未进行任何初始化，使用UID计算加密位
    FIRST_INIT_ID_CAL,                     // 完成初始化过程，无需进行校准
    FIRST_INIT_ID_FINISH,                  // 加密验证通过，但是需要进行校准
    FIRST_INIT_ID_BADWROTE = 0xFFFFFFFFL   // 上次擦除后写操作出错
};

// Read device uid
#define GetUID0()    (*UID0_ADDRESS)
#define GetUID1()    (*UID1_ADDRESS)


// Encrypt calculation, return the calculated value
void InitUserStoreVar();
void Init_Motor_Ctrl_Variables();
Uint16 IsValidProgram();     // Return if program is valid
Uint32 EncryptCalculation();
void initFlashAPI(void);
Uint16 CompareFlashData();     // Compare InUse and Backup data, return 0 the same, return n means number of different Uint16

void loadVariableToRAM(void *pBuf_src_start);

void loadVariableToRAMDefault();
void writeVariableFlash(void *pBuf_des_start);
void saveParamToFlash();

#endif /* USER_INCLUDE_USER_VARIABLES_STORE_H_ */
