/**
 * @file GSxRAM_Sample_Point_Queue.h
 * <AUTHOR> (liu<PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2025-08-29
 * @last modified 2025-08-29
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#ifndef GSXRAM_SAMPLE_POINT_QUEUE_H
#define GSXRAM_SAMPLE_POINT_QUEUE_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>

#define ADC_SAMPLE_CH_NUM (10)
#define ADC_SAMPLE_POINT_SIZE (2 * ADC_SAMPLE_CH_NUM + 1)

    typedef struct tagADCSamplePoint_t
    {
        uint16_t type;
        float ch[ADC_SAMPLE_CH_NUM];
    } ADCSamplePoint_t;

    uint16_t GSxRam_Sampling_Point_Queue_Dequeue_1_2(ADCSamplePoint_t *dst);
    uint16_t GSxRam_Sampling_Point_Queue_Si<PERSON>_1_2();
    uint16_t GSxRam_Sampling_Point_Queue_FreeSize_1_2();
    volatile ADCSamplePoint_t *GSxRam_Queue_Sampling_Point_1_2_GetWriteNodePointer();
    void GSxRam_Queue_Sampling_Point_1_2_EnqueueWriteNodePointer();
    bool GSxRam_Sampling_Point_Queue_IsEmpty_1_2();
#ifdef __cplusplus
}
#endif
#endif //! GSXRAM_SAMPLE_POINT_QUEUE_H
