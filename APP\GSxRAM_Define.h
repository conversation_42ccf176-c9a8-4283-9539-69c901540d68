/**
 * @file GSxRAM_Define.h
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-24
 * @last modified 2025-08-24
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#ifndef GSXRAM_DEFINE_H
#define GSXRAM_DEFINE_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>
#include "APP_Net_Regs.h"
    /*
    Buffer index:
    CPU1 to CPU2:
    0 - used for the application network sample point
    CPU2 to CPU1:
    0 - used to send changed system-register addresses from CPU2 to CPU1
    1 - used to send commands from CPU2 to CPU1
    */
    typedef struct tagCPU1ToCPU2Info
    {
        int16_t buf_w_ptr[2];
        int16_t buf_r_ptr[2];
        // Using for send
        SysRegs_t sys_regs_for_send;
        bool sys_regs_occupied;
        uint32_t cpu_runing;
    } CPU1ToCPU2Info_t;

    typedef struct tagCPU2ToCPU1Info
    {
        int16_t buf_r_ptr[2];
        int16_t buf_w_ptr[2];
        // Using for receive
        SysRegs_t sys_regs_for_read;
        bool sys_regs_occupied;
        uint16_t regs_changes_queue_buf[SYS_REG_NUM];
        uint16_t commands_queue_buf[15];
        bool regs_changed; 
        bool param_load_success;
        uint32_t cpu_runing;
    } CPU2ToCPU1Info_t;

    extern volatile CPU1ToCPU2Info_t g_cpu1_to_cpu2_info;
    extern volatile CPU2ToCPU1Info_t g_cpu2_to_cpu1_info;
    extern const volatile SysRegs_t g_sys_regs_for_save;

#define APP_Sys_Regs_HasChanged() (g_cpu2_to_cpu1_info.regs_changed)
#define APP_Sys_Regs_ChangeNotice() (g_cpu2_to_cpu1_info.regs_changed = true)
#define APP_Sys_Regs_ChangeNoticeClear() (g_cpu2_to_cpu1_info.regs_changed = false)

#ifdef __cplusplus
}
#endif
#endif //! GSXRAM_DEFINE_H
