/**
 * @file BFL_W5500.h
 * <AUTHOR> (liu<PERSON><EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-12
 * @last modified 2025-08-12
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#ifndef BFL_W5500_H
#define BFL_W5500_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>
#include "w5500.h"
#include "ccommon.h"

    typedef void (*w5500_rx_cb_t)(uint16_t, const uint16_t *, uint16_t);
    /**
     * @brief W5500初始化。
     *
     */
    void BFL_W5500_Init();

    void BFL_W5500_Poll();

    uint32_t BFL_W5500_UDP_Write_Blocked(uint16_t sn, const uint16_t *data, uint16_t length);

    void BFL_W5500_UDP_Set_ReceiveCallback(w5500_rx_cb_t callback);

    void BFL_Net_set_default_config(const wiz_NetInfo *config);
    void BFL_Net_set_dest_ip(const byte_t ip[4]);
    const wiz_NetInfo *BFL_Net_get_default_config(void);
    void BFL_Net_get_dest_ip(byte_t ip[4]);
    void BFL_Net_Set_DstPort(uint16_t port);
    void BFL_Net_Set_SelfPort(uint16_t port);
#ifdef __cplusplus
}
#endif
#endif //! BFL_W5500_H
