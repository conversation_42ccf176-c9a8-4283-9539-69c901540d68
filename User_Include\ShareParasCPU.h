/** ************************************************************************
 * @copyright Copyright (C), 2021, LR Co. Ltd.
 * @file    ShareParasCPU.h
 * <AUTHOR>
 * @date    2021/10/29
 * @version v1.01
 **************************************************************************/

#ifndef SHAREPARASCPU_H_
#define SHAREPARASCPU_H_

#include "F2837xD_device.h"

typedef struct {    Uint16 CPU2_UpgradeEnable;
                    Uint16 CPU2_EntryApp_Enable;
                    Uint16 UpgradeType;
                    Uint16 FrameType;
                    Uint32 BlockLength;
                    Uint16 LedState;
                } PQC_IPC_ToCPU2;

typedef struct {    Uint16 CPU2_RunFlag;
                    Uint16 CPU2_EntryAppFlag;
                    Uint16 CPU2_App_Flag;
                    Uint16 CPU2_UpgradeStatus;
                    Uint16 CPU2_UpgradeFlag;
                } PQC_IPC_ToCPU1;


#ifdef CPU1
#define PQC_IPC_ToCPU2_DEFAULTS {           0,\
                                            0,\
                                            0,\
                                            0,\
                                            0,\
                                            0 }

#endif

#ifdef CPU2

#define PQC_IPC_ToCPU1_DEFAULTS {           0,\
                                            0,\
                                            0,\
                                            0,\
                                            0}


#endif

#endif /* SHAREPARASCPU_H_ */
/*********************************************End**********************************************/
