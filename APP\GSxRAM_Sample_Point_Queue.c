/**
 * @file GSxRAM_Sample_Point_Queue.c
 * <AUTHOR> (liu<PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2025-08-29
 * @last modified 2025-08-29
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#include "GSxRAM_Sample_Point_Queue.h"
#include "GSxRAM_Queue.h"
#include "GSxRAM_Define.h"
#include "ccommon.h"

#define APP_NET_SAMPLE_POINT_BUF_SIZE (APP_NET_SEND_BUF_SIZE / sizeof(ADCSamplePoint_t))
//----------------------------------------
#ifdef __cplusplus
#pragma DATA_SECTION("AppNetSendBuf1File")
#else
#pragma DATA_SECTION(g_app_net_buf_1_2, "AppNetSendBuf1File");
#endif
volatile ADCSamplePoint_t g_app_net_buf_1_2[APP_NET_SAMPLE_POINT_BUF_SIZE] = {0};

/**
 * @brief Get the element size currently in the queue
 *
 * @return uint16_t
 */
uint16_t GSxRam_Sampling_Point_Queue_Size_1_2()
{
    int16_t wPtr = g_cpu1_to_cpu2_info.buf_w_ptr[0];
    int16_t rPtr = g_cpu2_to_cpu1_info.buf_r_ptr[0];
    // cycle buffer
    uint16_t elementSize = (wPtr - rPtr + APP_NET_SAMPLE_POINT_BUF_SIZE) % APP_NET_SAMPLE_POINT_BUF_SIZE;
    return elementSize;
}

/**
 * @brief Check if queue is empty
 *
 * @return bool true if empty
 */
bool GSxRam_Sampling_Point_Queue_IsEmpty_1_2()
{
    return g_cpu1_to_cpu2_info.buf_w_ptr[0] == g_cpu2_to_cpu1_info.buf_r_ptr[0];
}

/**
 * @brief Get the free space in the queue
 *
 * @return uint16_t
 */
#ifdef _FLASH
#pragma CODE_SECTION(GSxRam_Sampling_Point_Queue_FreeSize_1_2, ".TI.ramfunc");
#endif
uint16_t GSxRam_Sampling_Point_Queue_FreeSize_1_2()
{
    // int16_t wPtr = g_cpu1_to_cpu2_info.buf_w_ptr[0];
    // int16_t rPtr = g_cpu2_to_cpu1_info.buf_r_ptr[0];
    // // cycle buffer
    // uint16_t freeSize = (rPtr - wPtr - 1 + APP_NET_SAMPLE_POINT_BUF_SIZE) % APP_NET_SAMPLE_POINT_BUF_SIZE;
    // return freeSize;

    register int16_t wPtr = g_cpu1_to_cpu2_info.buf_w_ptr[0];
    register int16_t rPtr = g_cpu2_to_cpu1_info.buf_r_ptr[0];
    register int16_t diff;

    // 使用寄存器变量，减少内存访问
    diff = (rPtr - wPtr - 1);

    // 利用分支预测，大多数情况下不需要加上缓冲区大小
    if (diff < 0)
    {
        diff += APP_NET_SAMPLE_POINT_BUF_SIZE;
    }

    return (uint16_t)diff;
}

/**
 * @brief Dequeue data from gsxram queue
 *
 * @return uint16_t
 */
uint16_t GSxRam_Sampling_Point_Queue_Dequeue_1_2(ADCSamplePoint_t * dst)
{
    // Please ensure that the queue is not empty before calling this function
    if(GSxRam_Sampling_Point_Queue_IsEmpty_1_2())
    {
        return 0;
    }
    *dst = g_app_net_buf_1_2[g_cpu2_to_cpu1_info.buf_r_ptr[0]];
    g_cpu2_to_cpu1_info.buf_r_ptr[0] = (g_cpu2_to_cpu1_info.buf_r_ptr[0] + 1) % APP_NET_SAMPLE_POINT_BUF_SIZE;
    return 1;
}

// /**
//  * @brief Enqueue data to gsxram queue
//  *
//  * @param _fd The buffer index
//  * @param _data uint16_t data
//  *
//  */
// #ifdef _FLASH
// #pragma CODE_SECTION(GSxRam_Queue_Enqueue_1_2, ".TI.ramfunc");
// #endif
// void GSxRam_Queue_Enqueue_1_2_FD0(const ADCSamplePointFP32_t *src)
// {
//     // Please ensure that the queue is not full before calling this function
//     register int16_t wPtr = g_cpu1_to_cpu2_info.buf_w_ptr[0];
//     register int16_t newWPtr;

//     // 直接写入数据
//     g_app_net_buf_1_2[wPtr] = *src;

//     // 递增并检查是否需要回绕
//     newWPtr = wPtr + 1;
//     if (newWPtr >= APP_NET_SAMPLE_POINT_BUF_SIZE)
//     {
//         newWPtr = 0;
//     }
//     g_cpu1_to_cpu2_info.buf_w_ptr[0] = newWPtr;
// }

#ifdef _FLASH
#pragma CODE_SECTION(GSxRam_Queue_Sampling_Point_1_2_GetWriteNodePointer, ".TI.ramfunc");
#endif
volatile ADCSamplePoint_t * GSxRam_Queue_Sampling_Point_1_2_GetWriteNodePointer()
{
    // 直接写入数据
    return (volatile ADCSamplePoint_t *)&g_app_net_buf_1_2[g_cpu1_to_cpu2_info.buf_w_ptr[0]];
}

#ifdef _FLASH
#pragma CODE_SECTION(GSxRam_Queue_Sampling_Point_1_2_EnqueueWriteNodePointer, ".TI.ramfunc");
#endif
void GSxRam_Queue_Sampling_Point_1_2_EnqueueWriteNodePointer()
{
    // g_cpu1_to_cpu2_info.buf_w_ptr[0] = 
    // (g_cpu1_to_cpu2_info.buf_w_ptr[0] + 1) % APP_NET_SAMPLE_POINT_BUF_SIZE;

    // Please ensure that the queue is not full before calling this function
    register int16_t wPtr = g_cpu1_to_cpu2_info.buf_w_ptr[0];
    register int16_t newWPtr;

    // 已经写入数据

    // 递增并检查是否需要回绕
    newWPtr = wPtr + 1;
    if (newWPtr >= APP_NET_SAMPLE_POINT_BUF_SIZE)
    {
        newWPtr = 0;
    }
    g_cpu1_to_cpu2_info.buf_w_ptr[0] = newWPtr;
}
