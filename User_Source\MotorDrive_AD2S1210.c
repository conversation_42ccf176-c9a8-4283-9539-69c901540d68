/*
 * MotorDrive_150kW_AD2S1210.c
 *
 *  Created on: 2023年5月12日
 *      Author: KUST410_2
 */

#include "MotorDrive_AD2S1210.h"

unsigned char data1, data2, data3, data4;

// #pragma DATA_SECTION(DMA_BUF_ADCC, "DMA_RAM_BUF");

//=======================================================
// 函数名称:AD2S1210Init(void)
// 功能:    初始化AD2S1210芯片
// 输入:
// 返回:
// 备注:
//=======================================================
void AD2S1210Init(void)
{
  // 旋变解码芯片初始化//
  SAMPLE_DIS; // 采样禁止

  AD2S1210SoftReset(); // 软复位
  DELAY_US(200);

  // WriteToAD2S1210(CONTROL, 0x7A);//配置为0x7A, enable hysteresis, EnRES1=0,EnRES0=0; RES1=1,RES0=0(Resolution bits= 12);
  //  WriteToAD2S1210(CONTROL, 0x7E);//配置为0x7E, enable hysteresis, EnRES1=1,EnRES0=1; RES1=1,RES0=0(Resolution bits= 12);

  WriteToAD2S1210(AD2S1210_CONTROL, 0x72);
  DELAY_US(20);
  WriteToAD2S1210(AD2S1210_EXFREQUENCY, 0x1C); // 频率0x14---5kHz;0x18---6kHz;0x1C---7kHz;0x1D---7.25kHz;0x1E---7.5kHz;0x20----8kHz;0x28---10kHz
  DELAY_US(20);
  WriteToAD2S1210(AD2S1210_LOSTHRES, 0x0); // 配置为LOS threshold:0;
  DELAY_US(20);

  WriteToAD2S1210(AD2S1210_DOSORTHRES, 0x7f); // DOS overrange threshold:0x7f;
  DELAY_US(20);
  WriteToAD2S1210(AD2S1210_DOSMISTHRES, 0x7f); // DOS mismatch threshold:0x7f;
  DELAY_US(20);
  WriteToAD2S1210(AD2S1210_DOSRSTMXTHRES, 0x01); // DOS reset max threshold:0x01;
  DELAY_US(20);
  WriteToAD2S1210(AD2S1210_DOSRSTMITHRES, 0x7E); // DOS reset min threshold:0x7e;
  DELAY_US(20);
  WriteToAD2S1210(AD2S1210_LOTHITHRES, 0x7f); // LOT high threshold:0x7f;
  DELAY_US(20);
  WriteToAD2S1210(AD2S1210_LOTLOTHRES, 0x7f); // LOT low threshold:0x7f;
  DELAY_US(2000);

  ProcessCANA(); // 读取当前的CAN 数据

  EINT;
  MCV.Set_Speed_Ref = 0; //

  while (SpicRegs.SPIFFRX.bit.RXFFST >= 1) // 判断是否接收到数据。
  {
    (void)SpicRegs.SPIRXBUF; // 读取并丢弃
  }

  StartDMACH3(); // 启动DMA发送SPI数据。

  SAMPLE_EN; // 解码器采样信号使能。（提供一个下降沿，以此更新解码器芯片中寄存器的信息）

  DELAY_US(5);
}

// Function that writes to the AD2S via the SPI port.
//--------------------------------------------------------------------------------
//=======================================================
// 函数名称:WriteToAD2S1210(unsigned char address, unsigned char data)
// 功能:    写AD2S1210寄存器
// 输入:地址，数据
// 返回:
// 备注:
//=======================================================
void WriteToAD2S1210(unsigned char address, unsigned char data)
{

  // write control register address
  //  SPISTC_ENABLE;//使能

  SPIWrite(address); // 写地址，注意地址最高为1

  // SPISTC_DISABLE;//不使能

  //    F28x_usDelay(3);//延时300ns,根据AD2S1210数据手册时序t7要求，t7最小=2 x tcx + 20，在10Mhz波特率情况下为220ns

  // write control register data
  // SPISTC_ENABLE;//使能
  // asm(" RPT #100 ||NOP");
  SPIWrite(data); // 写数据，配置数据最高位为0

  // F28x_usDelay(5);//等待数据发送

  // SPISTC_DISABLE;//不使能
}

//=======================================================
// 函数名称:unsigned char ReadPosFromAD2S1210(unsigned char address)
// 功能:   从AD2S1210读位置信息
// 输入:  读的地址
// 返回:  读回的数据
// 备注:
//=======================================================
unsigned char ReadPosFromAD2S1210(unsigned char address)
{

  static char spi_flag = 0;
  // write control register address

  //    SPISTC_ENABLE;//使能
  if (spi_flag == 0)
  {
    SPIWrite(address);     // 写地址，注意地址最高为1，此时返回旧数据
    SPIWrite(address + 1); // 写地址，配置数据最高位为1，第二次写地址，传回当前数据。
    SPIWrite(address);     // 写地址，配置数据最高位为1，第二次写地址，传回当前数据。
    spi_flag = 1;
  }

  if (SpicRegs.SPIFFRX.bit.RXFFST >= 3) // 判断是否接收到数据。
  {
    data1 = SpicRegs.SPIRXBUF; // 第一个字为旧数据
    data2 = SpicRegs.SPIRXBUF; // 取第二个字，8位
    data3 = SpicRegs.SPIRXBUF; // 第二一个字
    spi_flag = 0;
    //        data4=SpicRegs.SPIRXBUF;//取第二个字，8位
  }

  return ((data2 << 8) + data3); // 返回读取的数据
}

//=======================================================
// 函数名称:void ReadFaultandPosFromAD2S1210(unsigned char address)
// 功能:   从AD2S1210读位置信息
// 输入:  读的地址
// 返回:  读回的数据
// 备注:
//=======================================================
void ReadFaultandPosFromAD2S1210(Uint16 *Position, Uint16 *Fault)
{

  static char spix_flag = 0;
  // write control register address

  //    SPISTC_ENABLE;//使能
  if (spix_flag == 0)
  {
    SPIWrite(AD2S1210_POSITIONMSB); // 写地址，注意地址最高为1，此时返回旧数据,产生SAMPLE信号
    SPIWrite(AD2S1210_POSITIONLSB); // 写地址，此时返位置高字节
    SPIWrite(AD2S1210_FAULT);       // 写地址，传回位置低字节。
    SPIWrite(AD2S1210_POSITIONMSB); // 写地址，传回位置高字节数据。
    spix_flag = 1;
  }

  if (SpicRegs.SPIFFRX.bit.RXFFST >= 4) // 判断是否接收到数据。
  {
    data1 = SpicRegs.SPIRXBUF; // 第一个字为旧数据
    data2 = SpicRegs.SPIRXBUF; // 取位置高字节
    data3 = SpicRegs.SPIRXBUF; // 取位置低字节
    data4 = SpicRegs.SPIRXBUF; // 取故障字节
    spix_flag = 0;
    //        data4=SpicRegs.SPIRXBUF;//取第二个字，8位
  }

  (*Position) = ((data2 << 8) + data3) >> 4; // 返回读取的数据
  (*Fault) = data4;                          // 返回读取的数据
}
//=======================================================
// 函数名称:unsigned char ReadFromAD2S1210(unsigned char address)
// 功能:   从AD2S1210读一个字节信息
// 输入:  读的地址
// 返回:  读回的数据
// 备注:
//=======================================================
unsigned char ReadFromAD2S1210(unsigned char address)
{

  static char spi_flag = 0;
  // write control register address

  //    SPISTC_ENABLE;//使能
  if (spi_flag == 0)
  {
    SPIWrite(address); // 写地址，注意地址最高为1，此时返回旧数据
    SPIWrite(address); // 写地址，配置数据最高位为1，第二次写地址，传回当前数据。
    spi_flag = 1;
  }
  while (SpicRegs.SPIFFRX.bit.RXFFST < 2)
  {
  };
  if (SpicRegs.SPIFFRX.bit.RXFFST >= 2) // 判断是否接收到数据。
  {
    data1 = SpicRegs.SPIRXBUF; // 第一个字为旧数据
    data2 = SpicRegs.SPIRXBUF; // 取第二个字，8位
    spi_flag = 0;
    //        data4=SpicRegs.SPIRXBUF;//取第二个字，8位
  }

  return (data2); // 返回读取的数据
}
//=======================================================
// 函数名称:void AD2S1210ConfigMode(void)
// 功能:   配置AD2S1210为 Configuration mode
// 输入:
// 返回:
// 备注:
//=======================================================
void AD2S1210ConfigMode(void)
{

  WriteToAD2S1210(AD2S1210_CONTROL, 0x7E); // 默认配置为0x7E
}

void AD2S1210SoftReset(void)
{
  WriteToAD2S1210(AD2S1210_SOFTRESET, 0x00); // 写数据0

  F28x_usDelay(100);
}
