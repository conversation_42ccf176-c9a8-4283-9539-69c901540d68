/**
 * @file NetPacket.h
 * <AUTHOR> (liu<PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2025-08-21
 * @last modified 2025-08-21
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#ifndef NETPACKET_H
#define NETPACKET_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>
#include "ccommon.h"

#define NETPACKET_HEADER_PART_LEN 6
#define NETPACKET_CRC_PART_LEN 2
#define NETPACKET_FIX_PART_LEN (NETPACKET_HEADER_PART_LEN + NETPACKET_CRC_PART_LEN)

    struct NetPacket
    {
        /*
                0        2        4        6                      len-2        len
        ┌─Header─┼─Remaining Length─┼─Sequence Number─┼── Subpacket Content (N Byte) ──┼─CRC─┐
        55 AA   LSB MSB   LSB MSB   ··· Variable Length Data ···          LSB MSB
        */
        uint16_t header;   // header
        uint16_t len;      // current len
        uint16_t seq;      // seq
        uint16_t idx;      // write idx
        uint16_t capacity; // capacity
        uint16_t crc;      // crc
        uint16_t *data;
    };

    void NetPacket_Init(struct NetPacket *packet, byte_t *buf, uint16_t capacity);
    void NetPacket_WriteByte(struct NetPacket *packet, byte_t data);
    void NetPacket_WriteUint16_LE(struct NetPacket *packet, uint16_t data);
    void NetPacket_WriteUint32_LE(struct NetPacket *packet, uint32_t data);
    void NetPacket_WriteFloat32_LE(struct NetPacket *packet, float data);
    void NetPacket_Start(struct NetPacket *packet);
    void NetPacket_End(struct NetPacket *packet);
#define NetPacket_GetSize(__packet) ((__packet)->len)
#define NetPacket_GetData(__packet) ((__packet)->data)
    uint16_t NetPacket_GetResidualCap(struct NetPacket *packet);
#ifdef __cplusplus
}
#endif
#endif //! NETPACKET_H
