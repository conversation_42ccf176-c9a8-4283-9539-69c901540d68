/**
 * @file APP_Net.c
 * <AUTHOR> (l<PERSON><PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2025-08-21
 * @last modified 2025-08-21
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#include "APP_Net.h"
#include "BFL_DebugPin.h"
#include "BFL_W5500.h"
#include "scheduler.h"
#include "HDL_CPU_Time.h"
#include "ccommon.h"
#include <string.h>
#include <stddef.h>
#include "BFL_DebugPin.h"

#include "NetPacket.h"
#include "GSxRAM_Queue.h"
#include "GSxRAM_Define.h"
#include "APP_Net_Regs.h"
#include "crc.h"
#include "APP_Net_Store.h"
#include "APP_Net_Regs_Sync.h"
#include "cnet.h"

/**
 * @brief Initialize the registers and try to reload saved registers value
 * from flash.
 *
 */
void APP_Net_Regs_Init();
#define CPU1_RUNING_FLAG_VALUE (0x55AA50A0UL)
#define CPU2_RUNING_FLAG_VALUE (0x55AA50A0UL)

#if defined(CPU1)
void APP_Net_Poll_User_CPU1(void);
void APP_Net_Poll_CPU1(void);
#elif defined(CPU2)
static void APP_Net_Receive_Data_Handler_CPU2(uint16_t sn, const uint16_t *data, uint16_t len);
void APP_Net_Poll_CPU2(void);
#endif // CPU1
void APP_Net_Send_Response(byte_t type, uint16_t seq, const byte_t *data, uint16_t len);

byte_t net_tx_buf[512] = {0};
struct NetPacket g_tx_packet = {0};
struct NetPacket *g_p_tx_packet = NULL;
bool g_tx_packet_busy = false;
byte_t net_tx_buf1[100] = {0};
struct NetPacket g_tx_packet1 = {0};
struct NetPacket *g_p_tx_packet1 = NULL;

void APP_Net_Init()
{
    HDL_CPU_Time_Init();
    BFL_DebugPin_Init();
    g_p_tx_packet = &g_tx_packet;
    NetPacket_Init(g_p_tx_packet, net_tx_buf, sizeof(net_tx_buf));
    g_p_tx_packet1 = &g_tx_packet1;
    NetPacket_Init(g_p_tx_packet1, net_tx_buf1, sizeof(net_tx_buf1));
    APP_Net_Regs_Init();
    // Buffer init
#if defined(CPU1)
    memset(&g_cpu1_to_cpu2_info.buf_w_ptr, 0, sizeof(g_cpu1_to_cpu2_info.buf_w_ptr));
    memset(&g_cpu1_to_cpu2_info.buf_r_ptr, 0, sizeof(g_cpu1_to_cpu2_info.buf_r_ptr));
    // Sync CPU1 to CPU2 first
    APP_Net_Regs_Sync_CPU1_TO_CPU2();
    BFL_W5500_Init();
    g_cpu1_to_cpu2_info.cpu_runing = CPU1_RUNING_FLAG_VALUE;
    // Wait for CPU2 inited and get the saved registers value
#elif defined(CPU2)
    memset(&g_cpu2_to_cpu1_info.buf_w_ptr, 0, sizeof(g_cpu2_to_cpu1_info.buf_w_ptr));
    memset(&g_cpu2_to_cpu1_info.buf_r_ptr, 0, sizeof(g_cpu2_to_cpu1_info.buf_r_ptr));
    g_cpu2_to_cpu1_info.param_load_success = false;
    /*
    Ensure that if this is the first run of the program,
    CPU2 cannot load saved parameters from flash,
    and that APP_Net_Regs_Sync_CPU2_TO_CPU1 functions correctly.
    */
    APP_Net_BurstReadReg_From_CPU1(
        (SysRegs_t *)&g_cpu2_to_cpu1_info.sys_regs_for_read);
    BFL_W5500_UDP_Set_ReceiveCallback(APP_Net_Receive_Data_Handler_CPU2);
    APP_Net_Regs_Reload_From_CPU2_Flash();
    APP_Sys_Regs_ChangeNoticeClear();
    uint32_t self_ip = APP_Net_ReadReg_From_CPU2(1);
    uint32_t gw_ip = APP_Net_ReadReg_From_CPU2(2);
    uint32_t netmask = APP_Net_ReadReg_From_CPU2(3);
    uint32_t mac_low4 = APP_Net_ReadReg_From_CPU2(4);
    uint32_t dest_ip = APP_Net_ReadReg_From_CPU2(5);
    uint32_t port = APP_Net_ReadReg_From_CPU2(6);
    uint16_t self_port = port >> 16;
    uint16_t dest_port = port & 0xFFFFU;

    wiz_NetInfo net_cfg = {
        .mac = {0x00, 0x08, MAC_LOW4_UINT32_TO_INIT(mac_low4)},
        .ip = {IPV4_UINT32_TO_INIT(self_ip)},
        .sn = {IPV4_UINT32_TO_INIT(netmask)},
        .gw = {IPV4_UINT32_TO_INIT(gw_ip)},
        .dns = {IPV4_UINT32_TO_INIT(gw_ip)}};
    byte_t ip[4] = {IPV4_UINT32_TO_INIT(dest_ip)};
    BFL_Net_set_default_config(&net_cfg);
    BFL_Net_set_dest_ip(ip);
    BFL_Net_Set_DstPort(dest_port);
    BFL_Net_Set_SelfPort(self_port);
    BFL_W5500_Init();
    g_cpu2_to_cpu1_info.cpu_runing = CPU2_RUNING_FLAG_VALUE;
#endif // CPU1
}

void APP_Net_Poll(void)
{
    // BFL_W5500_Poll();

#if defined(CPU1)
    {
        // CPU1 specific polling logic
        APP_Net_Poll_CPU1();
    }
#elif defined(CPU2)
    {
        // CPU2 specific polling logic
        APP_Net_Poll_CPU2();
    }
#else
    error("CPU1 or CPU2 must be defined for APP_Net_Poll");
#endif // CPU1
}

#include <math.h>
#if defined(CPU1)
void APP_Net_Poll_CPU1(void)
{
    APP_Net_Poll_User_CPU1();
}
#endif

#if defined(CPU2)
// static uint16_t cnt = 0;
static uint32_t error_cnt = 0;
void APP_Net_Poll_CPU2(void)
{
    BFL_W5500_Poll();
    // 300ms period send all regs to server
    static PeriodREC_t rec2 = 0;
    static PeriodREC_t rec3 = 0;
    static bool regs_ready_to_send = false;
    if (period_query_user(&rec2, MS_TO_US(400)))
    {
        regs_ready_to_send = true;
    }

    if (period_query_user(&rec3, MS_TO_US(500)))
    {
        uint16_t elementSize = GSxRam_Queue_Size_1_2_FD1();
        if (elementSize > 0)
        {
            uint16_t cmd = GSxRam_Queue_Dequeue_1_2_FD1();
            if (cmd == 0xA)
            {
                APP_Sys_Regs_ChangeNotice();
            }
        }
        APP_Net_Regs_Save_Poll_CPU2();
    }

    if (NetPacket_GetSize(g_p_tx_packet) == 0)
    {
        NetPacket_Start(g_p_tx_packet);
    }

    if (regs_ready_to_send && g_tx_packet_busy == false)
    {
        regs_ready_to_send = false;
        NetPacket_Start(g_p_tx_packet);
        NetPacket_WriteByte(g_p_tx_packet, 0xF3);
        NetPacket_WriteUint16_LE(g_p_tx_packet, SYS_REG_NUM);
        for (int i = 0; i < SYS_REG_NUM; i++)
        {
            uint32_t reg_value = APP_Net_ReadReg_From_CPU1(i);
            NetPacket_WriteUint32_LE(g_p_tx_packet, reg_value);
        }
        NetPacket_End(g_p_tx_packet);
        // Send
        BFL_W5500_UDP_Write_Blocked(
            0,
            (uint16_t *)NetPacket_GetData(g_p_tx_packet),
            NetPacket_GetSize(g_p_tx_packet));
        NetPacket_Start(g_p_tx_packet);
    }

    // cycle buffer
    uint16_t elementSize = GSxRam_Sampling_Point_Queue_Size_1_2();

    if (elementSize == 0)
    {
        return; // buffer empty
    }

    for (uint16_t i = 0; i < elementSize; i++)
    {
        if (NetPacket_GetSize(g_p_tx_packet) > 200)
        {
            NetPacket_End(g_p_tx_packet);
            // Send
            BFL_W5500_UDP_Write_Blocked(
                0,
                (uint16_t *)NetPacket_GetData(g_p_tx_packet),
                NetPacket_GetSize(g_p_tx_packet));
            NetPacket_Start(g_p_tx_packet);
            g_tx_packet_busy = false;
        }
        else
        {
            ADCSamplePoint_t point;
            if (GSxRam_Sampling_Point_Queue_Dequeue_1_2(&point) == 0)
            {
                error_cnt++;
                break;
            }
            NetPacket_WriteByte(g_p_tx_packet, point.type);
            uint16_t u16Var = 0;
            uint32_t u32Var = 0;
            if (point.type == 0xA1)
            {
                // if(cnt != point.ch[0])
                // {
                //     error_cnt++;
                // }
                // cnt = point.ch[0] + 1;
                for (int j = 0; j < ADC_SAMPLE_CH_NUM; j++)
                {
                    u16Var = point.ch[j];
                    NetPacket_WriteUint16_LE(g_p_tx_packet, u16Var);
                }
            }
            else if (point.type == 0xA2)
            {
                for (int j = 0; j < ADC_SAMPLE_CH_NUM; j++)
                {
                    NetPacket_WriteFloat32_LE(g_p_tx_packet, point.ch[j]);
                }
            }
            else if (point.type == 0xA3)
            {
                for (int j = 0; j < ADC_SAMPLE_CH_NUM; j++)
                {
                    u32Var = (int32_t)(point.ch[j] * 100000UL);
                    NetPacket_WriteUint32_LE(g_p_tx_packet, u32Var);
                }
            }
            g_tx_packet_busy = true;
        }
    }
}

static void APP_Net_Receive_Data_Handler_CPU2(uint16_t sn, const uint16_t *data, uint16_t len)
{
    if (len < 8)
    {
        return;
    }

    byte_t srcIp[4] = {0};
    uint16_t srcPort = 0;
    uint16_t dataLen = 0;
    uint16_t currRevLen = len - 8;

    for (int i = 0; i < 4; i++)
    {
        srcIp[i] = data[i];
    }

    srcPort = (data[4] << 8) | data[5];
    dataLen = (data[6] << 8) | data[7];
    dataLen = dataLen > currRevLen ? currRevLen : dataLen;

    const byte_t *pData = &data[8];
    uint16_t header = pData[0] | pData[1] << 8;
    uint16_t remaining_len = pData[2] | pData[3] << 8;
    uint16_t seq = pData[4] | pData[5] << 8;

    if (header != 0x55AA)
    {
        return;
    }

    if (remaining_len + NETPACKET_HEADER_PART_LEN != dataLen)
    {
        return;
    }

    // Check CRC
    // uint16_t crc = (pData[dataLen - 1] << 8) | pData[dataLen - 2];
    pData = pData + NETPACKET_HEADER_PART_LEN;
    uint16_t crc_cal = CRC16_Modbus((byte_t *)pData, remaining_len);
    if (crc_cal != 0)
    {
        return;
    }

    do
    {
        /*
         (0x10) Write multiple holding registers,
         similar to Modbus-RTU protocol without address code and CRC
        */
        byte_t funcode = pData[0];
        uint16_t regAddrStart = (pData[1]) | pData[2] << 8;
        uint16_t regNum = (pData[3]) | pData[4] << 8;
        const byte_t *pRegData = &pData[5];
        if (funcode != 0x10)
        {
            break;
        }

        if (regNum == 0)
        {
            break;
        }

        uint16_t regAddrEnd = regAddrStart + regNum - 1;
        if (regAddrEnd > SYS_REG_ADDR_END)
        {
            // Limit the end range
            regAddrEnd = SYS_REG_ADDR_END;
        }

        for (int i = 0; i < regNum; i++)
        {
            uint32_t reg_value = (((uint32_t)pRegData[i * 4 + 3] << 24) & 0xFF000000) |
                                 (((uint32_t)pRegData[i * 4 + 2] << 16) & 0xFF0000) |
                                 (((uint32_t)pRegData[i * 4 + 1] << 8) & 0xFF00) |
                                 (((uint32_t)pRegData[i * 4 + 0] << 0) & 0xFF);
            APP_Net_WriteReg(regAddrStart + i, reg_value);
        }

        // Perform the write operation
        uint16_t regWroteNum = regAddrStart - regAddrEnd + 1;
        byte_t resp[4] = {
            regAddrStart & 0xFF, regAddrStart >> 8, // start addr (LE)
            regWroteNum & 0xFF, regWroteNum >> 8    // register count (LE)
        };
        APP_Net_Send_Response(0x10, seq, resp, 4);
    } while (0);
}

void APP_Net_Send_Response(byte_t type, uint16_t seq, const byte_t *data, uint16_t len)
{
    g_p_tx_packet1->seq = seq;
    NetPacket_Start(g_p_tx_packet1);
    NetPacket_WriteByte(g_p_tx_packet1, type);
    for (int i = 0; i < len; i++)
    {
        NetPacket_WriteByte(g_p_tx_packet1, data[i]);
    }
    NetPacket_End(g_p_tx_packet1);
    // Send
    BFL_W5500_UDP_Write_Blocked(
        0,
        (uint16_t *)NetPacket_GetData(g_p_tx_packet1),
        NetPacket_GetSize(g_p_tx_packet1));
}
#endif // CPU2

bool APP_Net_Is_Param_Load_Success()
{
    return g_cpu2_to_cpu1_info.param_load_success;
}

bool APP_IsCPU1_Runing()
{
    return g_cpu1_to_cpu2_info.cpu_runing == CPU1_RUNING_FLAG_VALUE;
}

bool APP_IsCPU2_Runing()
{
    return g_cpu2_to_cpu1_info.cpu_runing == CPU2_RUNING_FLAG_VALUE;
}

void APP_Net_Wait_CPU2_Init(uint32_t timeout_us)
{
    uint32_t start_us = HDL_CPU_Time_GetUsTick();
    while (true)
    {
        if (APP_IsCPU2_Runing())
        {
            break;
        }
        if (HDL_CPU_Time_GetUsTick() - start_us >= timeout_us)
        {
            break;
        }
    }
}
