/**
 * @file main.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-24
 * @last modified 2025-08-24
 *
 * @copyright Copyright (c) 2025 Liu <PERSON>lin Personal.
 *
 */
//====================== Include ========================//
#include "CPU_Define.h"
#include "BFL_DebugPin.h"
#include "scheduler.h"
#include "HDL_CPU_Time.h"
#include "APP_Net.h"

//====================== Defines ========================//

//====================Function Declaration==================//

//====================Variable Definition====================//

void main(void)
{
    // Step 1. Initialize System Control:
    InitSysCtrl(); // PLL, WatchDog, enable Peripheral Clocks

    // Keep CPU2 in reset
    EALLOW;
    DevCfgRegs.CPU2RESCTL.bit.RESET = 1;
    EDIS;

    // Step 2. Initialize GPIO:
    InitGpio();

    //
    // Step 3. Initialize PIE vector table:
    // Disable and clear all CPU interrupts
    //
    DINT;
    IER = 0x0000;
    IFR = 0x0000;

    //
    // Initialize PIE control registers to their default state:
    // This function is found in the F2837xD_PieCtrl.c file.
    //
    InitPieCtrl();

    //
    // Initialize the PIE vector table with pointers to the shell Interrupt
    // Service Routines (ISR).
    // This will populate the entire table, even if the interrupt
    // is not used in this example.  This is useful for debug purposes.
    // The shell ISR routines are found in F2837xD_DefaultIsr.c.
    // This function is found in F2837xD_PieVect.c.
    //
    InitPieVectTable();

    //
    // Interrupts that are used in this example are re-mapped to
    // ISR functions found within this file.
    //
    EALLOW; // This is needed to write to EALLOW protected registers
    //    PieVectTable.EPWM1_INT   = &Epwm1_Isr;
    //    PieVectTable.ADCA1_INT    = &Adca1_Isr;
    //    PieVectTable.TIMER0_INT   = &Cpu_Timer0_Isr;
    //    PieVectTable.SCIB_RX_INT  = &Scib_Rxfifo_Isr;
    //    PieVectTable.SCID_RX_INT  = &Scid_Rxfifo_Isr;
    EDIS; // This is needed to disable write to EALLOW protected registers

    EnableInterrupts();

    // Step 4. Initialize all the Device Peripherals:

    //
    // Ensure DMA is connected to Peripheral Frame 2 bridge (EALLOW protected)
    //
    EALLOW;
    CpuSysRegs.SECMSEL.bit.PF2SEL = 1;
    EDIS;

    APP_Net_Init(); // Initialize network settings

    EALLOW;
    DevCfgRegs.CPU2RESCTL.bit.RESET = 0;
    EDIS;
    BFL_DebugPin_Set(DEBUG_PIN_1);
    HDL_CPU_Time_DelayUs(300000);
    BFL_DebugPin_Toggle(DEBUG_PIN_1);
    HDL_CPU_Time_DelayUs(300000);
    BFL_DebugPin_Toggle(DEBUG_PIN_1);
    HDL_CPU_Time_DelayUs(300000);
    BFL_DebugPin_Set(DEBUG_PIN_1);
    // Wait for CPU2 to start up
// #ifdef _STANDALONE
#ifdef _FLASH
    //    Send boot command to allow the CPU02 application to begin execution
    IPCBootCPU2(C1C2_BROM_BOOTMODE_BOOT_FROM_FLASH);
#else
    //    Send boot command to allow the CPU02 application to begin execution
    IPCBootCPU2(C1C2_BROM_BOOTMODE_BOOT_FROM_RAM);
// #endif //_STANDALONE
#endif //_FLASH
    /*
     CPU2 startup ready,
     the registers have been reload from cpu2 flash to cpu2 ram,
     here sync the registers from cpu2 ram to cpu1 ram
     */
    APP_Net_Regs_Sync_CPU2_TO_CPU1();

    PeriodREC_t rec1 = 0;
    while (1)
    {
        // Poll the network for incoming data
        APP_Net_Poll();
        if (period_query_user(&rec1, MS_TO_US(1000)))
        {
            BFL_DebugPin_Toggle(DEBUG_PIN_1);
        }
    }
}

//=======================================================
//                End of file.
//=======================================================
