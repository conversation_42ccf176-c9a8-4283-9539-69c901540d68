/**
 * @file APP_Net_Regs.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-24
 * @last modified 2025-08-24
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#include "APP_Net_Regs.h"
#include "APP_Net.h"
#include "NetPacket.h"
#include "GSxRAM_Queue.h"
#include "GSxRAM_Define.h"
#include <string.h>

/*
The read and write functions is looks like operator
a same memory, but the implementation is using
tow separate memory for CPU1 and CPU2.
*/

void APP_Net_Regs_Init()
{
    // Reload g_sys_regs_for_save from flash

    // Initialize sys_regs to 0
#if defined(CPU1)
    memset((void *)&g_cpu1_to_cpu2_info.sys_regs_for_send, 0, sizeof(g_cpu1_to_cpu2_info.sys_regs_for_send));
    g_cpu1_to_cpu2_info.sys_regs_for_send._crc = 0;
    g_cpu1_to_cpu2_info.sys_regs_for_send._write_cnt = 0;
    g_cpu1_to_cpu2_info.sys_regs_occupied = false;
#elif defined(CPU2)
    memset((void *)&g_cpu2_to_cpu1_info.sys_regs_for_read, 0, sizeof(g_cpu2_to_cpu1_info.sys_regs_for_read));
    g_cpu2_to_cpu1_info.sys_regs_for_read._crc = 0;
    g_cpu2_to_cpu1_info.sys_regs_for_read._write_cnt = 0;
    g_cpu2_to_cpu1_info.sys_regs_occupied = false;
#endif // CPU1
}

bool APP_Net_WriteReg(uint16_t regAddr, uint32_t reg_value)
{
    // Copy data to register
    if (regAddr >= SYS_REG_NUM)
    {
        return false; // register address out of range
    }

#if defined(CPU1)
    g_cpu1_to_cpu2_info.sys_regs_occupied = true;
    g_cpu1_to_cpu2_info.sys_regs_for_send.reg[regAddr] = reg_value;
    g_cpu1_to_cpu2_info.sys_regs_occupied = false;
#elif defined(CPU2)
    g_cpu2_to_cpu1_info.sys_regs_occupied = true;
#if SYS_CFG_REG_ADDR_BASE == 0
    if (regAddr <= SYS_CFG_REG_ADDR_END)
#else
    if (regAddr >= SYS_CFG_REG_ADDR_BASE && regAddr <= SYS_CFG_REG_ADDR_END)
#endif
    {
        g_cpu2_to_cpu1_info.sys_regs_for_read.reg[regAddr] = reg_value;
        if (GSxRam_Queue_FreeSize_2_1(0) > 0)
        {
            GSxRam_Queue_Enqueue_2_1(0, regAddr);
        }
    }
    else if (regAddr >= SYS_CMD_REG_ADDR_BASE && regAddr <= SYS_CMD_REG_ADDR_END)
    {
        g_cpu2_to_cpu1_info.sys_regs_for_read.reg[regAddr] = reg_value;
        if (GSxRam_Queue_FreeSize_2_1(1) >= 3)
        {
            GSxRam_Queue_Enqueue_2_1(1, regAddr);
            GSxRam_Queue_Enqueue_2_1(1, reg_value & 0xFFFFU);
            GSxRam_Queue_Enqueue_2_1(1, (reg_value >> 16) & 0xFFFFU);
        }
    }
    g_cpu2_to_cpu1_info.sys_regs_occupied = false;
#endif
    return true;
}

#if defined(CPU2)
bool APP_Net_Reg_IsDifferent(uint16_t regAddr, uint32_t reg_value)
{
    bool ret = false;
    // Copy data to register
    if (regAddr >= SYS_REG_NUM)
    {
        return ret; // register address out of range
    }

    g_cpu2_to_cpu1_info.sys_regs_occupied = true;
    ret = g_sys_regs_for_save.reg[regAddr] != reg_value;
    g_cpu2_to_cpu1_info.sys_regs_occupied = false;
    return ret;
}
#endif

bool APP_Net_BurstWriteReg(const SysRegs_t *src_regs)
{
#if defined(CPU1)
    g_cpu1_to_cpu2_info.sys_regs_occupied = true;
    memcpy((void *)&g_cpu1_to_cpu2_info.sys_regs_for_send.reg[0], &src_regs->reg[0], sizeof(SysRegs_t));
    g_cpu1_to_cpu2_info.sys_regs_occupied = false;
#elif defined(CPU2)
    g_cpu2_to_cpu1_info.sys_regs_occupied = true;
    memcpy((void *)&g_cpu2_to_cpu1_info.sys_regs_for_read.reg[0], &src_regs->reg[0], sizeof(SysRegs_t));
    g_cpu2_to_cpu1_info.sys_regs_occupied = false;
#endif
    return true;
}

uint32_t APP_Net_ReadReg_From_CPU2(uint16_t regAddr)
{
    if (regAddr >= SYS_REG_NUM)
    {
        return 0; // register address out of range
    }

    g_cpu2_to_cpu1_info.sys_regs_occupied = true;
    uint32_t reg_value = g_cpu2_to_cpu1_info.sys_regs_for_read.reg[regAddr];
    g_cpu2_to_cpu1_info.sys_regs_occupied = false;
    return reg_value;
}

uint32_t APP_Net_ReadReg_From_CPU1(uint16_t regAddr)
{
    if (regAddr >= SYS_REG_NUM)
    {
        return 0; // register address out of range
    }

    g_cpu1_to_cpu2_info.sys_regs_occupied = true;
    uint32_t reg_value = g_cpu1_to_cpu2_info.sys_regs_for_send.reg[regAddr];
    g_cpu1_to_cpu2_info.sys_regs_occupied = false;
    return reg_value;
}

bool APP_Net_BurstReadReg_From_CPU1(SysRegs_t *dst_regs)
{
    g_cpu1_to_cpu2_info.sys_regs_occupied = true;
    memcpy(&dst_regs->reg[0], (void *)&g_cpu1_to_cpu2_info.sys_regs_for_send.reg[0], sizeof(SysRegs_t));
    g_cpu1_to_cpu2_info.sys_regs_occupied = false;
    return true;
}

bool APP_Net_BurstReadReg_From_CPU2(SysRegs_t *dst_regs)
{
    g_cpu2_to_cpu1_info.sys_regs_occupied = true;
    memcpy(&dst_regs->reg[0], (void *)&g_cpu2_to_cpu1_info.sys_regs_for_read.reg[0], sizeof(SysRegs_t));
    g_cpu2_to_cpu1_info.sys_regs_occupied = false;
    return true;
}

bool APP_Net_IsRegsOccupied()
{
#if defined(CPU1)
    return g_cpu1_to_cpu2_info.sys_regs_occupied;
#elif defined(CPU2)
    return g_cpu2_to_cpu1_info.sys_regs_occupied;
#endif
}
