//=======================================================
//    Project：         MMC_PET_CPU1
//    File:                 MMC_PET_Init.c
//    Created on:     2021
//    By：                GCY
//    Describe：       初始化函数
//=======================================================
//====================== Include ========================//
#include "MotorDrive_Init.h"

//====================== Defines ========================//


//====================Variable Definition=====================//



//=======================================================
//函数名称: InitPeripheral()
//功能:   DSP外设初始化函数
//输入:
//返回:
//备注:
//=======================================================
void InitPeripheral(void)
{
    Init_epwm1();
    Init_epwm2();
    Init_epwm3();
    Init_epwm4();
    Init_epwm7();
    InitCAN_A();
    InitCAN_B();
    Init_ADC();

    Init_My_DMA();

    Init_scia_fifo();//SCI接口
    Init_scib_fifo();
    Init_scic_fifo();
    Init_scid_fifo();

    Init_spic_fifo();//SPI接口
}

//=======================================================
//函数名称:Init_epwm1(void)
//功能:  初始化EPWM1为10kHz触发SOCA和INT1
//输入:
//返回:
//备注:
//=======================================================
void Init_epwm1(void)
{
    EALLOW;
    EPwm1Regs.TZCTL.bit.TZA = TZ_FORCE_LO;
    EPwm1Regs.TZCTL.bit.TZB = TZ_FORCE_LO;
    EPwm1Regs.TZSEL.bit.OSHT1 = 1;   //All_Fault--to do

    EDIS;
    PWMSoftOutputOff();


    EALLOW;
    CpuSysRegs.PCLKCR0.bit.TBCLKSYNC = 0;             // Disable TBCLK within the EPWM

    EPwm1Regs.TBCTL.bit.CTRMODE = TB_COUNT_UPDOWN;    // Count up and down
    EPwm1Regs.TBCTL.bit.HSPCLKDIV = TB_DIV1;          // TBCLK = EPWMCLK /（HSPCLKDIV× CLKDIV）
    EPwm1Regs.TBCTL.bit.CLKDIV = TB_DIV1;             // 设置Epwm1  TBCLK = 200M/1=100MHz
    EPwm1Regs.TBCTL.bit.PHSEN = TB_DISABLE;           // Disable phase loading
    EPwm1Regs.TBCTL.bit.PRDLD = TB_SHADOW;            // TBPRD is loaded from its shadow register

    EPwm1Regs.TBPRD = mVar_RAM.EPWM_PERIOD_Base;      // Set timer period (100M/(2*5000)=1k)---控制频率
    EPwm1Regs.TBPHS.bit.TBPHS = 0x0000;               // Phase is 0
    EPwm1Regs.TBCTR = 0x0000;                         // Clear counter
    EPwm1Regs.TBCTL.bit.SYNCOSEL = TB_CTR_ZERO;       // Output Sync signal

    EPwm1Regs.CMPCTL.bit.SHDWAMODE = CC_SHADOW;      // Load registers every ZERO
    EPwm1Regs.CMPCTL.bit.SHDWBMODE = CC_SHADOW;
    EPwm1Regs.CMPCTL.bit.LOADAMODE = CC_CTR_PRD;
    EPwm1Regs.CMPCTL.bit.LOADBMODE = CC_CTR_PRD;

    // Setup compare
    EPwm1Regs.CMPA.bit.CMPA = 0;

    // Set actions
    EPwm1Regs.AQCTLA.bit.CAU = AQ_SET;             //正三角形计数
    EPwm1Regs.AQCTLA.bit.CAD = AQ_CLEAR;
    EPwm1Regs.AQCTLB.bit.CAU = AQ_SET;          // Set PWM1A on Zero
    EPwm1Regs.AQCTLB.bit.CAD = AQ_CLEAR;

    // Active Low PWMs - Setup Deadband
    EPwm1Regs.DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
    EPwm1Regs.DBCTL.bit.POLSEL = DB_ACTV_HIC;
    EPwm1Regs.DBCTL.bit.IN_MODE = DBA_ALL;
    EPwm1Regs.DBRED.bit.DBRED = mVar_RAM.EPWM_DB;
    EPwm1Regs.DBFED.bit.DBFED = mVar_RAM.EPWM_DB;

    EPwm1Regs.ETSEL.bit.SOCASEL = ET_CTR_ZERO;      // Conter = 0
    EPwm1Regs.ETPS.bit.SOCAPRD = ET_1ST;            // Generate SOCA pulse on 1st event
    EPwm1Regs.ETCLR.bit.SOCA = 1;                   // Clear SOCA flag
    EPwm1Regs.ETSEL.bit.SOCAEN = 1;                 // Enable SOCA

//    EPwm1Regs.ETSEL.bit.INTSEL = ET_CTR_ZERO;       // Conter = 0
//    EPwm1Regs.ETPS.bit.INTPRD = ET_1ST;             // Generate INT pulse on 1st event
//    EPwm1Regs.ETCLR.bit.INT = 1;                    // Clear INT flag
//    EPwm1Regs.ETSEL.bit.INTEN = 1;                  // Enable Interrupt

    SyncSocRegs.SYNCSELECT.bit.SYNCOUT=0;             // 00: EPWM1SYNCOUT selected

    CpuSysRegs.PCLKCR0.bit.TBCLKSYNC = 1;             // Enable TBCLK within the EPWM

    EDIS;
}


//=======================================================
//函数名称:Init_epwm2(void)
//功能:  初始化EPWM2为20kHz触发SOCA和INT1
//输入:
//返回:
//备注:
//=======================================================
void Init_epwm2(void)
{

    EALLOW;
    EPwm2Regs.TZCTL.bit.TZA = TZ_FORCE_LO;
    EPwm2Regs.TZCTL.bit.TZB = TZ_FORCE_LO;
    EPwm2Regs.TZSEL.bit.OSHT1 = 1;   //All_Fault

    EDIS;
    PWMSoftOutputOff();


    EALLOW;
    CpuSysRegs.PCLKCR0.bit.TBCLKSYNC = 0;             // Disable TBCLK within the EPWM

    EPwm2Regs.TBCTL.bit.CTRMODE = TB_COUNT_UPDOWN;    // Count up and down
    EPwm2Regs.TBCTL.bit.HSPCLKDIV = TB_DIV1;          // TBCLK = EPWMCLK /（HSPCLKDIV× CLKDIV）
    EPwm2Regs.TBCTL.bit.CLKDIV = TB_DIV1;             // 设置Epwm2  TBCLK = 200M/1=100MHz
    EPwm2Regs.TBCTL.bit.PHSEN = TB_ENABLE;           // Enable phase loading
    EPwm2Regs.TBCTL.bit.PRDLD = TB_SHADOW;            // TBPRD is loaded from its shadow register

    EPwm2Regs.TBPRD = mVar_RAM.EPWM_PERIOD_Base;                // Set timer period (100M/(2*2500)=20k)---控制频率
    EPwm2Regs.TBPHS.bit.TBPHS = 0x0000;               // Phase is 0
    EPwm2Regs.TBCTL.bit.PHSDIR = TB_UP;
    EPwm2Regs.TBCTR = 0x0000;                         // Clear counter
    EPwm2Regs.TBCTL.bit.SYNCOSEL = TB_SYNC_IN;       // Output Sync signal

    EPwm2Regs.CMPCTL.bit.SHDWAMODE = CC_SHADOW;      // Load registers every ZERO
    EPwm2Regs.CMPCTL.bit.SHDWBMODE = CC_SHADOW;
    EPwm2Regs.CMPCTL.bit.LOADAMODE = CC_CTR_PRD;
    EPwm2Regs.CMPCTL.bit.LOADBMODE = CC_CTR_PRD;

    // Setup compare
    EPwm2Regs.CMPA.bit.CMPA = 0;

    // Set actions
    EPwm2Regs.AQCTLA.bit.CAU = AQ_SET;             //正三角形计数
    EPwm2Regs.AQCTLA.bit.CAD = AQ_CLEAR;
    EPwm2Regs.AQCTLB.bit.CAU = AQ_SET;              // Set PWM1A on Zero
    EPwm2Regs.AQCTLB.bit.CAD = AQ_CLEAR;

    // Active Low PWMs - Setup Deadband
    EPwm2Regs.DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
    EPwm2Regs.DBCTL.bit.POLSEL = DB_ACTV_HIC;
    EPwm2Regs.DBCTL.bit.IN_MODE = DBA_ALL;
    EPwm2Regs.DBRED.bit.DBRED = mVar_RAM.EPWM_DB;
    EPwm2Regs.DBFED.bit.DBFED = mVar_RAM.EPWM_DB;



    CpuSysRegs.PCLKCR0.bit.TBCLKSYNC = 1;             // Enable TBCLK within the EPWM

    EDIS;
}


//=======================================================
//函数名称:Init_epwm3(void)
//功能:  初始化EPWM3为10kHz触发SOCA和INT1
//输入:
//返回:
//备注:
//=======================================================
void Init_epwm3(void)
{
    EALLOW;
    EPwm3Regs.TZCTL.bit.TZA = TZ_FORCE_LO;
    EPwm3Regs.TZCTL.bit.TZB = TZ_FORCE_LO;
    EPwm3Regs.TZSEL.bit.OSHT1 = 1;   //All_Fault

    EDIS;

    PWMSoftOutputOff();


    EALLOW;
    CpuSysRegs.PCLKCR0.bit.TBCLKSYNC = 0;             // Disable TBCLK within the EPWM

    EPwm3Regs.TBCTL.bit.CTRMODE = TB_COUNT_UPDOWN;    // Count up and down
    EPwm3Regs.TBCTL.bit.HSPCLKDIV = TB_DIV1;          // TBCLK = EPWMCLK /（HSPCLKDIV× CLKDIV）
    EPwm3Regs.TBCTL.bit.CLKDIV = TB_DIV1;             // 设置Epwm2  TBCLK = 200M/1=100MHz
    EPwm3Regs.TBCTL.bit.PHSEN = TB_ENABLE;           // Enable phase loading
    EPwm3Regs.TBCTL.bit.PRDLD = TB_SHADOW;            // TBPRD is loaded from its shadow register

    EPwm3Regs.TBPRD = mVar_RAM.EPWM_PERIOD_Base;                // Set timer period (100M/(2*2500)=20k)---控制频率
    EPwm3Regs.TBPHS.bit.TBPHS = 0x0000;               // Phase is 0
    EPwm3Regs.TBCTL.bit.PHSDIR = TB_UP;
    EPwm3Regs.TBCTR = 0x0000;                         // Clear counter

    EPwm3Regs.CMPCTL.bit.SHDWAMODE = CC_SHADOW;      // Load registers every ZERO
    EPwm3Regs.CMPCTL.bit.SHDWBMODE = CC_SHADOW;
    EPwm3Regs.CMPCTL.bit.LOADAMODE = CC_CTR_PRD;
    EPwm3Regs.CMPCTL.bit.LOADBMODE = CC_CTR_PRD;

    // Setup compare
    EPwm3Regs.CMPA.bit.CMPA = 0;

    // Set actions
    EPwm3Regs.AQCTLA.bit.CAU = AQ_SET;            //正三角形计数
    EPwm3Regs.AQCTLA.bit.CAD = AQ_CLEAR;
    EPwm3Regs.AQCTLB.bit.CAU = AQ_SET;              // Set PWM1A on Zero
    EPwm3Regs.AQCTLB.bit.CAD = AQ_CLEAR;

    // Active Low PWMs - Setup Deadband
    EPwm3Regs.DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
    EPwm3Regs.DBCTL.bit.POLSEL = DB_ACTV_HIC;
    EPwm3Regs.DBCTL.bit.IN_MODE = DBA_ALL;
    EPwm3Regs.DBRED.bit.DBRED = mVar_RAM.EPWM_DB;
    EPwm3Regs.DBFED.bit.DBFED = mVar_RAM.EPWM_DB;


    EPwm1Regs.TBCTL.bit.SWFSYNC = 1;
    EPwm2Regs.TBCTL.bit.SWFSYNC = 1;
    EPwm3Regs.TBCTL.bit.SWFSYNC = 1;

    CpuSysRegs.PCLKCR0.bit.TBCLKSYNC = 1;             // Enable TBCLK within the EPWM

    EDIS;
}


//=======================================================
//函数名称:Init_epwm4(void)
//功能:  初始化EPWM4为200kHz，作为DAC输出
//输入:
//返回:
//备注:
//=======================================================
void Init_epwm4(void)
{
    EALLOW;
    CpuSysRegs.PCLKCR0.bit.TBCLKSYNC = 0;             // Disable TBCLK within the EPWM

    EPwm4Regs.TBCTL.bit.CTRMODE = TB_COUNT_UP;    // Count up
    EPwm4Regs.TBCTL.bit.HSPCLKDIV = TB_DIV1;          // TBCLK = EPWMCLK /（HSPCLKDIV× CLKDIV）
    EPwm4Regs.TBCTL.bit.CLKDIV = TB_DIV1;             // 设置Epwm2  TBCLK = 200M/1=100MHz
    EPwm4Regs.TBCTL.bit.PHSEN = TB_ENABLE;           // Enable phase loading
    EPwm4Regs.TBCTL.bit.PRDLD = TB_SHADOW;            // TBPRD is loaded from its shadow register

    EPwm4Regs.TBPRD = mVar_RAM.EPWM_PERIOD_Base >> 3;                // Set frequency to 8 times of base frequency
    EPwm4Regs.TBPHS.bit.TBPHS = 0x0000;               // Phase is 0
    EPwm4Regs.TBCTL.bit.PHSDIR = TB_UP;
    EPwm4Regs.TBCTR = 0x0000;                         // Clear counter

    EPwm4Regs.CMPCTL.bit.SHDWAMODE = CC_SHADOW;      // Load registers every ZERO
    EPwm4Regs.CMPCTL.bit.SHDWBMODE = CC_SHADOW;
    EPwm4Regs.CMPCTL.bit.LOADAMODE = CC_CTR_PRD;
    EPwm4Regs.CMPCTL.bit.LOADBMODE = CC_CTR_PRD;

    // Setup compare
    EPwm4Regs.CMPA.bit.CMPA = 0;

    // Set actions
    EPwm4Regs.AQCTLA.bit.CAU = AQ_CLEAR;
    EPwm4Regs.AQCTLA.bit.ZRO = AQ_SET;

    EPwm4Regs.ETSEL.bit.SOCASEL = ET_CTR_ZERO;      // Conter = 0
    EPwm4Regs.ETPS.bit.SOCAPRD = ET_1ST;            // Generate SOCA pulse on 1st event
    EPwm4Regs.ETCLR.bit.SOCA = 1;                   // Clear SOCA flag
    EPwm4Regs.ETSEL.bit.SOCAEN = 1;                 // Enable SOCA

    CpuSysRegs.PCLKCR0.bit.TBCLKSYNC = 1;             // Enable TBCLK within the EPWM

    EDIS;
}


//=======================================================
//函数名称:Init_epwm7(void)
//功能:  初始化EPWM7为12.5kHz，作为SPIC-DMA的定时读数。
//输入:
//返回:
//备注:
//=======================================================
void Init_epwm7(void)
{
    EALLOW;
    CpuSysRegs.PCLKCR0.bit.TBCLKSYNC = 0;             // Disable TBCLK within the EPWM

    EPwm7Regs.TBCTL.bit.CTRMODE = TB_COUNT_UPDOWN;    // Count up
    EPwm7Regs.TBCTL.bit.HSPCLKDIV = TB_DIV1;          // TBCLK = EPWMCLK /（HSPCLKDIV× CLKDIV）
    EPwm7Regs.TBCTL.bit.CLKDIV = TB_DIV1;             // 设置Epwm2  TBCLK = 200M/1=100MHz
    EPwm7Regs.TBCTL.bit.PHSEN = TB_ENABLE;           // Enable phase loading
    EPwm7Regs.TBCTL.bit.PRDLD = TB_SHADOW;            // TBPRD is loaded from its shadow register

    EPwm7Regs.TBPRD = mVar_RAM.EPWM_PERIOD_Base >> 3; // Set frequency to 8 times of base frequency
    EPwm7Regs.TBPHS.bit.TBPHS = 0x0000;               // Phase is 0
    EPwm7Regs.TBCTL.bit.PHSDIR = TB_UP;
    EPwm7Regs.TBCTR = 0x0000;                         // Clear counter

    EPwm7Regs.CMPCTL.bit.SHDWAMODE = CC_SHADOW;      // Load registers every ZERO
    EPwm7Regs.CMPCTL.bit.SHDWBMODE = CC_SHADOW;
    EPwm7Regs.CMPCTL.bit.LOADAMODE = CC_CTR_PRD;
    EPwm7Regs.CMPCTL.bit.LOADBMODE = CC_CTR_PRD;

    // Setup compare
    EPwm7Regs.CMPA.bit.CMPA = 0;

    // Set actions
    EPwm7Regs.AQCTLA.bit.CAU = AQ_CLEAR;
    EPwm7Regs.AQCTLA.bit.ZRO = AQ_SET;

    EPwm7Regs.ETSEL.bit.SOCASEL = ET_CTR_ZERO;      // Conter = 0
    EPwm7Regs.ETPS.bit.SOCAPRD = ET_1ST;            // Generate SOCA pulse on 1st event
    EPwm7Regs.ETCLR.bit.SOCA = 1;                   // Clear SOCA flag
    EPwm7Regs.ETSEL.bit.SOCAEN = 1;                 // Enable SOCA

    SyncSocRegs.SYNCSELECT.bit.EPWM7SYNCIN=0;       // 00: Selects Sync Input Source for EPWM7----    000: EPWM1SYNCOUT selected

    CpuSysRegs.PCLKCR0.bit.TBCLKSYNC = 1;             // Enable TBCLK within the EPWM

    EDIS;
}
//=======================================================
//函数名称:Init_ADC(void)
//功能:  初始化ADC-A/B/C/D为12bit模式分别采集6个通道
//输入:
//返回:
//备注:
//=======================================================
void Init_ADC(void)
{
    Uint16 acqps = 30;

    EALLOW;

    //Write configurations
    AdcaRegs.ADCCTL2.bit.PRESCALE = 6   ;    //set ADCCLK divider to /4       200M/4=50M  ADC最大50MHz
    AdcbRegs.ADCCTL2.bit.PRESCALE = 6   ;    //set ADCCLK divider to /4       200M/4=50M  ADC最大50MHz
    AdccRegs.ADCCTL2.bit.PRESCALE = 6   ;    //set ADCCLK divider to /4       200M/4=50M  ADC最大50MHz
    AdcdRegs.ADCCTL2.bit.PRESCALE = 6   ;    //set ADCCLK divider to /4       200M/4=50M  ADC最大50MHz
    AdcSetMode(ADC_ADCA, ADC_RESOLUTION_12BIT, ADC_SIGNALMODE_SINGLE);  //12位单端输入
    AdcSetMode(ADC_ADCB, ADC_RESOLUTION_12BIT, ADC_SIGNALMODE_SINGLE);  //12位单端输入
    AdcSetMode(ADC_ADCC, ADC_RESOLUTION_12BIT, ADC_SIGNALMODE_SINGLE);  //12位单端输入
    AdcSetMode(ADC_ADCD, ADC_RESOLUTION_12BIT, ADC_SIGNALMODE_SINGLE);  //12位单端输入

    //Set pulse positions to late
    AdcaRegs.ADCCTL1.bit.INTPULSEPOS = 1;
    AdcbRegs.ADCCTL1.bit.INTPULSEPOS = 1;
    AdccRegs.ADCCTL1.bit.INTPULSEPOS = 1;
    AdcdRegs.ADCCTL1.bit.INTPULSEPOS = 1;

    //Power up the ADC
    AdcaRegs.ADCCTL1.bit.ADCPWDNZ = 1;
    AdcbRegs.ADCCTL1.bit.ADCPWDNZ = 1;
    AdccRegs.ADCCTL1.bit.ADCPWDNZ = 1;
    AdcdRegs.ADCCTL1.bit.ADCPWDNZ = 1;

    //Delay for 1ms to allow ADC time to power up
    DELAY_US(1000);


    //ADCA: 0:A0    1:A1   2:A2   3:A3  4:A4    5:A5
    //ADCB: 0:B0    1:B1   2:B2   3:B3  4:B4    5:B5
    //ADCC: 0:XX    1:XX   2:C2   3:C3  4:C4    5:C5 (无C0,C1)
    //ADCD: 0:D0    1:D1   2:D2   3:D3  4:D4    5:D5
    //ADCx: e:X14   f:X15  ----(X=A,B,C,D)

    //ADCA: 0:A0---xx      1:A1---3.3V    2:A2---xx    3:A3----NTC1   4:A4---xx      5:A5---NTC2
    //ADCB: 0:B0---xx      1:B1---Ibus    2:B2----5V   3:B3---xxx     4:B4---xx      5:B5---
    //ADCC: 0:C2---xx      1:C3---Ib      2:C4---Ic    3:C5---Ia      4:C14----PT1   5:C15----PT4
    //ADCD: 0:D2---Vbus1   1:D0---Vbus2   2:D1---xx    3:D4----PT2
    //ADCD: 4:D5---PT3     4:D3---PT5

    //ADCA: 0:A1---3.3V    1:A3----NTC1   2:A5---NTC2  3:C14----PT1    4:A1---C15----PT4    5:----
    //ADCB: 0:B1---Ibus    1:B1---Ibus    2:B2----5V   3:B2----5V      4:B2----5V           5:----
    //ADCC: 0:C5---Ia      1:C3---Ib      2:C4---Ic
    //ADCC: 3:C5---Ia      4:C3---Ib      5:C4---Ic
    //ADCC: 6:C5---Ia      7:C3---Ib      8:C4---Ic
    //ADCD: 0:D2---Vbus1   1:D0---Vbus2   2:D4----PT2  3: D5---PT3  4:D3---PT5              5:----



    //ADCA: 0:A1---3.3V    1:A3----NTC1   2:A5---NTC2  3:C14----PT1    4:A1---C15----PT4    5:----
    AdcaRegs.ADCSOC0CTL.bit.CHSEL   = 1;          //SOC0 will convert pin A1----3.3V
    AdcaRegs.ADCSOC0CTL.bit.ACQPS   = acqps;      //sample window is 40 SYSCLK cycles
    AdcaRegs.ADCSOC0CTL.bit.TRIGSEL = 5;          //tri0gger on  ePWM1 SOCA

    AdcaRegs.ADCSOC1CTL.bit.CHSEL   = 3;          //SOC1 will convert pin A3---NTC1
    AdcaRegs.ADCSOC1CTL.bit.ACQPS   = acqps;      //sample window is 40 SYSCLK cycles
    AdcaRegs.ADCSOC1CTL.bit.TRIGSEL = 5;          //trigger on  ePWM1 SOCA

    AdcaRegs.ADCSOC2CTL.bit.CHSEL   = 5;          //SOC2 will convert pin A5---NTC2
    AdcaRegs.ADCSOC2CTL.bit.ACQPS   = acqps;      //sample window is 40 SYSCLK cycles
    AdcaRegs.ADCSOC2CTL.bit.TRIGSEL = 5;          //trigger on  ePWM1 SOCA

    AdcaRegs.ADCSOC3CTL.bit.CHSEL   = 0xE;          //SOC3 will convert pin C14---PT1
    AdcaRegs.ADCSOC3CTL.bit.ACQPS   = acqps;      //sample window is 40 SYSCLK cycles
    AdcaRegs.ADCSOC3CTL.bit.TRIGSEL = 5;          //trigger on  ePWM1 SOCA

    AdcaRegs.ADCSOC4CTL.bit.CHSEL   = 0xF;          //SOC4 will convert pin C15---PT4
    AdcaRegs.ADCSOC4CTL.bit.ACQPS   = acqps;      //sample window is 40 SYSCLK cycles
    AdcaRegs.ADCSOC4CTL.bit.TRIGSEL = 5;          //trigger on  ePWM1 SOCA

//    AdcaRegs.ADCSOC5CTL.bit.CHSEL   = 5;          //SOC5 will convert pin A5---xxxxx
//    AdcaRegs.ADCSOC5CTL.bit.ACQPS   = acqps;      //sample window is 40 SYSCLK cycles
//    AdcaRegs.ADCSOC5CTL.bit.TRIGSEL = 5;          //trigger on  ePWM1 SOCA

//   //ADC-B
    //ADCB: 0:B1---Ibus    1:B1---Ibus    2:B2----5V   3:B2----5V     4:B2----5V     5:----
    AdcbRegs.ADCSOC0CTL.bit.CHSEL   = 1;          //SOC0 will convert pin B1---Ibus
    AdcbRegs.ADCSOC0CTL.bit.ACQPS   = acqps;      //sample window is 40 SYSCLK cycles
    AdcbRegs.ADCSOC0CTL.bit.TRIGSEL = 5;          //trigger on  ePWM1 SOCA

    AdcbRegs.ADCSOC1CTL.bit.CHSEL   = 1;          //SOC1 will convert pin B1---Ibus
    AdcbRegs.ADCSOC1CTL.bit.ACQPS   = acqps;      //sample window is 40 SYSCLK cycles
    AdcbRegs.ADCSOC1CTL.bit.TRIGSEL = 5;          //trigger on  ePWM1 SOCA

    AdcbRegs.ADCSOC2CTL.bit.CHSEL   = 2;          //SOC2 will convert pin B2----5V
    AdcbRegs.ADCSOC2CTL.bit.ACQPS   = acqps;      //sample window is 40 SYSCLK cycles
    AdcbRegs.ADCSOC2CTL.bit.TRIGSEL = 5;          //trigger on  ePWM1 SOCA

    AdcbRegs.ADCSOC3CTL.bit.CHSEL   = 2;          //SOC3 will convert pin B2---5V
    AdcbRegs.ADCSOC3CTL.bit.ACQPS   = acqps;      //sample window is 40 SYSCLK cycles
    AdcbRegs.ADCSOC3CTL.bit.TRIGSEL = 5;          //trigger on  ePWM1 SOCA

    AdcbRegs.ADCSOC4CTL.bit.CHSEL   = 2;          //SOC4 will convert pin B2----5V
    AdcbRegs.ADCSOC4CTL.bit.ACQPS   = acqps;      //sample window is 40 SYSCLK cycles
    AdcbRegs.ADCSOC4CTL.bit.TRIGSEL = 5;          //trigger on  ePWM1 SOCA

//    AdcbRegs.ADCSOC5CTL.bit.CHSEL   = 5;          //SOC5 will convert pin B5---3.3V
//    AdcbRegs.ADCSOC5CTL.bit.ACQPS   = acqps;      //sample window is acqps SYSCLK cycles
//    AdcbRegs.ADCSOC5CTL.bit.TRIGSEL = 5;          //trigger on  ePWM1 SOCA


//   //ADC-C, four samples per trigger
    //ADCC: 0:C5---Ia      1:C3---Ib      2:C4---Ic
    //ADCC: 3:C5---Ia      4:C3---Ib      5:C4---Ic
    //ADCC: 6:C5---Ia      7:C3---Ib      8:C4---Ic
    //ADCC: 9:C5---Ia      10:C3---Ib     11:C4---Ic
    AdccRegs.ADCSOC0CTL.bit.CHSEL = 5;         //SOC0 will convert pin C5---Ia
    AdccRegs.ADCSOC0CTL.bit.ACQPS   = acqps;   //sample window is 40 SYSCLK cycles
    AdccRegs.ADCSOC0CTL.bit.TRIGSEL = 11;       //trigger on  ePWM4 SOCA

    AdccRegs.ADCSOC1CTL.bit.CHSEL   = 3;         //SOC1 will convert pin C3---Ib
    AdccRegs.ADCSOC1CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
    AdccRegs.ADCSOC1CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA

    AdccRegs.ADCSOC2CTL.bit.CHSEL   = 4;         //SOC2 will convert pin C4---Ic
    AdccRegs.ADCSOC2CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
    AdccRegs.ADCSOC2CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA

    AdccRegs.ADCSOC3CTL.bit.CHSEL = 5;         //SOC0 will convert pin C5---Ia
    AdccRegs.ADCSOC3CTL.bit.ACQPS   = acqps;   //sample window is 40 SYSCLK cycles
    AdccRegs.ADCSOC3CTL.bit.TRIGSEL = 11;       //trigger on  ePWM4 SOCA

    AdccRegs.ADCSOC4CTL.bit.CHSEL   = 3;         //SOC1 will convert pin C3---Ib
    AdccRegs.ADCSOC4CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
    AdccRegs.ADCSOC4CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA

    AdccRegs.ADCSOC5CTL.bit.CHSEL   = 4;         //SOC2 will convert pin C4---Ic
    AdccRegs.ADCSOC5CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
    AdccRegs.ADCSOC5CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA

    AdccRegs.ADCSOC6CTL.bit.CHSEL = 5;         //SOC0 will convert pin C5---Ia
    AdccRegs.ADCSOC6CTL.bit.ACQPS   = acqps;   //sample window is 40 SYSCLK cycles
    AdccRegs.ADCSOC6CTL.bit.TRIGSEL = 11;       //trigger on  ePWM4 SOCA

    AdccRegs.ADCSOC7CTL.bit.CHSEL   = 3;         //SOC1 will convert pin C3---Ib
    AdccRegs.ADCSOC7CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
    AdccRegs.ADCSOC7CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA

    AdccRegs.ADCSOC8CTL.bit.CHSEL   = 4;         //SOC2 will convert pin C4---Ic
    AdccRegs.ADCSOC8CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
    AdccRegs.ADCSOC8CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA

    AdccRegs.ADCSOC9CTL.bit.CHSEL = 5;         //SOC0 will convert pin C5---Ia
	AdccRegs.ADCSOC9CTL.bit.ACQPS   = acqps;   //sample window is 40 SYSCLK cycles
	AdccRegs.ADCSOC9CTL.bit.TRIGSEL = 11;       //trigger on  ePWM4 SOCA

	AdccRegs.ADCSOC10CTL.bit.CHSEL   = 3;         //SOC1 will convert pin C3---Ib
	AdccRegs.ADCSOC10CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
	AdccRegs.ADCSOC10CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA

	AdccRegs.ADCSOC11CTL.bit.CHSEL   = 4;         //SOC2 will convert pin C4---Ic
	AdccRegs.ADCSOC11CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
	AdccRegs.ADCSOC11CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA


    //ADC-D
    //ADCD: 0:D0---Vbus1   1:D2---Vbus2   2:D4----PT2  3: D5---PT3  4:D3---PT5              5:----
    AdcdRegs.ADCSOC0CTL.bit.CHSEL   = 2;         //SOC0 will convert pin D0----Vbus1
    AdcdRegs.ADCSOC0CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
    AdcdRegs.ADCSOC0CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA

    AdcdRegs.ADCSOC1CTL.bit.CHSEL   = 0;         //SOC1 will convert pin D2--Vbus2
    AdcdRegs.ADCSOC1CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
    AdcdRegs.ADCSOC1CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA

    AdcdRegs.ADCSOC2CTL.bit.CHSEL   = 4;         //SOC2 will convert pin D4--PT2
    AdcdRegs.ADCSOC2CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
    AdcdRegs.ADCSOC2CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA

    AdcdRegs.ADCSOC3CTL.bit.CHSEL   = 5;         //SOC3 will convert pin D5---PT3
    AdcdRegs.ADCSOC3CTL.bit.ACQPS   = acqps;     //sample window is 40 SYSCLK cycles
    AdcdRegs.ADCSOC3CTL.bit.TRIGSEL = 11;         //trigger on  ePWM4 SOCA

    AdcdRegs.ADCSOC4CTL.bit.CHSEL  = 3;        //SOC4 will convert pin D3---PT5
    AdcdRegs.ADCSOC4CTL.bit.ACQPS   = acqps;   //sample window is 40 SYSCLK cycles
    AdcdRegs.ADCSOC4CTL.bit.TRIGSEL = 11;       //trigger on  ePWM4 SOCA

    AdcaRegs.ADCINTSEL1N2.bit.INT1SEL = 4;       //End of ADC-A SOC4 will set INT1 flag
    AdcaRegs.ADCINTFLGCLR.bit.ADCINT1 = 1;       //Make sure INT1 flag is cleared
    AdcaRegs.ADCINTSEL1N2.bit.INT1E   = 1;       //Enable INT1 flag

//    AdccRegs.ADCINTSEL1N2.bit.INT1SEL = 6;      // End of ADC-C SOC6 will set INT1 flag
//    AdccRegs.ADCINTSEL1N2.bit.INT1CONT = 1;     // ADCC INT1 Continue to Interrupt Mode(*在DMA模式下使用)
//    AdccRegs.ADCINTFLGCLR.bit.ADCINT1 = 1;      // Make sure INT1 flag is cleared
//    AdccRegs.ADCINTSEL1N2.bit.INT1E = 1;        // Enable INT1 flag

    AdccRegs.ADCINTSEL1N2.bit.INT1SEL = 8;      // End of ADC-C SOC8 will set INT1 flag
    AdccRegs.ADCINTSEL1N2.bit.INT1CONT = 1;     // ADCD INT1 Continue to Interrupt Mode(*在DMA模式下使用)
    AdccRegs.ADCINTFLGCLR.bit.ADCINT1 = 1;      // Make sure INT1 flag is cleared
    AdccRegs.ADCINTSEL1N2.bit.INT1E = 1;        // Enable INT1 flag

    EDIS;
}

//=======================================================
//函数名称:Init_emif1_async_cs3(void)
//功能:  初始化emif1为CS3异步访问模式
//输入:
//返回:
//备注:
//=======================================================
//void Init_emif1_async_cs3(void)
//{
//    EALLOW;
//    ClkCfgRegs.PERCLKDIVSEL.bit.EMIF1CLKDIV = 0x0;          //  EMIF1 Clock Divide Select:  /1 of PLLSYSCLK is selected
//    Emif1ConfigRegs.EMIF1MSEL.all = MSEL_EMIF1_CPU1;    //EMIF1 Master Sel Register: CPU1 is master
//    EDIS;
//
//    Emif1Regs.ASYNC_CS3_CR.all =  (EMIF_ASYNC_ASIZE_16  |      // Asynchronous Memory Size: 16 Bit data bus
//                                            EMIF_ASYNC_TA_4          |          // Turn Around cycles of 4 EMxCLK cycles
//                                            EMIF_ASYNC_RHOLD_8       |          // Read Strobe Hold cycles of 4 EMxCLK cycles
//                                            EMIF_ASYNC_RSTROBE_16    |          // Read Strobe Duration cycles of 16 EMxCLK cycles  (ensure 45ns)
//                                            EMIF_ASYNC_RSETUP_8      |          // Read Strobe Setup cycles of 4 EMxCLK cycles
//                                            EMIF_ASYNC_WHOLD_8       |          // Write Strobe Hold cycles of 4 EMxCLK cycles
//                                            EMIF_ASYNC_WSTROBE_16    |          // Write Strobe Duration cycles of 16 EMxCLK cycles (ensure 30ns)
//                                            EMIF_ASYNC_WSETUP_8      |          // Write Strobe Setup cycles of 4 EMxCLK cycles
//                                            EMIF_ASYNC_EW_DISABLE    |          // Extended Wait Disable.
//                                            EMIF_ASYNC_SS_DISABLE               // Strobe Select Mode Disable.
//                                           );
//
//
////    Emif1Regs.ASYNC_CS3_CR.all =  (EMIF_ASYNC_ASIZE_16  |      // Asynchronous Memory Size: 16 Bit data bus
////                                            EMIF_ASYNC_TA_4          |          // Turn Around cycles of 4 EMxCLK cycles
////                                            EMIF_ASYNC_RHOLD_4       |          // Read Strobe Hold cycles of 4 EMxCLK cycles
////                                            EMIF_ASYNC_RSTROBE_16    |          // Read Strobe Duration cycles of 16 EMxCLK cycles  (ensure 45ns)
////                                            EMIF_ASYNC_RSETUP_4      |          // Read Strobe Setup cycles of 4 EMxCLK cycles
////                                            EMIF_ASYNC_WHOLD_4       |          // Write Strobe Hold cycles of 4 EMxCLK cycles
////                                            EMIF_ASYNC_WSTROBE_16    |          // Write Strobe Duration cycles of 16 EMxCLK cycles (ensure 30ns)
////                                            EMIF_ASYNC_WSETUP_4      |          // Write Strobe Setup cycles of 4 EMxCLK cycles
////                                            EMIF_ASYNC_EW_DISABLE    |          // Extended Wait Disable.
////                                            EMIF_ASYNC_SS_DISABLE               // Strobe Select Mode Disable.
////                                           );
//
//}

//=======================================================
//函数名称:Init_scia_fifo(void)
//功能:  初始化SCIA
//输入:
//返回:
//备注:
//=======================================================
void Init_scia_fifo(void)
{
    // 1 stop bit,  No loopback, No parity,8 char bits,async mode, idle-line protocol
   SciaRegs.SCICCR.all =0x0007;
   // enable TX, RX, internal SCICLK, Disable RX ERR, SLEEP, TXWAKE
   SciaRegs.SCICTL1.all =0x0003;
   SciaRegs.SCICTL2.bit.TXINTENA =0;             // Disable SCI Transmit interrupt
   SciaRegs.SCICTL2.bit.RXBKINTENA =0;         // Disable SCI Receive interrupt

   SciaRegs.SCIHBAUD.all = 0x0002;                // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=9600
   SciaRegs.SCILBAUD.all = 0x008A;                // BRR = 650(28AH)

//   SciaRegs.SCIHBAUD.all =0x0001;             // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=19200
//   SciaRegs.SCILBAUD.all =0x0045;              // BRR = 325(145H)
//
//   SciaRegs.SCIHBAUD = 0x0000;                // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=115200
//   SciaRegs.SCILBAUD  = 0x0035;                // BRR = 53(35H)

//   SciaRegs.SCIHBAUD.all = 0x0000;                // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=460800
//   SciaRegs.SCILBAUD.all  = 0x000C;                // BRR = 12(0CH)

   SciaRegs.SCIFFTX.all=0xE040;//0xC028;
   SciaRegs.SCIFFRX.all=0x2044;//0x0028;                  // 8 fifo+disable interupt
   SciaRegs.SCIFFCT.all=0x00;

   SciaRegs.SCICTL1.all =0x0023;                 // Relinquish SCI from Reset

   SciaRegs.SCIFFTX.bit.TXFIFORESET=1;
   SciaRegs.SCIFFRX.bit.RXFIFORESET=1;

}

//=======================================================
//函数名称:Init_scib_fifo(void)
//功能:  初始化SCIB
//输入:
//返回:
//备注:
//=======================================================
void Init_scib_fifo(void)
{
    // 1 stop bit,  No loopback, No parity,8 char bits,async mode, idle-line protocol
   ScibRegs.SCICCR.all =0x0007;
   // enable TX, RX, internal SCICLK, Disable RX ERR, SLEEP, TXWAKE
   ScibRegs.SCICTL1.all =0x0003;
   ScibRegs.SCICTL2.bit.TXINTENA =0;             // Disable SCI Transmit interrupt
   ScibRegs.SCICTL2.bit.RXBKINTENA =0;         // Enable SCI Receive interrupt

   ScibRegs.SCIHBAUD.all = 0x0002;                // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=9600
   ScibRegs.SCILBAUD.all = 0x008A;                // BRR = 650(28AH)

//   ScibRegs.SCIHBAUD.all =0x0001;             // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=19200
//   ScibRegs.SCILBAUD.all =0x0045;              // BRR = 325(145H)
//
//   ScibRegs.SCIHBAUD = 0x0000;                // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=115200
//   ScibRegs.SCILBAUD  = 0x0035;                // BRR = 53(35H)

   ScibRegs.SCIFFTX.all=0xE040;//0xC028;
   ScibRegs.SCIFFRX.all=0x2044;//0x0028;                   // 8 fifo+enable interupt
   ScibRegs.SCIFFCT.all=0x00;

   ScibRegs.SCICTL1.all =0x0023;                 // Relinquish SCI from Reset
   ScibRegs.SCIFFTX.bit.TXFIFORESET=1;
   ScibRegs.SCIFFRX.bit.RXFIFORESET=1;
}

//=======================================================
//函数名称:Init_scic_fifo(void)
//功能:  初始化SCIC
//输入:
//输出:
//返回:
//=======================================================
void Init_scic_fifo(void)
{
    // 1 stop bit,  No loopback, No parity,8 char bits,async mode, idle-line protocol
   ScicRegs.SCICCR.all =0x0007;
   // enable TX, RX, internal SCICLK, Disable RX ERR, SLEEP, TXWAKE
   ScicRegs.SCICTL1.all =0x0003;
   ScicRegs.SCICTL2.bit.TXINTENA =0;         // Disable SCI Transmit interrupt
   ScicRegs.SCICTL2.bit.RXBKINTENA =0;     // Enable SCI Receive interrupt

   ScicRegs.SCIHBAUD.all = 0x0002;                // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=9600
   ScicRegs.SCILBAUD.all = 0x008A;                // BRR = 650(28AH)

//   ScicRegs.SCIHBAUD.all =0x0001;             // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=19200
//   ScicRegs.SCILBAUD.all =0x0045;              // BRR = 325(145H)
//
//   ScicRegs.SCIHBAUD = 0x0000;                // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=115200
//   ScicRegs.SCILBAUD  = 0x0035;                // BRR = 53(35H)

   ScicRegs.SCIFFTX.all=0xE040;//0xC028;
   ScicRegs.SCIFFRX.all=0x2044;//0x0028;                  // 8 fifo+enable interupt
   ScicRegs.SCIFFCT.all=0x00;

   ScicRegs.SCICTL1.all =0x0023;                 // Relinquish SCI from Reset
   ScicRegs.SCIFFTX.bit.TXFIFORESET=1;
   ScicRegs.SCIFFRX.bit.RXFIFORESET=1;
}

//=======================================================
//函数名称:Init_scid_fifo(void)
//功能:  初始化SCID(485-2)
//输入:
//返回:
//备注:
//=======================================================
void Init_scid_fifo(void)
{
    // 1 stop bit,  No loopback, No parity,8 char bits,async mode, idle-line protocol
   ScidRegs.SCICCR.all =0x0007;
   // enable TX, RX, internal SCICLK, Disable RX ERR, SLEEP, TXWAKE
   ScidRegs.SCICTL1.all =0x0003;
   ScidRegs.SCICTL2.bit.TXINTENA =0;             // Disable SCI Transmit interrupt
   ScidRegs.SCICTL2.bit.RXBKINTENA =0;         // Enable SCI Receive interrupt

   ScidRegs.SCIHBAUD.all = 0x0002;                // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=9600
   ScidRegs.SCILBAUD.all = 0x008A;                // BRR = 650(28AH)

//   ScidRegs.SCIHBAUD.all =0x0001;             // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=19200
//   ScidRegs.SCILBAUD.all =0x0045;              // BRR = 325(145H)
//
//   ScidRegs.SCIHBAUD.all = 0x0000;                // BRR = (LSPCLK_FREQ/(SCI_FREQ*8))-1     LSPCLK_FREQ=200M/4=50M   SCI_FREQ=115200
//   ScidRegs.SCILBAUD.all  = 0x0035;                // BRR = 53(35H)

//   ScidRegs.SCIFFTX.bit.SCIRST = 0;             // Reset the SCI transmit and receive channels.
//   ScidRegs.SCIFFTX.bit.SCIRST = 1;             // Reset the SCI transmit and receive channels.
//   ScidRegs.SCIFFTX.bit.SCIFFENA = 1;        // SCI FIFO enhancements are enabled
//   ScidRegs.SCIFFTX.bit.TXFIFORESET = 0;   // Reset the FIFO pointer to zero and hold in reset
//   ScidRegs.SCIFFTX.bit.TXFIFORESET = 1;   // Reset the FIFO pointer to zero and hold in reset
//   ScidRegs.SCIFFTX.bit.TXFFINTCLR =  1;   // clear TXFFINT flag.
//   ScidRegs.SCIFFTX.bit.TXFFIENA = 0;        // TX FIFO interrupt is disabled.
//
//   ScidRegs.SCIFFRX.bit.RXFIFORESET = 0;    // Receive FIFO reset
//   ScidRegs.SCIFFRX.bit.RXFIFORESET = 1;    // Receive FIFO reset
//   ScidRegs.SCIFFRX.bit.RXFFOVRCLR =1;     // RXFFOVF clear
//   ScidRegs.SCIFFRX.bit.RXFFINTCLR =1;      // Receive FIFO interrupt clear
//   ScidRegs.SCIFFRX.bit.RXFFIENA =1;          // Receive FIFO interrupt enable
//   ScidRegs.SCIFFRX.bit.RXFFIL = CNT_485;   //配置中断等级为命令字长度3位

   ScidRegs.SCIFFTX.all=0xE040;//0xC028;
   ScidRegs.SCIFFRX.all=0x2044;//0x0028;                  // 8 fifo+enable interupt
   ScidRegs.SCIFFCT.all=0x00;

   ScidRegs.SCICTL1.all =0x0023;                 // Relinquish SCI from Reset

   ScidRegs.SCIFFTX.bit.TXFIFORESET=1;
   ScidRegs.SCIFFRX.bit.RXFIFORESET=1;
}

//=======================================================
//函数名称:Init_spia_fifo(void)
//功能:  初始化SPIA
//输入:
//返回:
//备注:
//=======================================================
void Init_spia_fifo(void)
{
//    SpiaRegs.SPIFFTX.all = 0xE040;
//    SpiaRegs.SPIFFRX.all = 0x2044;
//    SpiaRegs.SPIFFCT.all = 0x0000;

    SpiaRegs.SPICCR.bit.SPISWRESET   = 0;         // Set reset low before configuration changes
    SpiaRegs.SPICCR.bit.CLKPOLARITY = 1;       // Clock polarity (0 == rising, 1 == falling)
    SpiaRegs.SPICCR.bit.HS_MODE = 1;            // Enable SPI High Speed mode
    SpiaRegs.SPICCR.bit.SPILBK = 0;                 // Disable loop-back
    SpiaRegs.SPICCR.bit.SPICHAR = (16-1);       // 16-bit character

    SpiaRegs.SPICTL.bit.MASTER_SLAVE = 1;       // Enable master (0 == slave, 1 == master)
    SpiaRegs.SPICTL.bit.TALK = 1;                       // Enable transmission (Talk)
    SpiaRegs.SPICTL.bit.CLK_PHASE = 1;              // Clock phase (0 == normal, 1 == delayed)
    SpiaRegs.SPICTL.bit.SPIINTENA = 0;              // SPI interrupts are disabled

    SpiaRegs.SPIBRR.all=0x031;                   //SPI Baud Rate = 50MHz / (49 + 1)=1MHz
//    SpiaRegs.SPIBRR.all=0x063;                   //SPI Baud Rate = 50MHz / (99 + 1)=500kHz

    SpiaRegs.SPIPRI.all =0x0000;                 // enable talk, and SPI int disabled.// enable talk(Enables transmission For the 4-pin option),disable int.
    SpiaRegs.SPIPRI.bit.FREE=1;                  // continue SPI operation when suspend occurred

    SpiaRegs.SPICCR.bit.SPISWRESET = 1;         // Release the SPI from reset
}

//=======================================================
//函数名称:Init_spib_fifo(void)
//功能:  初始化SPIB
//输入:
//返回:
//备注:
//=======================================================
void Init_spib_fifo(void)
{
    SpibRegs.SPICCR.all=0x0047;                 // reset on, falling edge output, raising edge input, 16-bits
    SpibRegs.SPICTL.all=0x0006;                  //master mode, output enabled, normal opration, no int
    SpibRegs.SPIPRI.all =0x0000;                 // enable talk, and SPI int disabled.// enable talk(Enables transmission For the 4-pin option),disable int.
    //    SpiaRegs.SPIBRR.all=0x031;                     //SPI Baud Rate = 50MHz / (49 + 1)=1MHz
    SpibRegs.SPIBRR.all=0x063;                   //SPI Baud Rate = 50MHz / (99 + 1)=500kHz
    SpibRegs.SPICCR.all=0x00c7;                 // SPI exit from Reset ,  SPI High Speed mode enabled
    SpibRegs.SPIPRI.bit.FREE=1;                  // continue SPI operation when suspend occurred
}


//=======================================================
//函数名称:Init_spic_fifo(void)
//功能:  初始化SPIC
//输入:
//返回:
//备注:旋变SPI接口
//=======================================================
void Init_spic_fifo(void)
{
    SpicRegs.SPICCR.bit.SPISWRESET   = 0;       // Set reset low before configuration changes
    SpicRegs.SPICCR.bit.CLKPOLARITY = 0;        // Clock polarity (0 == rising, 1 == falling)
    SpicRegs.SPICCR.bit.HS_MODE = 1;            // Enable SPI High Speed mode
    SpicRegs.SPICCR.bit.SPILBK = 0;             // 0---Disable loop-back,1---enable loop-back
    SpicRegs.SPICCR.bit.SPICHAR = (8-1);       // 8-bit character----8位数据，一次发送一个字节。  数据12位需要发送2次，读故障寄存器需要发送1次

    SpicRegs.SPIFFTX.all = 0xE040;//使能接收FIFO则需同时设置SPIFFTX寄存器。

    //    SpiaRegs.SPIFFRX.all = 0x2044;
    SpicRegs.SPIFFRX.bit.RXFFOVFCLR = 1;    //Clear SPIFFRX RXFFOVF.
    SpicRegs.SPIFFRX.bit.RXFIFORESET = 0;   //Reset the FIFO pointer to zero, and hold in reset.
    SpicRegs.SPIFFRX.bit.RXFIFORESET = 1;   //Re-enable receive FIFO operation.
    SpicRegs.SPIFFRX.bit.RXFFINTCLR  =1 ;
    SpicRegs.SPIFFRX.bit.RXFFIENA = 1;      //RX FIFO interrupt based on RXFFIL match (greater than
                                                    //or equal to) will be disabled.
    SpicRegs.SPIFFRX.bit.RXFFIL = 4;        //A RX FIFO interrupt request is generated when there
                                                      //are 1 word in the RX buffer.
//    SpiaRegs.SPIFFCT.all = 0x0002; //h (R/W) = The next word in the TX FIFO buffer is transferred to SPITXBUF 2 serial clock cycles after  completion of transmission of the previous word.




    SpicRegs.SPICTL.bit.MASTER_SLAVE = 1;       // Enable master (0 == slave, 1 == master)
    SpicRegs.SPICTL.bit.TALK = 1;               // Enable transmission (Talk)
    SpicRegs.SPICTL.bit.CLK_PHASE = 0;          // Clock phase (0 == normal, 1 == delayed)  rising  edge without delay
    SpicRegs.SPICTL.bit.SPIINTENA = 1;          // SPI interrupts are disabled

    SpicRegs.SPIBRR.all=0x003;                  //SPI Baud Rate = 50MHz / (3 + 1)=12.5MHz
//    SpicRegs.SPIBRR.all=0x004;                  //SPI Baud Rate = 50MHz / (4 + 1)=10MHz
//    SpicRegs.SPIBRR.all=0x009;                  //SPI Baud Rate = 50MHz / (9 + 1)=5MHz
//    SpicRegs.SPIBRR.all=0x031;                  //SPI Baud Rate = 50MHz / (49 + 1)=1MHz
//    SpiaRegs.SPIBRR.all=0x063;                //SPI Baud Rate = 50MHz / (99 + 1)=500kHz

    SpicRegs.SPIFFCT.bit.TXDLY =0x0A;         //These bits define the delay between every transfer from FlFOtransmit buffer to transmit shift register

    SpicRegs.SPIPRI.all =0x0000;                // enable talk, and SPI int disabled.// enable talk(Enables transmission For the 4-pin option),disable int.
    SpicRegs.SPIPRI.bit.FREE=1;                 // continue SPI operation when suspend occurred

    SpicRegs.SPICCR.bit.SPISWRESET = 1;         // Release the SPI from reset
}
//=======================================================
//函数名称:InitMyGpio(void)
//功能:  初始化用户IO
//输入:
//返回:
//备注:
//=======================================================
void InitMyGpio(void)
{

    // 一、通信
    //1.1 RS-422通信-1
    //     1.RX_IN1      [GPIO85]        异步时钟 SCIRXDA外设功能I[GMUX=1][MUX=2]      422接收引脚          上拉
    //     2.TX_OUT1     [GPIO84]        SCITXDA外设功能O[GMUX=1][MUX=2]           422发送引脚          上拉
    //     3.COM_/RE1    [GPIO3]    11   GPIO输出O[GMUX=0][MUX=0]                 422接收使能引脚
    //     4.COM_DE1     [GPIO2]    12   GPIO输出O[GMUX=0][MUX=0]                 422接收使能引脚

            EALLOW;

            GpioCtrlRegs.GPCCSEL3.bit.GPIO85 = 0;           // Select CPU1 control the  GPIO85
            GpioCtrlRegs.GPCCSEL3.bit.GPIO84 = 0;           // Select CPU1 control the  GPIO84
            GpioCtrlRegs.GPACSEL1.bit.GPIO3 = 0;           // Select CPU1 control the  GPIO3
            GpioCtrlRegs.GPACSEL1.bit.GPIO2 = 0;           // Select CPU1 control the  GPIO2

            GpioCtrlRegs.GPCGMUX2.bit.GPIO85 = 1;        // Configure GPIO85 for SCIRXDA operation
            GpioCtrlRegs.GPCMUX2.bit.GPIO85  = 2;
            GpioCtrlRegs.GPCGMUX2.bit.GPIO84 = 1;        // Configure GPIO84 for SCITXDA operation
            GpioCtrlRegs.GPCMUX2.bit.GPIO84  = 2;

            GpioCtrlRegs.GPAGMUX1.bit.GPIO3 = 0;        // Configure GPIO3 for GPIO
            GpioCtrlRegs.GPAMUX1.bit.GPIO3  = 0;
            GpioCtrlRegs.GPAGMUX1.bit.GPIO2 = 0;        // Configure GPIO2 for GPIO
            GpioCtrlRegs.GPAMUX1.bit.GPIO2  = 0;

            GpioCtrlRegs.GPCPUD.bit.GPIO85 = 0;             // Enable pull-up for GPIO85 (SCIRXDA)
            GpioCtrlRegs.GPCPUD.bit.GPIO84 = 0;             // Enable pull-up for GPIO84 (SCITXDA)
            GpioCtrlRegs.GPAPUD.bit.GPIO3 = 1;             // Disable pull-up for GPIO3
            GpioCtrlRegs.GPAPUD.bit.GPIO2 = 1;             // Disable pull-up for GPIO2

            GpioCtrlRegs.GPCDIR.bit.GPIO85 = 0;              // GPIO9(SCIRXDA) is INPUT
            GpioCtrlRegs.GPCDIR.bit.GPIO84 = 1;              // GPIO8(SCITXDA) is OUTPUT
            GpioCtrlRegs.GPADIR.bit.GPIO3 = 1;              // GPIO3  is OUTPUT
            GpioCtrlRegs.GPADIR.bit.GPIO2 = 1;              // GPIO2  is OUTPUT
    //      GpioCtrlRegs.GPADIR.bit.GPIO3 = 0;              // GPIO3  is INPUT
    //      GpioCtrlRegs.GPADIR.bit.GPIO2 = 0;              // GPIO2  is INPUT

            GpioCtrlRegs.GPCQSEL2.bit.GPIO85 = 3;          // Asynch input GPIO85 (SCIRXDA)

    //        GpioDataRegs.GPACLEAR.bit.GPIO3 = 1;        //422-1开启接收
    //        GpioDataRegs.GPACLEAR.bit.GPIO2 = 1;        //422-1关闭发送




    //1.2 RS-485通信-2--SCI-D
    //    1.SCIRXDD[GPIO142]    138     SCITXDD
    //    2.RX_IN2  [GPIO46]            SCIRXDD外设功能I[GMUX=1][MUX=2]    422接收引脚         上拉

    //
 //           GpioCtrlRegs.GPECSEL2.bit.GPIO142 = 0;          // Select CPU1 control the  GPIO142
 //           GpioCtrlRegs.GPBCSEL2.bit.GPIO46 = 0;           // Select CPU1 control the  GPIO46

 //           GpioCtrlRegs.GPEGMUX1.bit.GPIO142 = 1;          // Configure GPIO142 for SCITXDD operation
 //           GpioCtrlRegs.GPEMUX1.bit.GPIO142  = 2;
 //           GpioCtrlRegs.GPBGMUX1.bit.GPIO46 = 1;           // Configure GPIO46 for SCIRXDD operation
  //          GpioCtrlRegs.GPBMUX1.bit.GPIO46  = 2;

  //          GpioCtrlRegs.GPEPUD.bit.GPIO142 = 0;             // Enable pull-up for GPIO46 (SCIRXDD)
  //          GpioCtrlRegs.GPBPUD.bit.GPIO46 = 0;              // Enable pull-up for GPIO46 (SCIRXDD)

 //           GpioCtrlRegs.GPBDIR.bit.GPIO46 = 0;              // GPIO46(SCIRXDD) is INPUT
 //           GpioCtrlRegs.GPBQSEL1.bit.GPIO46 = 3;             // Asynch input GPIO46 (SCIRXDD)
  //          GpioCtrlRegs.GPEDIR.bit.GPIO142 = 0;              // GPIO142(SCIRXDD) is OUTPUT


//1.3 RS485--SCI-B
//    1.SCIRXDB[GPIO141]    138     异步时钟     SCIRXDD外设功能I[GMUX=1][MUX=2] SCI-D接收引脚
 //    2.SCITXDB[GPIO142]    137                       SCITXDD外设功能O[GMUX=0][MUX=2] SCI-D发送引脚

            GpioCtrlRegs.GPECSEL2.bit.GPIO141 = 0;           // Select CPU1 control the  GPIO141
            GpioCtrlRegs.GPECSEL2.bit.GPIO142 = 0;           // Select CPU1 control the  GPIO142
//
            GpioCtrlRegs.GPEGMUX1.bit.GPIO141 = 1;        // Configure GPIO141 for SCIRXDD operation
            GpioCtrlRegs.GPEMUX1.bit.GPIO141  = 2;
            GpioCtrlRegs.GPEGMUX1.bit.GPIO142 = 1;        // Configure GPIO132 for SCITXDD operation
            GpioCtrlRegs.GPEMUX1.bit.GPIO142  = 2;
        //
            GpioCtrlRegs.GPEPUD.bit.GPIO141 = 0;             // Enable pull-up for GPIO141 (SCIRXDC)
            GpioCtrlRegs.GPEPUD.bit.GPIO142 = 0;             // Enable pull-up for GPIO142 (SCITXDC)
        //
            GpioCtrlRegs.GPEDIR.bit.GPIO142 = 1;              // GPIO142(SCIRXDC) is OUTPUT
            GpioCtrlRegs.GPEDIR.bit.GPIO141 = 0;              // GPIO141(SCITXDC) is INPUT
        //
            GpioCtrlRegs.GPEQSEL1.bit.GPIO141 = 3;          // Asynch input GPIO141 (SCIRXDD)

    //1.4 RS485--SCI-C
    //    1.SCIRXDC[GPIO139]   136         SCIRXDC外设功能I[GMUX=1][MUX=2] SCI-C接收引脚
    //    2.SCITXDC[GPIO140]   135          SCITXDC外设功能O[GMUX=1][MUX=2] SCI-C发送引脚
    //    3.COM_/RE3[GPIO43]               GPIO输出O[GMUX=0][MUX=0]              422接收使能引脚
    //    4.COM_DE3 [GPIO61]               GPIO输出O[GMUX=0][MUX=0]              422发送使能引脚

            GpioCtrlRegs.GPECSEL2.bit.GPIO140 = 0;           // Select CPU1 control the  GPIO140
            GpioCtrlRegs.GPECSEL2.bit.GPIO139 = 0;           // Select CPU1 control the  GPIO139
            GpioCtrlRegs.GPBCSEL2.bit.GPIO43  = 0;           // Select CPU1 control the  GPIO53
            GpioCtrlRegs.GPBCSEL4.bit.GPIO61 = 0;          // Select CPU1 control the  GPIO121

            GpioCtrlRegs.GPEGMUX1.bit.GPIO140 = 1;        // Configure GPIO140 for SCITXDC operation
            GpioCtrlRegs.GPEMUX1.bit.GPIO140  = 2;
            GpioCtrlRegs.GPEGMUX1.bit.GPIO139 = 1;        // Configure GPIO139 for SCIRXDC operation
            GpioCtrlRegs.GPEMUX1.bit.GPIO139  = 2;
            GpioCtrlRegs.GPBGMUX1.bit.GPIO43  = 0;          // Configure GPIO53 for GPIO
            GpioCtrlRegs.GPBMUX1.bit.GPIO43   = 0;
            GpioCtrlRegs.GPBGMUX2.bit.GPIO61 = 0;          // Configure GPIO133 for GPIO
            GpioCtrlRegs.GPBMUX2.bit.GPIO61  = 0;


            GpioCtrlRegs.GPEPUD.bit.GPIO140 = 0;             // Enable pull-up for GPIO140 (SCIRXDC)
            GpioCtrlRegs.GPEPUD.bit.GPIO139 = 0;             // Enable pull-up for GPIO139 (SCITXDC)
            GpioCtrlRegs.GPBPUD.bit.GPIO43  = 1;             // Disable pull-up for GPIO53
            GpioCtrlRegs.GPBPUD.bit.GPIO61 = 1;              // Disable pull-up for GPIO121

            GpioCtrlRegs.GPEDIR.bit.GPIO140 = 1;              // GPIO140(SCIRXDC) is OUTPUT
            GpioCtrlRegs.GPEDIR.bit.GPIO139 = 0;              // GPIO139(SCITXDC) is INPUT
            GpioCtrlRegs.GPBDIR.bit.GPIO43  = 1;              // GPIO53  is OUTPUT
            GpioCtrlRegs.GPBDIR.bit.GPIO61 = 1;             // GPIO121  is OUTPUT

            GpioCtrlRegs.GPEQSEL1.bit.GPIO139 = 3;          // Asynch input GPIO139 (SCIRXDC)

     //1.5 RS485--SCI-B
     //    1.SCIRXDB[GPIO11]   138          SCIRXDB外设功能I[GMUX=1][MUX=2] SCI-B接收引脚
     //    2.SCITXDB[GPIO18]   137          SCITXDB外设功能O[GMUX=0][MUX=2] SCI-B发送引脚


           GpioCtrlRegs.GPACSEL2.bit.GPIO11 = 0;          // Select CPU1 control the  GPIO11
           GpioCtrlRegs.GPACSEL3.bit.GPIO18 = 0;          // Select CPU1 control the  GPIO18


           GpioCtrlRegs.GPAGMUX1.bit.GPIO11 = 1;          // Configure GPIO11 for SCIRXDC operation
           GpioCtrlRegs.GPAMUX1.bit.GPIO11  = 2;
           GpioCtrlRegs.GPAGMUX2.bit.GPIO18 = 0;          // Configure GPIO18 for SCITXDC operation
           GpioCtrlRegs.GPAMUX2.bit.GPIO18  = 2;



           GpioCtrlRegs.GPAPUD.bit.GPIO11 = 0;             // Enable pull-up for GPIO11 (SCIRXDC)
           GpioCtrlRegs.GPAPUD.bit.GPIO18 = 0;             // Enable pull-up for GPIO18 (SCITXDC)


           GpioCtrlRegs.GPADIR.bit.GPIO11 = 0;              // GPIO140(SCIRXDC) is INPUT
           GpioCtrlRegs.GPADIR.bit.GPIO18 = 1;              // GPIO139(SCITXDC) is OUTPUT


           GpioCtrlRegs.GPAQSEL1.bit.GPIO11 = 3;          // Asynch input GPIO11 (SCIRXDC)





    //1.6 CANA

    //    1.CANTXA[GPIO19]   17                     外设功能O[GMUX=0][MUX=3] CANTXA发送引脚
    //    2.CANRXA[GPIO62]   18                     外设功能I[GMUX=1][MUX=2] CANRXA接收引脚
            GpioCtrlRegs.GPACSEL3.bit.GPIO19 = 0;        // Select CPU1 control the  GPIO19
            GpioCtrlRegs.GPBCSEL4.bit.GPIO62 = 0;        // Select CPU1 control the  GPIO62

            GpioCtrlRegs.GPAGMUX2.bit.GPIO19 = 0;        // Configure GPIO19 for CANTXA operation
            GpioCtrlRegs.GPAMUX2.bit.GPIO19  = 3;
            GpioCtrlRegs.GPBGMUX2.bit.GPIO62 = 1;        // Configure GPIO62 for CANRXA operation
            GpioCtrlRegs.GPBMUX2.bit.GPIO62  = 2;

            GpioCtrlRegs.GPAPUD.bit.GPIO19 = 0;          // Enable pull-up for  GPIO19 (CANTXA)
            GpioCtrlRegs.GPBPUD.bit.GPIO62 = 1;          // Disable pull-up for GPIO62 (CANRXA)

            GpioCtrlRegs.GPADIR.bit.GPIO19 = 1;          // GPIO19(CANTXA) is OUTPUT
            GpioCtrlRegs.GPBDIR.bit.GPIO62 = 0;          // GPIO62(CANRXA) is INPUT
            GpioCtrlRegs.GPBQSEL2.bit.GPIO62 = 3;        // Input qualification type:0,0 Sync
                                                                                   //0,1 Qualification (3 samples)
                                                                                   //1,0 Qualification (6 samples)
      //1.7 CANB

      //    1.CANTXB[GPIO16]                         外设功能O[GMUX=0][MUX=3] CANTXB发送引脚
      //    2.CANRXB[GPIO10]                         外设功能I[GMUX=1][MUX=2] CANRXB接收引脚
            GpioCtrlRegs.GPACSEL3.bit.GPIO16 = 0;        // Select CPU1 control the  GPIO16
            GpioCtrlRegs.GPACSEL2.bit.GPIO10 = 0;        // Select CPU1 control the  GPIO10

            GpioCtrlRegs.GPAGMUX2.bit.GPIO16 = 0;        // Configure GPIO16 for CANTXB operation
            GpioCtrlRegs.GPAMUX2.bit.GPIO16  = 2;
            GpioCtrlRegs.GPAGMUX1.bit.GPIO10 = 0;        // Configure GPIO10 for CANRXB operation
            GpioCtrlRegs.GPAMUX1.bit.GPIO10  = 2;

            GpioCtrlRegs.GPAPUD.bit.GPIO16 = 0;          // Enable pull-up for  GPIO16 (CANTXB)
            GpioCtrlRegs.GPAPUD.bit.GPIO10 = 1;          // Disable pull-up for GPIO10 (CANRXB)

            GpioCtrlRegs.GPADIR.bit.GPIO16 = 1;          // GPIO16(CANTXB) is OUTPUT
            GpioCtrlRegs.GPADIR.bit.GPIO10 = 0;          // GPIO10(CANRXB) is INPUT
            GpioCtrlRegs.GPAQSEL1.bit.GPIO10 = 3;        // Input qualification type:ASync
                                                                                           //0,1 Qualification (3 samples)
                                                                                           //1,0 Qualification (6 samples)
                                                                                           // 1,1 Async (no Sync or Qualification)                                                                                   // 1,1 Async (no Sync or Qualification)
     //1.8 SPI----同步串口，接到SPI-C口---用422小板，直接三根线https://pro.lceda.cn/editor?cll=warn#
     //    1.MSPIMOC[GPIO100]    xx                外设功能O[GMUX=1][MUX=2] 主机出，从机输入出
     //    2.MSPIMIC[GPIO101]    56                外设功能I[GMUX=1][MUX=2] 主机入，从机输出
     //    3.CLOCK  [GPIO102]    55                外设功能O[GMUX=1][MUX=2] SPI-C 的时钟输出
     //    4.SPISTC [GPIO103]                      外设功能O[GMUX=0][MUX=0]，SPISTC模式，控制从机数据输出
     //                                            GPIO输出[GMUX=0][MUX=0]，GPIO模式，控制从机数据输出
     //    4.1 SAMPLE [GPIO53]                      外设功能O[GMUX=1][MUX=2]，SPISTC模式，控制解码芯片锁存数据
     //    5.SAMPLE_EN [GPIO111]                      GPIO输出[GMUX=0][MUX=0]，GPIO模式，解码芯片锁存数据信号使能
     //    6.COM_/RE4[GPIO30]    57                GPIO输出O[GMUX=0][MUX=0]   SPI-C 接收使能引脚
     //    7.COM_DE4 [GPIO17]    58                GPIO输出O[GMUX=0][MUX=0]   SPI-C 输出时钟使能引脚

           GpioCtrlRegs.GPDCSEL1.bit.GPIO100 = 0;           // Select CPU1 control the  GPIO100
           GpioCtrlRegs.GPDCSEL1.bit.GPIO101 = 0;           // Select CPU1 control the  GPIO101
           GpioCtrlRegs.GPDCSEL1.bit.GPIO102 = 0;           // Select CPU1 control the  GPIO102
           GpioCtrlRegs.GPDCSEL1.bit.GPIO103 = 0;           // Select CPU1 control the  GPIO103
           GpioCtrlRegs.GPBCSEL3.bit.GPIO53 = 0;           // Select CPU1 control the  GPIO53
           GpioCtrlRegs.GPDCSEL2.bit.GPIO111 = 0;           // Select CPU1 control the  GPIO111

           GpioCtrlRegs.GPACSEL4.bit.GPIO30 = 0;           // Select CPU1 control the  GPIO30
           GpioCtrlRegs.GPACSEL3.bit.GPIO17 = 0;           // Select CPU1 control the  GPIO17

           GpioCtrlRegs.GPDGMUX1.bit.GPIO100 = 1;        // Configure GPIO101 for MSPIMOC operation
           GpioCtrlRegs.GPDMUX1.bit.GPIO100  = 2;
           GpioCtrlRegs.GPDGMUX1.bit.GPIO101 = 1;        // Configure GPIO101 for MSPIMIC operation
           GpioCtrlRegs.GPDMUX1.bit.GPIO101  = 2;
           GpioCtrlRegs.GPDGMUX1.bit.GPIO102 = 1;        // Configure GPIO102 for SPI-C-CLOCK operation
           GpioCtrlRegs.GPDMUX1.bit.GPIO102  = 2;
           GpioCtrlRegs.GPDGMUX1.bit.GPIO103 = 1;        // Configure GPIO103 for SPISTC operation
           GpioCtrlRegs.GPDMUX1.bit.GPIO103  = 2;
           GpioCtrlRegs.GPBGMUX2.bit.GPIO53 = 1;        // Configure GPIO53 for SPISTC operation
           GpioCtrlRegs.GPBMUX2.bit.GPIO53  = 2;
            //            GpioCtrlRegs.GPDGMUX1.bit.GPIO103 = 0;        // Configure GPIO103 for GPIO operation
            //            GpioCtrlRegs.GPDMUX1.bit.GPIO103  = 0;

           GpioCtrlRegs.GPDGMUX1.bit.GPIO111 = 0;        // Configure GPIO111 for GPIO operation
           GpioCtrlRegs.GPDMUX1.bit.GPIO111  = 0;

           GpioCtrlRegs.GPAGMUX2.bit.GPIO30  = 0;          // Configure GPIO30 for GPIO
           GpioCtrlRegs.GPAMUX2.bit.GPIO30   = 0;
           GpioCtrlRegs.GPAGMUX2.bit.GPIO17  = 0;          // Configure GPIO17 for GPIO
           GpioCtrlRegs.GPAMUX2.bit.GPIO17   = 0;

           GpioCtrlRegs.GPDPUD.bit.GPIO100 = 0;             // Enable pull-up for GPIO100 (MSPIMOC)
           GpioCtrlRegs.GPDPUD.bit.GPIO101 = 0;             // Enable pull-up for GPIO101 (MSPIMIC)
           GpioCtrlRegs.GPDPUD.bit.GPIO102 = 0;             // Enable pull-up for GPIO102 (CLOCK)
           GpioCtrlRegs.GPDPUD.bit.GPIO103 = 0;             // Enable pull-up for GPIO103 (SPITC)
           GpioCtrlRegs.GPBPUD.bit.GPIO53 = 0;             // Enable pull-up for GPIO53 (SPITC)
           GpioCtrlRegs.GPDPUD.bit.GPIO111 = 0;             // Enable pull-up for GPIO111

           GpioCtrlRegs.GPAPUD.bit.GPIO30  = 1;             // Disable pull-up for GPIO30
           GpioCtrlRegs.GPAPUD.bit.GPIO17  = 1;            // Disable pull-up for GPIO17

           GpioCtrlRegs.GPDDIR.bit.GPIO100 = 1;              // GPIO101(MSPIMIC) is OUTPUT
           GpioCtrlRegs.GPDDIR.bit.GPIO101 = 0;              // GPIO101(MSPIMIC) is INPUT
           GpioCtrlRegs.GPDDIR.bit.GPIO102 = 1;              // GPIO102(CLOCK) is OUTPUT
           GpioCtrlRegs.GPDDIR.bit.GPIO103 = 1;              // GPIO103 (SPITC) is OUTPUT
           GpioCtrlRegs.GPBDIR.bit.GPIO53 = 1;              // GPIO53 (SPITC) is OUTPUT
           GpioCtrlRegs.GPDDIR.bit.GPIO111 = 1;              // GPIO111 is OUTPUT

           GpioCtrlRegs.GPADIR.bit.GPIO30  = 1;              // GPIO30  is OUTPUT
           GpioCtrlRegs.GPADIR.bit.GPIO17  = 1;              // GPIO17  is OUTPUT

           GpioCtrlRegs.GPDQSEL1.bit.GPIO101 = 3;          // Asynch input GPIO101 (MSPIMIC)


           GpioDataRegs.GPACLEAR.bit.GPIO30=1;   //  设置RE4为低
           GpioDataRegs.GPASET.bit.GPIO17=1;   //  设置DE4为高

           EDIS;

           SAMPLE_DIS;//旋变解码芯片采样禁止


    //二、开关量输入输出
    //2.1输入
    //    1.SW1[GPIO44]  114        GPIO输入I[GMUX=0][MUX=0]     外部隔离输入
    //
    //    3.DriverFLT1[GPIO38]  1122      GPIO输入I[GMUX=0][MUX=0]     外部输入,驱动器1故障
    //    4.DriverFLT2[GPIO39]  120       GPIO输入I[GMUX=0][MUX=0]     外部输入，驱动器2故障
    //    5.DriverFLT3[GPIO40]  118       GPIO输入I[GMUX=0][MUX=0]     外部输入 ，驱动器3故障
    //    6.PreCh1FB[GPIO49]    104       GPIO输入I[GMUX=0][MUX=0]     外部输入，继电器1反馈
    //    7.PreCh2FB[GPIO50]    102       GPIO输入I[GMUX=0][MUX=0]     外部输入 ，继电器2反馈
    //    8.Fault_All[GP119]    140       GPIO输入I[GMUX=0][MUX=0]     外部输入 ，总故障

    //    9.NTC1-Error[GPIO87]             GPIO输入I[GMUX=0][MUX=0]    外部输入 ，NTC1故障
    //    10.NTC2-Error[GPIO86]            GPIO输入I[GMUX=0][MUX=0]    外部输入 ，NTC2故障

    //    13.VBus-Error[GPIO129]         GPIO输入I[GMUX=0][MUX=0]       外部输入,母线过压故障
    //    14.IBus-Error[GPIO35]          GPIO输入I[GMUX=0][MUX=0]      外部输入，母线过流故障
    //    15.Ia-Error[GPIO65]          GPIO输入I[GMUX=0][MUX=0]       外部输入 ，A相过流故障
    //    16.Ib-Error[GPIO0]            GPIO输入I[GMUX=0][MUX=0]      外部输入，B相过流故障
    //    17.Ic-Error[GPIO122]            GPIO输入I[GMUX=0][MUX=0]      外部输入 ，C相过流故障

    //    18.PreColdFB[GPIO87]            GPIO输入I[GMUX=0][MUX=0]     外部输入，水冷系统反馈
    //    19.PreK1FB[GPIO45]              GPIO输入I[GMUX=0][MUX=0]      外部输入 ，K1继电器启动信号---主功率总开关





            //
            EALLOW;
    //输入
           // GPIO_SetupPinOptions(106, GPIO_INPUT, GPIO_QUAL6);
           // GPIO_SetupPinMux(106, GPIO_MUX_CPU1, 0);
            GpioCtrlRegs.GPBCSEL2.bit.GPIO44 = 0;           // Select CPU1 control the  GPIO44
            //GpioCtrlRegs.GPBCSEL2.bit.GPIO41 = 0;           // Select CPU1 control the  GPIO41

            //GpioCtrlRegs.GPBCSEL2.bit.GPIO46 = 0;           // Select CPU1 control the  GPIO46

            GpioCtrlRegs.GPBGMUX1.bit.GPIO44 = 0;  //
            GpioCtrlRegs.GPBMUX1.bit.GPIO44 = 0;
            GpioCtrlRegs.GPBDIR.bit.GPIO44 = 0;   // GPIO44 = input
            GpioCtrlRegs.GPBPUD.bit.GPIO44 = 0;   // 上拉
            GpioCtrlRegs.GPBCTRL.bit.QUALPRD1 =2; //采样周期=4*Tsysckout
            GpioCtrlRegs.GPBQSEL1.bit.GPIO44 = 2; // 6个采样周期宽度限制

//            GpioCtrlRegs.GPBGMUX1.bit.GPIO41 = 0;  //
//            GpioCtrlRegs.GPBMUX1.bit.GPIO41 = 0;
//            GpioCtrlRegs.GPBDIR.bit.GPIO41 = 0;   // GPIO41 = input
//            GpioCtrlRegs.GPBPUD.bit.GPIO41 = 0;   // 上拉
//            GpioCtrlRegs.GPBCTRL.bit.QUALPRD1 =2; //采样周期=4*Tsysckout
//            GpioCtrlRegs.GPBQSEL1.bit.GPIO41 = 2; // 6个采样周期宽度限制

            GPIO_SetupPinOptions(38, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(38, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(39, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(39, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(40, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(40, GPIO_MUX_CPU1, 0);

            GPIO_SetupPinOptions(49, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(49, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(50, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(50, GPIO_MUX_CPU1, 0);

            GPIO_SetupPinOptions(119, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(119, GPIO_MUX_CPU1, 0);


            GPIO_SetupPinOptions(87, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(87, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(86, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(86, GPIO_MUX_CPU1, 0);

            //    13.VBus-Error[GPIO129]         GPIO输入I[GMUX=0][MUX=0]       外部输入,母线过压故障
            //    14.IBus-Error[GPIO35]          GPIO输入I[GMUX=0][MUX=0]      外部输入，母线过流故障
            //    15.Ia-Error[GPIO65]          GPIO输入I[GMUX=0][MUX=0]       外部输入 ，A相过流故障
            //    16.Ib-Error[GPIO0]            GPIO输入I[GMUX=0][MUX=0]      外部输入，B相过流故障
            //    17.Ic-Error[GPIO122]            GPIO输入I[GMUX=0][MUX=0]      外部输入 ，C相过流故障

            GPIO_SetupPinOptions(129, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(129, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(35, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(35, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(65, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(65, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(0, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(0, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(122, GPIO_INPUT, GPIO_QUAL3);
            GPIO_SetupPinMux(122, GPIO_MUX_CPU1, 0);


//            GPIO_SetupPinOptions(87, GPIO_INPUT, GPIO_QUAL3);
//            GPIO_SetupPinMux(87, GPIO_MUX_CPU1, 0);
//            GPIO_SetupPinOptions(45, GPIO_INPUT, GPIO_QUAL3);
//            GPIO_SetupPinMux(45, GPIO_MUX_CPU1, 0);


            EDIS;




//2.2输出

    //    2.RST1[GPIO48]           GPIO输出O[GMUX=0][MUX=0]     复位控制信号：  1-不输出   0-输出
    //    3.PreCH1[GPIO51]         GPIO输出O[GMUX=0][MUX=0]     继电器1控制信号：  1-不输出   0-输出
    //    4.PreCH2[GPIO52]         GPIO输出O[GMUX=0][MUX=0]     继电器2控制信号：  1-不输出   0-输出
    //    5.CPLD_FLT_CLR[GPIO28]   GPIO输出O[GMUX=0][MUX=0]     CPLD故障清零：  1-不输出   0-输出
    //    6.SCI_BOOT_EN [GPIO143]  GPIO输出O[GMUX=0][MUX=0]     BOOT使能：  1-不输出   0-输出
    //    7.PreCold[GPIO86]        GPIO输出O[GMUX=0][MUX=0]     冷却电源控制：  1-不输出   0-输出
    //输出
            EALLOW;

//            GpioCtrlRegs.GPBGMUX1.bit.GPIO46 = 0;  //
//            GpioCtrlRegs.GPBMUX1.bit.GPIO46 = 0;
//            GpioCtrlRegs.GPBDIR.bit.GPIO46 = 1;   // GPIO46 = output
//            GpioCtrlRegs.GPBPUD.bit.GPIO46 = 0;   // 上拉
            GPIO_SetupPinOptions(48, GPIO_OUTPUT, GPIO_PULLUP);
            GPIO_SetupPinMux(48, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(51, GPIO_OUTPUT, GPIO_PULLUP);
            GPIO_SetupPinMux(51, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(52, GPIO_OUTPUT, GPIO_PULLUP);
            GPIO_SetupPinMux(52, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(28, GPIO_OUTPUT, GPIO_PULLUP);
            GPIO_SetupPinMux(28, GPIO_MUX_CPU1, 0);
            GPIO_SetupPinOptions(143, GPIO_OUTPUT, GPIO_PULLUP);
            GPIO_SetupPinMux(143, GPIO_MUX_CPU1, 0);
           // GPIO_SetupPinOptions(86, GPIO_OUTPUT, GPIO_PULLUP);
            //GPIO_SetupPinMux(86, GPIO_MUX_CPU1, 0);

            EDIS;



// 三、PWM  输出
       //1.PWM1A[145]----- PWM1A输出O[GMUX=0][MUX=01]
       //2.PWM1B[146]----- PWM1B输出O[GMUX=0][MUX=01]
       //3.PWM2A[147]----- PWM2A输出O[GMUX=0][MUX=01]
       //4.PWM2B[148]----- PWM2B输出O[GMUX=0][MUX=01]
       //5.PWM3A[149]----- PWM3A输出O[GMUX=0][MUX=01]
       //6.PWM3B[150]----- PWM3B输出O[GMUX=0][MUX=01]

       //7.PWM4A[151]----- PWM4A输出O[GMUX=0][MUX=01]----用作DA
       //8.PWM4A[152]----- PWM4B输出O[GMUX=0][MUX=01]----用作DA

            EALLOW;

            GpioCtrlRegs.GPECSEL3.bit.GPIO145 = 0;           // Select CPU1 control the  GPIO145
            GpioCtrlRegs.GPECSEL3.bit.GPIO146 = 0;           // Select CPU1 control the  GPIO146
            GpioCtrlRegs.GPECSEL3.bit.GPIO147 = 0;           // Select CPU1 control the  GPIO147
            GpioCtrlRegs.GPECSEL3.bit.GPIO148 = 0;           // Select CPU1 control the  GPIO148
            GpioCtrlRegs.GPECSEL3.bit.GPIO149 = 0;           // Select CPU1 control the  GPIO149
            GpioCtrlRegs.GPECSEL3.bit.GPIO150 = 0;           // Select CPU1 control the  GPIO150
            GpioCtrlRegs.GPECSEL3.bit.GPIO151 = 0;           // Select CPU1 control the  GPIO151
            GpioCtrlRegs.GPECSEL4.bit.GPIO152 = 0;           // Select CPU1 control the  GPIO152


            GpioCtrlRegs.GPEPUD.bit.GPIO145 = 1;    // Disable pull-up on GPIO145 (EPWM1A)
            GpioCtrlRegs.GPEPUD.bit.GPIO146 = 1;    // Disable pull-up on GPIO146 (EPWM1B)
            GpioCtrlRegs.GPEGMUX2.bit.GPIO145 = 0;   // Configure GPIO145 as EPWM1A
            GpioCtrlRegs.GPEMUX2.bit.GPIO145 = 1;   // Configure GPIO145 as EPWM1A
            GpioCtrlRegs.GPEGMUX2.bit.GPIO146 = 0;   // Configure GPIO145 as EPWM1B
            GpioCtrlRegs.GPEMUX2.bit.GPIO146 = 1;   // Configure GPIO146 as EPWM1B

            GpioCtrlRegs.GPEPUD.bit.GPIO147 = 1;    // Disable pull-up on GPIO147 (EPWM2A)
            GpioCtrlRegs.GPEPUD.bit.GPIO148 = 1;    // Disable pull-up on GPIO148 (EPWM2B)
            GpioCtrlRegs.GPEGMUX2.bit.GPIO147 = 0;   // Configure GPIO145 as EPWM1A
            GpioCtrlRegs.GPEMUX2.bit.GPIO147 = 1;   // Configure GPIO147 as EPWM2A
            GpioCtrlRegs.GPEGMUX2.bit.GPIO148 = 0;   // Configure GPIO148 as EPWM2B
            GpioCtrlRegs.GPEMUX2.bit.GPIO148 = 1;   // Configure GPIO148 as EPWM2B

            GpioCtrlRegs.GPEPUD.bit.GPIO149 = 1;    // Disable pull-up on GPIO149 (EPWM3A)
            GpioCtrlRegs.GPEPUD.bit.GPIO150 = 1;    // Disable pull-up on GPIO150 (EPWM3B)
            GpioCtrlRegs.GPEGMUX2.bit.GPIO149 = 0;   // Configure GPIO149 as EPWM3A
            GpioCtrlRegs.GPEMUX2.bit.GPIO149 = 1;   // Configure GPIO149 as EPWM3A
            GpioCtrlRegs.GPEGMUX2.bit.GPIO150 = 0;   // Configure GPIO150 as EPWM3B
            GpioCtrlRegs.GPEMUX2.bit.GPIO150 = 1;   // Configure GPIO150 as EPWM3B


            GpioCtrlRegs.GPEPUD.bit.GPIO151 = 1;    // Disable pull-up on GPIO151 (EPWM4A)
            GpioCtrlRegs.GPEPUD.bit.GPIO152 = 1;    // Disable pull-up on GPIO152 (EPWM4B)
            GpioCtrlRegs.GPEGMUX2.bit.GPIO151 = 0;   // Configure GPIO151 as EPWM4A
            GpioCtrlRegs.GPEMUX2.bit.GPIO151 = 1;   // Configure GPIO151 as EPWM4A
            GpioCtrlRegs.GPEGMUX2.bit.GPIO152 = 0;   // Configure GPIO152 as EPWM4B
            GpioCtrlRegs.GPEMUX2.bit.GPIO152 = 1;   // Configure GPIO152 as EPWM4B

            InputXbarRegs.INPUT1SELECT= 0x77;//GPIO 119, Fault_All--  TripZone 1


            EDIS;

            Pre_CH1_OFF=1;//继电器K1断开
            Pre_CH2_OFF=1;//继电器K2断开
            RST1_HIGH=1;//驱动器复位引脚为高
            RX1_Mode_485;
            Dis_TX1_Mode_485;

            RX3_Mode_485;
            Dis_TX3_Mode_485;
            RX4_Mode_SSI;
            TX4_Mode_SSI;
            SCI_BOOT_DIS;//输出高，不使能SCI boot 模式
}


//=======================================================
//函数名称: INIT_TIMER0()
//功能:  初始化定时器0
//输入:
//返回:
//备注:
//TIMER1TIM     0x0C00  计数寄存器
//TIMER1TIMH    0x0C01  计数寄存器
//TIMER1PRD     0x0C02  周期寄存器
//TIMER1PRDH    0x0C03  周期寄存器
//TIMER1TCR     0x0C04  控制寄存器
//Reserved      0x0C05
//TIMER1TPR     0x0C06  预分频器
//TIMER1TPRH    0x0C07  预分频寄存器为高
//=======================================================
void INIT_TIMER0(void)
{
    //-----------------------------------------------------------------------
    CpuTimer0Regs.TIM.all = 0u;             // 清零CPU定时器计数寄存器

    CpuTimer0Regs.PRD.all = mVar_RAM.TIMER0_PRD;     // CPU定时器周期值500ms

    CpuTimer0Regs.TCR.bit.TSS   = 1u;       // CPU定时器运行停止控制位: 0=正在运行；1=停止运行。
    CpuTimer0Regs.TCR.bit.TRB   = 1u;       // 写1时定时器重新加载PRDH：PRD中的值，并且预分频计数器（PSCH：PSC）加载定时器分频的值。
    CpuTimer0Regs.TCR.bit.TIF   = 1u;       // 清除CPU定时器中断标志位，当CPU定时器计数到0时自动置位,写1清除该标志位,写0无效。

    CpuTimer0Regs.TCR.bit.TIE   = 0u;       // 禁用CPU定时器中断。

    CpuTimer0Regs.TCR.bit.SOFT  = 0u;       // 0 = TIMH：TIM下一次递减时，定时器将停止。
    CpuTimer0Regs.TCR.bit.FREE  = 0u;       // 0 = 由SOFT位控制仿真行为。

    CpuTimer0Regs.TPR.bit.TDDR  = 0u;       // CPU定时器预分频（请将预分频值-1写入TDDRH：TDDR）
    CpuTimer0Regs.TPRH.bit.TDDRH= 0u;       // 重新加载PRDH：PRD中的值，并且预分频计数器（PSCH：PSC）加载定时器分频的值。
    //CpuTimer1Regs.TPR.bit.PSC;   （只读位）CPU定时器预分频计数器低8位。
    //CpuTimer1Regs.TPRH.bit.PSCH; （只读位）CPU定时器预分频计数器高8位。
    //-----------------------------------------------------------------------
}

//=======================================================
//函数名称: INIT_TIMER1()
//功能:  初始化定时器1
//输入:
//返回:
//备注:
//TIMER1TIM     0x0C00  计数寄存器
//TIMER1TIMH    0x0C01  计数寄存器
//TIMER1PRD     0x0C02  周期寄存器
//TIMER1PRDH    0x0C03  周期寄存器
//TIMER1TCR     0x0C04  控制寄存器
//Reserved      0x0C05
//TIMER1TPR     0x0C06  预分频器
//TIMER1TPRH    0x0C07  预分频寄存器为高
//=======================================================
void INIT_TIMER1(void)
{
    //-----------------------------------------------------------------------
    CpuTimer1Regs.TIM.all = 0u;             // 清零CPU定时器计数寄存器

    CpuTimer1Regs.PRD.all = mVar_RAM.TIMER1_PRD;     // CPU定时器周期值500ms

    CpuTimer1Regs.TCR.bit.TSS   = 1u;       // CPU定时器运行停止控制位: 0=正在运行；1=停止运行。
    CpuTimer1Regs.TCR.bit.TRB   = 1u;       // 写1时定时器重新加载PRDH：PRD中的值，并且预分频计数器（PSCH：PSC）加载定时器分频的值。
    CpuTimer1Regs.TCR.bit.TIF   = 1u;       // 清除CPU定时器中断标志位，当CPU定时器计数到0时自动置位,写1清除该标志位,写0无效。

    CpuTimer1Regs.TCR.bit.TIE   = 0u;       // 禁用CPU定时器中断。

    CpuTimer1Regs.TCR.bit.SOFT  = 0u;       // 0 = TIMH：TIM下一次递减时，定时器将停止。
    CpuTimer1Regs.TCR.bit.FREE  = 0u;       // 0 = 由SOFT位控制仿真行为。

    CpuTimer1Regs.TPR.bit.TDDR  = 0u;       // CPU定时器预分频（请将预分频值-1写入TDDRH：TDDR）
    CpuTimer1Regs.TPRH.bit.TDDRH= 0u;       // 重新加载PRDH：PRD中的值，并且预分频计数器（PSCH：PSC）加载定时器分频的值。
    //CpuTimer1Regs.TPR.bit.PSC;   （只读位）CPU定时器预分频计数器低8位。
    //CpuTimer1Regs.TPRH.bit.PSCH; （只读位）CPU定时器预分频计数器高8位。
    //-----------------------------------------------------------------------
}



//=======================================================
//                End of file.
//=======================================================
