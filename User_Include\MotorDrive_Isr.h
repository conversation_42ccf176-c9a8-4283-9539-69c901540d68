//=======================================================
//    Project：         MMC_PET_CPU1
//    File:                 MMC_PET_Isr.h
//    Created on:     2021
//    By：                GCY
//    Describe：
//=======================================================

#ifndef USER_INCLUDE_MOTORDRIVE_ISR_H_
#define USER_INCLUDE_MOTORDRIVE_ISR_H_

#ifdef __cplusplus
extern "C"
{
#endif

//=============================================================================
// Includes and Defines
//=============================================================================
//  Includes
#include "F28x_Project.h"

    // Defines

    //
    // #define Ld      0.0005016
    // #define Lq      0.0005799
    // #define flux    0.12
    // #define Rs      0.15
    // #define Pn      6

    //=============================================================================
    //  Typdefs and Externs
    //=============================================================================
    //  Typdefs

    //  Global Variable

    typedef struct
    {
        Uint16 MotorPosition;
        Uint16 ResovlerFault;
        Uint16 mAngle;   // 角度数据，二进制码
        Uint16 Reserved; // Align(4)
        float mEtheta;   // 旋变输出电角度
        float mEtheta1;
        float mEthetaAVG;
        float mEthetaRad;  // 旋变输出电角度弧度
        float mEthetaZero; // 旋变零点角度

        float Vdd_P_3V3;
        float mPT1;
        float mPT2;
        float mPT3;
        float mPT4;
        float mPT5;
        float Vdd_P_5V;
        float mNTC1;
        float mNTC2;
        float mTempNTC1; // 温度值
        float mTempNTC2;
        float Ia;
        float Ib;
        float Ic;
        float Vbus;         // 逆变器电容电压，滤波比较多
        float Vbus_In;      // 电池输入电压
        float Vbus_protect; // 保护用少滤波母线电压
        float Vbus_filter;  // Vbus的进一步滤波
        float Ibus;         // 逆变器输入电流
        float Set_Speed_Ref;
        float Last_Set_Speed_Ref;
        float RampSet_Speed_Ref;

        float mSpeed;
        float mDuty;
        float we;

        float mTe_ref;

        float Va; // phase voltage
        float Vb;
        float Vc;

        float Iset_q_ref;
        float Te_set_ref; //扭矩目标值，单位Nm
        float inv_Kt; // 扭矩常数倒数
        float Kt_inv; // 扭矩常数倒数
    } MOTOR_CONTROL_VARIABLES;

    extern MOTOR_CONTROL_VARIABLES MCV;

    // ADCA: 0:A0---xxxx    1:A1---xxxx   2:A2---xxxx    3:A3----15V   4:A4---+15V     5:A5---+3.3V  e:C14---PT1; f:C15---PT2
    // ADCB: 0:B0---5V      1:B1---WC2    2:B2---WC3     3:B3---WC1    4:B4---NTC1     5:B5---NTC2
    // ADCC: 0:xx---xxxx    1:xx---xxxx   2:C2---NTC3    3:C3---NTC4   4:C4---Ic       5:C5---IcPeak
    // ADCD: 0:D0---Ib      1:D1---IbPeak 2:D2---IaPeak  3:D3---Ia     4:D4---Vbus     5:D5---Ibus

    extern Uint16 RotTX_ZeroEN_FLG;
    extern Uint16 SYS_OpertionMode; // 模式
    extern Uint16 SYS_RUN_RSEN;     // 上位机使能位

    // average filter structure, output is the sum of all the past input data
    typedef struct
    {
        uint16_t filter_order;    // How many data will be added together
        uint16_t *pfilter_buffer; // pointer of the input data buffer
        uint16_t filter_index;    // filter data index, minus old data first and then add new data at pfilter_buffer[index]
        uint32_t filter_output;   // filter data output, the sum of all the past input data
    } AVG_u16;

    typedef struct
    {
        Uint16 filter_order;    // How many data will be added together
        float filter_order_inv; // 1/order, for calculate the output value
        float *pfilter_buffer;  // pointer of the input data buffer
        Uint16 filter_index;    // filter data index, minus old data first and then add new data at pfilter_buffer[index]
        float filter_sum;       // the sum of all the past input data
        float filter_output;    // filter data output, filter_order_inv*filter_sum
    } AVG_f32;

    typedef struct
    {
        float mTe_ref; // Input,mTe reference
        float Idref;   // Output, Idref
        float Iqref;   // Outpu, Iqref

    } MTPA_F;

    extern MTPA_F MTPA_Idq1;

    extern Uint16 FaultFlag;     // 错误标志位
    extern Uint16 Enable;        // 使能标志位
    extern Uint16 DCtoDCFLTFlag; // DC-DC模块故障标志
    extern Uint32 CANTimeOutCNT;
    extern Uint32 CANBTimeOutCNT;
    extern Uint16 PWMEnFlg;
    extern Uint16 ROTTX_ZERO_PulseCNT;
    extern Uint16 FaultDataLogEnable;
    extern Uint16 Fault_temp1_Flag, Fault_temp2_Flag, Fault_temp3_Flag, Fault_temp4_Flag;
    extern Uint16 CPLDFaultFlag, UVDCFaultFlag, OCIbusFaultFlag, OCIarmPFaultFlag, OCIarmNFaultFlag, OCIacFaultFlag;
    extern Uint16 OVACFaultFlag_Vab, OOVDCFaultFlagVACFaultFlag_Vac, OVACFaultFlag_Vbc, OCIarmPFaultFlag_aP, OCIarmPFaultFlag_aN, OCIarmPFaultFlag_bP, OCIarmPFaultFlag_bN;
    extern Uint16 OCFaultFlag_Ia, OCFaultFlag_Ib, OCFaultFlag_Ic;
    extern Uint16 OTFaultFlag_DRV1, OTFaultFlag_DRV2, OTFaultFlag_DRV3;
    extern Uint16 OTFaultFlag_PT1, OTFaultFlag_PT2, OTFaultFlag_PT3, OTFaultFlag_PT4, OTFaultFlag_PT5;
// Realy//
#define Pre_CH1_ON GpioDataRegs.GPBSET.bit.GPIO51
#define Pre_CH1_OFF GpioDataRegs.GPBCLEAR.bit.GPIO51
#define Pre_CH1_Status GpioDataRegs.GPBDAT.bit.GPIO51
#define Pre_CH2_ON GpioDataRegs.GPBSET.bit.GPIO52
#define Pre_CH2_OFF GpioDataRegs.GPBCLEAR.bit.GPIO52
#define Pre_CH2_Status GpioDataRegs.GPBDAT.bit.GPIO52
#define RST1_HIGH GpioDataRegs.GPBCLEAR.bit.GPIO48 // 光纤驱动器采用75452B，反逻辑。
#define RST1_LOW GpioDataRegs.GPBSET.bit.GPIO48    // 光纤驱动器采用75452B，反逻辑。

    // #define FPGA_RESET_OFF  GpioDataRegs.GPASET.bit.GPIO4
    // #define FPGA_RESET_EN GpioDataRegs.GPACLEAR.bit.GPIO4

    // #define Fault_LED_ON    GpioDataRegs.GPDSET.bit.GPIO124
    // #define Fault_LED_OFF   GpioDataRegs.GPDCLEAR.bit.GPIO124

    // #define CPLD_FLT_FLG_ENABLE  GpioDataRegs.GPBSET.bit.GPIO92
    // #define CPLD_FLT_FLG_CLEAR   GpioDataRegs.GPBCLEAR.bit.GPIO92

#define CPLD_Fault_All_FLG (GpioDataRegs.GPDDAT.bit.GPIO119) // CPLD故障反馈

    void AVG_FILTER_init(AVG_f32 *avg_filter, float init_value, Uint16 order, float *pbuf);

    inline void AVG_FILTER_calc(AVG_f32 *avg_filter, float newdata)
    {
        // minus old data first
        avg_filter->filter_sum -= avg_filter->pfilter_buffer[avg_filter->filter_index];
        // store new data
        avg_filter->pfilter_buffer[avg_filter->filter_index] = newdata;
        // add new data
        avg_filter->filter_sum += newdata;

        avg_filter->filter_output = avg_filter->filter_sum * avg_filter->filter_order_inv;
        // modify index
        avg_filter->filter_index++;
        avg_filter->filter_index %= avg_filter->filter_order;
    }

//=============================================================================
//  Function Prototypes
//=============================================================================
#ifdef _FLASH
#pragma CODE_SECTION(Adca1_Isr, ".TI.ramfunc");
#endif
#pragma INTERRUPT(Adca1_Isr, HPI)
    interrupt void Adca1_Isr(void);

#ifdef _FLASH
#pragma CODE_SECTION(MCTRL_SM_Control, ".TI.ramfunc");
#pragma CODE_SECTION(MotorDriverRunControl, ".TI.ramfunc");
#pragma CODE_SECTION(PWMUpdate, ".TI.ramfunc");
#pragma CODE_SECTION(StatusDataLog, ".TI.ramfunc");
#pragma CODE_SECTION(FaultStatusDataLog, ".TI.ramfunc");
#pragma CODE_SECTION(SSIWrite, ".TI.ramfunc");
#pragma CODE_SECTION(SPIWrite, ".TI.ramfunc");
#pragma CODE_SECTION(CalRadandSpeed, ".TI.ramfunc");
#pragma CODE_SECTION(AVG_FILTER_calc, ".TI.ramfunc");
#pragma CODE_SECTION(FaultProcessing, ".TI.ramfunc");

#endif

    void MCTRL_SM_Control();
    void MotorDriverRunControl(void);
    void PWMUpdate(void);

    void MTPA_Ctrl(MTPA_F *m);
    void StatusDataLog(void);
    void FaultStatusDataLog(void); // 故障时刻状态数据保存，用于故障诊断
    void SSIWrite(Uint16 a);
    void SPIWrite(Uint16 a);
    void CalRadandSpeed(void);
    void FaultProcessing(void);
    void FaultReset(void);

#ifdef __cplusplus
}
#endif /* extern "C" */

#endif // end of MMC_PET_ISR_H_

//=======================================================
//                                                          End of file.
//=======================================================
