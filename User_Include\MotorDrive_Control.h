//=======================================================
//    Project：         MMC_PET_CPU1
//    File:                 MMC_PET_control.h
//    Created on:     2021
//    By：                GCY
//    Describe：       定义常数和变量
//=======================================================

#ifndef  USER_INCLUDE_MOTORDRIVE_CONTROL_H_
#define  USER_INCLUDE_MOTORDRIVE_CONTROL_H_

#ifdef __cplusplus
extern "C" {
#endif

//=============================================================================
// Includes and Defines
//=============================================================================
//  Includes
#include "F28x_Project.h"

// Defines

//CAN-A //
#define CANA_CheckMotorStatus1_MSG_ID          0x0c800101 // Extended  ID of the Motor driver
#define CANA_SetMotorSpeed_MSG_ID              0x0c800102 // Extended  ID of the Motor driver
#define CANA_ResetMotorError_MSG_ID            0x0c800104 // Extended  ID of the Motor driver
#define CANA_CheckMotorStatus2_MSG_ID          0x0c800103 // Extended  ID of the Motor driver
#define CANA_SetRelayOpen_MSG_ID               0x0c800105 // Extended  ID of the Motor driver
#define CANA_CheckDrvStatus_MSG_ID             0x0c800106 // Extended  ID of the Motor driver
#define CANA_Feedback1toCCU_MSG_ID             0x08002E01 // Extended ID of the central control unit
#define CANA_Feedback2toCCU_MSG_ID             0x08002E02 // Extended ID of the central control unit
#define CANA_Feedback3toCCU_MSG_ID             0x08002E03 // Extended ID of the central control unit

//Type II
#define CANA_SetASRPI_MSG_ID                   0x0c800107 // Extended  ID of the Motor driver
#define CANA_SetACR_d_PI_MSG_ID                0x0c800108 // Extended  ID of the Motor driver
#define CANA_SetACR_q_PI_MSG_ID                0x0c800109 // Extended  ID of the Motor driver
#define CANA_SetVbusIref_MSG_ID                0x0c80010A // Extended  ID of the Motor driver
#define CANA_SetControlMode_MSG_ID             0x0c80010B // Extended  ID of the Motor driver
#define CANA_DSPBOOTEN_MSG_ID                  0x0c80010C // Extended  ID of the Motor driver
#define CANA_Feedback4toCCU_MSG_ID             0x08002E04 // Extended ID of the central control unit


#define CANA_RX1_MSG_OBJ         1   //接收邮箱
#define CANA_RX2_MSG_OBJ         2   //接收邮箱
#define CANA_RX3_MSG_OBJ         3   //接收邮箱
#define CANA_RX4_MSG_OBJ         4   //接收邮箱
#define CANA_RX5_MSG_OBJ         5   //接收邮箱
#define CANA_RX6_MSG_OBJ         6   //接收邮箱
#define CANA_TX1_MSG_OBJ         7   //发送邮箱
#define CANA_TX2_MSG_OBJ         8   //发送邮箱
#define CANA_TX3_MSG_OBJ         9   //发送邮箱
//Type II
#define CANA_RX7_MSG_OBJ         10   //接收邮箱
#define CANA_RX8_MSG_OBJ         11   //接收邮箱
#define CANA_RX9_MSG_OBJ         12   //接收邮箱
#define CANA_RX10_MSG_OBJ        13   //接收邮箱
#define CANA_RX11_MSG_OBJ        14   //接收邮箱
#define CANA_RX12_MSG_OBJ        15   //接收邮箱
#define CANA_TX4_MSG_OBJ         16   //发送邮箱


//CAN-B

#define CANB_DCtoDCStatus_MSG_ID         0x10002E01 // Extended  ID of the DC-DC converter
#define AKCANB_DCtoDCStatus_MSG_ID       0x10002E02 // Extended  ID of the DC-DC converter


#define CANB_RX1_MSG_OBJ         1   //接收邮箱---结束2个字节
#define CANB_TX1_MSG_OBJ         2   //发送收邮箱---结束4个字节

#define CAN_MAX_BIT_DIVISOR     (13)   // The maximum CAN bit timing divisor
#define CAN_MIN_BIT_DIVISOR     (5)    // The minimum CAN bit timing divisor
#define CAN_MAX_PRE_DIVISOR     (1024) // The maximum CAN pre-divisor
#define CAN_MIN_PRE_DIVISOR     (1)    // The minimum CAN pre-divisor
#define CAN_BTR_BRP_M           (0x3F)
#define CAN_BTR_BRPE_M          (0xF0000)
#define CAN_MSG_ID_SHIFT        18

extern unsigned char ucTXMsgData[8]; // TX Data
extern unsigned char ucRXMsgData[2];         // RX Data
extern uint32_t messageSize;          // Message Size (DLC)
extern volatile unsigned long msgCount; // A counter that keeps track of the
                                     // number of times the transmit was
                                     // successful.
extern volatile unsigned long errFlag;  // A flag to indicate that some
                                     // transmission error occurred.

static const uint16_t canBitValues[] =
{
    0x1100, // TSEG2 2, TSEG1 2, SJW 1, Divide 5
    0x1200, // TSEG2 2, TSEG1 3, SJW 1, Divide 6
    0x2240, // TSEG2 3, TSEG1 3, SJW 2, Divide 7
    0x2340, // TSEG2 3, TSEG1 4, SJW 2, Divide 8
    0x3340, // TSEG2 4, TSEG1 4, SJW 2, Divide 9
    0x3440, // TSEG2 4, TSEG1 5, SJW 2, Divide 10
    0x3540, // TSEG2 4, TSEG1 6, SJW 2, Divide 11
    0x3640, // TSEG2 4, TSEG1 7, SJW 2, Divide 12
    0x3740  // TSEG2 4, TSEG1 8, SJW 2, Divide 13
};

typedef enum
{
        //! Transmit message object.
        MSG_OBJ_TYPE_TRANSMIT,

        //! Receive message object.
        MSG_OBJ_TYPE_RECEIVE
}msgObjType;


//-----------------IO口操作-----------------//
//#define LDAC(x)     x ? (GpioDataRegs.GPESET.bit.GPIO130=1) : (GpioDataRegs.GPECLEAR.bit.GPIO130=1)
#define LED1(x)     x ? (GpioDataRegs.GPBSET.bit.GPIO33=1) : (GpioDataRegs.GPBCLEAR.bit.GPIO33=1)
#define LED2(x)     x ? (GpioDataRegs.GPDSET.bit.GPIO124=1) : (GpioDataRegs.GPDCLEAR.bit.GPIO124=1)
#define LED1Toggle   GpioDataRegs.GPBDAT.bit.GPIO33
#define GPIO16(x)     x ? (GpioDataRegs.GPASET.bit.GPIO16=1) : (GpioDataRegs.GPACLEAR.bit.GPIO16=1)

#define SW1_IO        (GpioDataRegs.GPBDAT.bit.GPIO44)     // SW1状态   //  低电平有效
#define PreCh1FB      (GpioDataRegs.GPBDAT.bit.GPIO49)     // 继电器K1反馈   //  高电平有效
#define PreCh2FB      (GpioDataRegs.GPBDAT.bit.GPIO50)     // 继电器K2反馈   //  高电平有效
///-----------------422/485操作-----------------//
#define RX1_Mode_485      GpioDataRegs.GPACLEAR.bit.GPIO3=1
#define Dis_RX1_Mode_485  GpioDataRegs.GPASET.bit.GPIO3=1
#define TX1_Mode_485      GpioDataRegs.GPASET.bit.GPIO2=1
#define Dis_TX1_Mode_485  GpioDataRegs.GPACLEAR.bit.GPIO2=1

#define RX3_Mode_485      GpioDataRegs.GPBCLEAR.bit.GPIO43=1
#define Dis_RX3_Mode_485  GpioDataRegs.GPBSET.bit.GPIO43=1
#define TX3_Mode_485      GpioDataRegs.GPBSET.bit.GPIO61=1
#define Dis_TX3_Mode_485  GpioDataRegs.GPBCLEAR.bit.GPIO61=1

#define RX4_Mode_SSI      GpioDataRegs.GPDCLEAR.bit.GPIO102=1
#define Dis_RX4_Mode_SSI  GpioDataRegs.GPDSET.bit.GPIO102=1
#define TX4_Mode_SSI      GpioDataRegs.GPASET.bit.GPIO17=1
#define Dis_TX4_Mode_SSI  GpioDataRegs.GPACLEAR.bit.GPIO17=1

#define CPLD_FLT_CLR      GpioDataRegs.GPACLEAR.bit.GPIO28=1
#define Dis_CPLD_FLT_CLR  GpioDataRegs.GPASET.bit.GPIO28=1

#define SCI_BOOT_EN       GpioDataRegs.GPECLEAR.bit.GPIO143=1
#define SCI_BOOT_DIS      GpioDataRegs.GPESET.bit.GPIO143=1


#define SAMPLE_EN         GpioDataRegs.GPDCLEAR.bit.GPIO111=1
#define SAMPLE_DIS        GpioDataRegs.GPDSET.bit.GPIO111=1

//    9.NTC1-Error[GPIO91]             GPIO输入I[GMUX=0][MUX=0]    外部输入 ，NTC1故障
//    10.NTC2-Error[GPIO90]            GPIO输入I[GMUX=0][MUX=0]    外部输入 ，NTC2故障
//    11.NTC3-Error[GPIO89]           GPIO输入I[GMUX=0][MUX=0]     外部输入 ，NTC3故障
//    12.NTC4-Error[GPIO88]           GPIO输入I[GMUX=0][MUX=0]     外部输入 ，NTC4故障
//    13.VBus-Error[GPIO87]         GPIO输入I[GMUX=0][MUX=0]       外部输入,母线过压故障
//    14.IBus-Error[GPIO86]          GPIO输入I[GMUX=0][MUX=0]      外部输入，母线过流故障
//    15.Ia-Error[GPIO129]          GPIO输入I[GMUX=0][MUX=0]       外部输入 ，A相过流故障
//    16.Ib-Error[GPIO35]            GPIO输入I[GMUX=0][MUX=0]      外部输入，B相过流故障
//    17.Ic-Error[GPIO65]            GPIO输入I[GMUX=0][MUX=0]      外部输入 ，C相过流故障
#define NTC1_Err_IN      GpioDataRegs.GPCDAT.bit.GPIO89
#define NTC2_Err_IN      GpioDataRegs.GPCDAT.bit.GPIO88

#define Vbus_Err_IN      GpioDataRegs.GPEDAT.bit.GPIO129
#define Ibus_Err_IN      GpioDataRegs.GPBDAT.bit.GPIO35
#define Ia_Err_IN        GpioDataRegs.GPCDAT.bit.GPIO65
#define Ib_Err_IN        GpioDataRegs.GPADAT.bit.GPIO0
#define Ic_Err_IN        GpioDataRegs.GPDDAT.bit.GPIO122


//-----------------EMIF通讯-----------------//
// 片外AD的数据读取首地址;CS3寻址范围：0x0030 0000~0x0037 FFFF;
// EMIF异步通讯模式下 BA1作为地址的最低位
// EM1BA[0]未与FPGA连接，所以地址的第0位无法使用，只能左移1位来使用，只能使用偶地址
#define EM1CS3_DATA1              ((Uint16 *)(0x300000 | (0x000001)))
#define EM1CS3_DATA2              ((Uint16 *)(0x300000 | (0x000002)))
#define EM1CS3_DATA3              ((Uint16 *)(0x300000 | (0x000AA5)))
#define EM1CS3_DATA4              ((Uint16 *)(0x300000 | (0x0000A5)))


// SCI_MSG structure
#define SCI_CMD_HEADER1      0x55
#define SCI_CMD_HEADER2      0xAA

#define SCI_FRAME_LEN        8

//=============================================================================
//  Typdefs and Externs
//=============================================================================

//  Global Variable
 extern float Idref;
 extern float Iqref;
 extern float Ud_last;
 extern float Uq_last;
 extern float  Udcmax ;
 extern float  ids;
 extern float  iqs;


extern float  INV_Ubase;    //1/输出电压基值

extern float Xtheta;//角度

extern float sinthetaA;
extern float costhetaA;

extern float sinthetaB;
extern float sinthetaC;

extern float costhetaB;
extern float costhetaC;

extern unsigned int dac_num;//输出sin波形 角度计数

extern Uint16 SW1_STATUS;//初始化为1，无按键按下
extern Uint16 PreCh1FB_STATUS;
extern Uint16 PreCh2FB_STATUS;

extern float DutyA,DutyB,DutyC;
extern float Motor_Control_Ud; // 上个控制周期的Ud
extern float Motor_Control_Uq; // 上个控制周期的Uq


 // SCI 通信状态机定义
 enum SCI_SM_ID {
     SCI_SM_ID_IDLE = 0x0,               // SCI idle state, go to header if header1 matches, else go to idle
     SCI_SM_ID_HEADER2,                   // SCI header2 match, go to DATA4, else go to idle
     SCI_SM_ID_DATA4,                    // Read DATA4, go to DATA3
     SCI_SM_ID_DATA3,                    // Read DATA3, go to DATA2
     SCI_SM_ID_DATA2,                    // Read DATA2, go to DATA1
     SCI_SM_ID_DATA1,                    // Read DATA1, go to DATA0
     SCI_SM_ID_DATA0,                    // Read DATA0, go to CHECKSUM
     SCI_SM_ID_CHECKSUM,                 // Read CHECKSUM, ProcessMsg, go to idle
 };

 //报文处理状态机定义
   enum CAN_MSG_SM_ID {
   CAN_MSG_SM_IDLE = 0x0,  // Idle待机状态

   CAN_MSG_SM_CMD1,        // 状态1查询指令
   CAN_MSG_SM_CMD2,        // 报文2系统速度设置指令
   CAN_MSG_SM_CMD3,        // 报文3故障复位指令（测试）

   CAN_MSG_SM_CMD4,        // 报文4状态2查询指令（测试）
   CAN_MSG_SM_CMD5,        // 报文5旋变找零指令（测试）
   CAN_MSG_SM_CMD6,        // 报文6驱动器状态查询（测试）
   CAN_MSG_SM_CMD7,        // 报文7,设置速度环PI参数    （测试用）
   CAN_MSG_SM_CMD8,        // 报文8，设置电流环d轴PI参数    （测试用）
   CAN_MSG_SM_CMD9,        // 报文9，设置电流环q轴PI参数（测试用）
   CAN_MSG_SM_CMD10,       // 报文10，设置直流母线电压和电流环参考值   （测试用）
   CAN_MSG_SM_CMD11,       // 报文11，设置工作模式（测试用）
   CAN_MSG_SM_CMD12,       // 报文12，设置SCI boot模式

//   CAN_MSG_SM_CMD30,                     // 继电器断开（测试用）
//   CAN_MSG_SM_CMD31,                     //
//
//
//   CAN_MSG_SM_CMD40,               // 报文40  急停
//   CAN_MSG_SM_CMD41,
//   CAN_MSG_SM_CMD42,
   };

//报文处理状态机定义
   enum CANB_MSG_SM_ID {
      CANB_MSG_SM_IDLE = 0x0,  // Idle待机状态

      CANB_MSG_SM_CMD1,        // 报文1，DC-DC模块状态

      };


 struct CAN_MSG_STRUC {
     Uint8 DATA0;
     Uint8 DATA1;       //
     Uint8 DATA2;
     Uint8 DATA3;      //
     Uint8 DATA4;
     Uint8 DATA5;
     Uint8 DATA6;
     Uint8 DATA7;
 };


 extern  volatile struct  CAN_MSG_STRUC   CANmMsgRecv;
 extern  volatile struct  CAN_MSG_STRUC   CANmMsgTran;

 extern  volatile struct  CAN_MSG_STRUC   CANBmMsgRecv;//Can-b 接收数据


 struct OCFLT_REG_BITS{

     Uint8  OC_PhaseC_Hard  :   1;      //C相硬件过流
     Uint8  OC_PhaseB_Hard  :   1;      //B相硬件过流
     Uint8  OC_PhaseA_Hard  :   1;      //A相硬件过流
     Uint8  OC_IBus_Hard    :   1;      //母线硬件过流
     Uint8  OC_PhaseC_Soft  :   1;      //C相软件过流
     Uint8  OC_PhaseB_Soft  :   1;      //B相软件过流
     Uint8  OC_PhaseA_Soft  :   1;      //A相软件过流
     Uint8  OC_IBus_Soft    :   1;      //母线软件过流
 };

 union OCFLT_REG {
     Uint8  all;
     struct  OCFLT_REG_BITS bit;
 };




 struct SYSSTATUS_REG_BITS{

     Uint8  SelfCheck_FLT  :   1;      //自检异常
     Uint8  Phase_FLT      :   1;      // 缺相
     Uint8  MOT_OT         :   1;      // 电机过热
     Uint8  INV_OT         :   1;      //逆变器过温
     Uint8  IGBT_FLT       :   1;      //功率器件保护
     Uint8  Bus_UV         :   1;      // 母线欠压
     Uint8  Bus_OV         :   1;      // 母线过压
     Uint8  INV_OC         :   1;      // 逆变器过流
 };

 union SYSSTATUS_REG {
     Uint8  all;
     struct  SYSSTATUS_REG_BITS bit;
 };

 struct INVSTATUS_REG_BITS{

     Uint8  Ib_Err           :   1;      //
     Uint8  Ia_Err           :   1;      //
     Uint8  Ibus_Err         :   1;      //
     Uint8  Vbus_Err         :   1;      //
     Uint8  NTC4_Err         :   1;      //
     Uint8  NTC3_Err         :   1;      //
     Uint8  NTC2_Err         :   1;      //
     Uint8  NTC1_Err         :   1;      //

 };

 union INVSTATUS_REG {
     Uint8  all;
     struct  INVSTATUS_REG_BITS bit;
 };

 struct MOTSTATUS_REG_BITS{

     Uint8  Sensor_FLT     :   1;      //位置传感器异常
     Uint8  OVSpeed        :   1;      //内部超速
     Uint8  CPLD_FLT       :   1;      //
     Uint8  FLT4_Flag      :   1;      //
     Uint8  FLT3_Flag      :   1;      //
     Uint8  FLT2_Flag      :   1;      //
     Uint8  FLT1_Flag      :   1;      //
     Uint8  Ic_Err         :   1;      //高位

 };

 union MOTSTATUS_REG {
     Uint8  all;
     struct  MOTSTATUS_REG_BITS bit;
 };

 struct CAN_TXSTATUS1_STRUC {

//     Uint8 Duty;         // 占空比
     union OCFLT_REG OCFLT;
     Uint8 SpeedH;       // 高位
     Uint8 SpeedL;       // 低位
     union SYSSTATUS_REG SYSSTATUS;        //
//     union MOTSTATUS_REG MOTSTATUS;
     Uint8 VbusH;
     Uint8 VbusL;
     Uint8 IbusH;
     Uint8 IbusL;
 };



 struct CAN_TXSTATUS2_STRUC {

     union INVSTATUS_REG INVSTATUS;
     union MOTSTATUS_REG MOTSTATUS;
     Uint8 IaH;       // 高位
     Uint8 IaL;       // 低位
     Uint8 IbH;
     Uint8 IbL;
     Uint8 IcH;
     Uint8 IcL;

 };


 extern  volatile struct CAN_TXSTATUS1_STRUC mCANStatus1Tran;
 extern  volatile struct CAN_TXSTATUS2_STRUC mCANStatus2Tran;

 extern  volatile struct CAN_TXSTATUS1_STRUC mFaultCANStatus1Tran;
 extern  volatile struct CAN_TXSTATUS2_STRUC mFaultCANStatus2Tran;

 struct ERRORCODE_REG_BITS{

     Uint8  RSVD2      :   1;      //自检异常
     Uint8  NTC_FLT    :   1;      // 过温保护
     Uint8  FLT5V      :   1;      // 电机过热
     Uint8  FLT15V     :   1;      //逆变器过温
     Uint8  DESAT2     :   1;      //功率器件保护
     Uint8  DESAT1     :   1;      // 母线欠压
     Uint8  RSVD1      :   1;      // 母线过压
     Uint8  FaultALL   :   1;      // 逆变器过流

 };

 union ERRORCODE_REG {
     Uint8  all;
     struct  ERRORCODE_REG_BITS bit;
 };


 struct CAN_DRV_STATUS_STRUC {

     union ERRORCODE_REG ERRORCODE1;        //
     Uint8 Temp1;       // #1温度
     union ERRORCODE_REG ERRORCODE2;        //
     Uint8 Temp2;       // #2温度
     union ERRORCODE_REG ERRORCODE3;        //
     Uint8 Temp3;   // #3温度
     Uint8 RSVD1;
     Uint8 RSVD2;
 };


extern  volatile struct CAN_DRV_STATUS_STRUC mCANDRVStatusTran;
extern  volatile struct CAN_DRV_STATUS_STRUC mFaultCANDRVStatusTran;

 struct SCI_MSG_STRUC {

     Uint8 Header1;       // Header1
     Uint8 Header2;       // Header2
     Uint8 DATA4;
     Uint8 DATA3;
     Uint8 DATA2;
     Uint8 DATA1;
     Uint8 DATA0;
     Uint8 CHECKSUM;

 };

 struct ERRCODE_BIT
 {
     Uint8 Fault_all:1;       // Indicate if there is a fault.
     Uint8 Fault_rst:1;       // if is under reseting. (Not used for now)
     Uint8 Desat1:1;
     Uint8 Desat2:1;
     Uint8 Fault_15V:1;
     Uint8 Fault_5V:1;
     Uint8 Fault_NTC:1;
     Uint8 Rsvd1:1;
     //Uint8 Rsvd2:8;
 };

 union ERRCODE_REG {
     Uint8  all;
     struct  ERRCODE_BIT  bit;
 };

 struct SCI_DATA_STRUC {

     Uint8 Volt_15V;
     Uint8 Volt_5V;
     Uint8 Temperature;
     union ERRCODE_REG errcode;
     Uint8 Rsvd2;
 };



 extern  volatile struct  SCI_MSG_STRUC  mMsgRecv_B;
 extern  volatile struct  SCI_MSG_STRUC  mMsgRecv_C;
 extern  volatile struct  SCI_MSG_STRUC  mMsgRecv_D;
 extern  volatile struct  SCI_DATA_STRUC mMsgDATA_B;
 extern  volatile struct  SCI_DATA_STRUC mMsgDATA_C;
 extern  volatile struct  SCI_DATA_STRUC mMsgDATA_D;
// extern  volatile struct  SCI_MSG_STRUC   mMsgTran;

 extern Uint16 SCIA_DataCheckFLG;
 extern Uint16 SCIB_DataCheckFLG;
 extern Uint16 SCIC_DataCheckFLG;
 extern Uint16 SCID_DataCheckFLG;

 extern Uint16 SCIB_CheckErr_Cnt;
 extern Uint16 SCIC_CheckErr_Cnt;
 extern Uint16 SCID_CheckErr_Cnt;
 extern Uint16 SCIB_ResetEn;
 extern Uint16 SCIC_ResetEn;
 extern Uint16 SCID_ResetEn;

 //控制状态机ID定义
  enum MCTRL_SM_ID {
      MCTRL_SM_ID_IDLE = 0x00,                // 空闲 0x00
      MCTRL_SM_ID_Speed_Mode_SEL=0x01,
      MCTRL_SM_ID_LoSpeed_MODE=0x02,
      MCTRL_SM_ID_HiSpeed_MODE=0x03,
      MCTRL_SM_ID_Bus_CHARGE = 0x04,          // 直流母线充电，
      MCTRL_SM_ID_Bus_CHARGE_DELAY = 0x05,    // 直流母线充电延时，
      MCTRL_SM_ID_Bus_CHARGE_OVER_PRE = 0x06, //直流母线充电预结束，继电器闭合后等待300ms，使其完全闭合。
      MCTRL_SM_ID_Bus_CHARGE_OVER = 0x07,     //直流母线充电结束
      MCTRL_SM_ID_MD_RUN = 0x08,              //进入电机驱动器运行，进入条件收到0x02报文且速度设置不为零
      MCTRL_SM_ID_MD_PWM_OFF = 0x09,          //电机驱动器停止功率输出

      MCTRL_SM_ID_SYSTEM_STOP_PRE=0x40,    // Enable=0,系统准备急停
      MCTRL_SM_ID_SYSTEM_STOP=0x41,             // 系统急停

      MCTRL_SM_ID_ROTTX_ZERO_EN_PRE=0x50,        // 准备零位校准
      MCTRL_SM_ID_ROTTX_ZERO_EN_PRE2=0x51,           // 准备零位校准2
      MCTRL_SM_ID_ROTTX_ZERO_PULSE_ON=0x52,           // 发送小脉冲
      MCTRL_SM_ID_ROTTX_ZERO_DATA_READ=0x53,           // 获得零位数据
      MCTRL_SM_ID_ROTTX_ZERO_DATA_LOG=0x54,           // 存数据

      MCTRL_SM_ID_FAULT = 0xFE,             // 故障，收到0xF1报文清除故障后进入MCTRL_SM_ID_FAULT_RET,等待故障返回
      MCTRL_SM_ID_FAULT_RET = 0xFF             // 故障，收到0xF1报文清除故障后进入MCTRL_SM_ID_IDLE
  };

  extern enum MCTRL_SM_ID mMCTRL_State;

enum SpeedCtrlMode  {
     LoSpeedMode=0x01,
     HiSpeedMode=0x02
};

 extern enum SpeedCtrlMode mSpeedCtrlMode;

  extern Uint16 CAN_CMD;//CAN报文编号
  extern Uint16 CAN_CMD1FLG;//指令1收到标志位
  extern Uint16 CAN_CMD2FLG;
  extern Uint16 CAN_CMD3FLG;
  extern Uint16 CAN_CMD4FLG;
  extern Uint16 CAN_CMD5FLG;
  extern Uint16 CAN_CMD6FLG;
  extern Uint16 CAN_CMD7FLG;
  extern Uint16 CAN_CMD8FLG;
  extern Uint16 CAN_CMD9FLG;
  extern Uint16 CAN_CMD10FLG;
  extern Uint16 CAN_CMD11FLG;
  extern Uint16 CAN_CMD12FLG;

  extern Uint16 CAN_CMD30FLG;

  extern Uint16 CAN_CMD40FLG;

  extern Uint16 Vdcset_ref;//母线电压设置参考值
  extern Uint16 Iset_d_ref;//d轴电流参考值



  extern Uint16 CAN_CMD1OKFLG;////指令执行完毕标志位0---未执行完，1-----执行完。
  extern Uint16 CAN_CMD2OKFLG;//指令2收到标志位
  extern Uint16 CAN_CMD3OKFLG;
  extern Uint16 CAN_CMD4OKFLG;
  extern Uint16 CAN_CMD5OKFLG;
  extern Uint16 CAN_CMD6OKFLG;
  extern Uint16 CAN_CMD7OKFLG;
  extern Uint16 CAN_CMD8OKFLG;
  extern Uint16 CAN_CMD9OKFLG;
  extern Uint16 CAN_CMD10OKFLG;
  extern Uint16 CAN_CMD11OKFLG;
  extern Uint16 CAN_CMD12OKFLG;

  extern Uint16 CAN_CMD30OKFLG;

  extern Uint16 CAN_CMD40OKFLG;

  extern Uint16  PWMOffFlag;    // 只有当无硬件错误时，才可软件置位此标志

//=============================================================================
//  Function Prototypes
//=============================================================================
 //=======================================================
 //函数名称:  Controller_PI()
 //功能:   DigitalController_PI
 //输入:   PI参数结构体：PI_STRUCT *p
 //返回:   PI输出： p->Out
 //备注:   积分抗饱和方法：(具体方法详见Matlab help - PID Controller，需要宏定义)
 //          _AntiWinup_BackCalculation[反馈抗饱和]     _AntiWinup_Clamping[钳位正向积分]
 //=======================================================
 #ifdef _FLASH
 #pragma CODE_SECTION(Controller_runPID, ".TI.ramfunc");
 #endif

typedef volatile struct PID_struct {
    float   Ref;             // Input: Reference input
    float   Fdb;             // Input: Feedback input
    float   Fdb_last;        // previous Feedback value
    float   Err;             // Variable: Error, e(k)
    float   Kp;              // Parameter: Proportional gain
    float   Ki;              // Parameter:Integral time (Ki=Ts/Ti)
    float   Kd;              // PID controller: Kd
    float   Kd_Filter;       // Derivative Filter Coefficient, 0~1, 1 is slowest.
    float   Up;              // Variable: Proportional output
    float   Ui;              // Variable: Integral output
    float   Ud;              // Variable: Differential output
    float   OutMax;          // Parameter: Maximum output
    float   OutMin;          // Parameter: Minimum output
    float   OutPreSat;       // Variable: Pre-saturated output
    float   Out;             // Output: PID output
} PID_STRUCT;


static inline float Controller_runPID(PID_STRUCT *p)
{
    p->Err = p->Ref - p->Fdb;                                     // Compute the error
    p->Up= p->Kp * p->Err;                                        // Compute the proportional output
    p->Ui += p->Ki * p->Err;                                      // Compute the integral output
    p->Ud = p->Ud * p->Kd_Filter - p->Kd * (p->Fdb - p->Fdb_last) * (1-p->Kd_Filter);
    p->Ui = fmaxf((fminf(p->OutMax, p->Ui)), p->OutMin);
    p->OutPreSat = p->Up + p->Ui + p->Ud;                           // Compute the pre-saturated output
    p->Out = fmaxf((fminf(p->OutMax, p->OutPreSat)), p->OutMin);     // Saturate the output
    p->Fdb_last = p->Fdb;                                           // Save Fdb_last
    return (p->Out);

}


// PI结构体 //
extern PID_STRUCT ACR_d_PID;//电流环d轴
extern PID_STRUCT ACR_q_PID;//电流环q轴
extern PID_STRUCT ASR_PI;//速度调节器




#ifdef _FLASH
#pragma CODE_SECTION(SPWM_Vref, ".TI.ramfunc");
#pragma CODE_SECTION(Controlloops, ".TI.ramfunc");
#pragma CODE_SECTION(Spll_Control, ".TI.ramfunc");
#pragma CODE_SECTION(PWMSoftOutputOff, ".TI.ramfunc");
#pragma CODE_SECTION(PWMSoftOutputOn, ".TI.ramfunc");
#pragma CODE_SECTION(SVDPWM, ".TI.ramfunc");
#pragma CODE_SECTION(MTPA_Ctrl, ".TI.ramfunc");
#pragma CODE_SECTION(ProcessSCIB, ".TI.ramfunc");
#pragma CODE_SECTION(ProcessSCIC, ".TI.ramfunc");
#pragma CODE_SECTION(ProcessSCID, ".TI.ramfunc");
#endif

void ProcessCANA(void);
void ProcessCanMSG();
void ProcessCANB(void);
void SPWM_Vref(void);
void Spll_Control(void);
void Controlloops(void);


void SVDPWM(void);


void PWMSoftOutputOff();
void PWMSoftOutputOn();
void Process_SW1(void);
void MotorDrive_Ctrl_PI_init(void);

void InitCANACANB(void);
uint32_t setCANABitRate(uint32_t sourceClock, uint32_t bitRate);
uint32_t setCANBBitRate(uint32_t sourceClock, uint32_t bitRate);
void setupMessageObject(uint32_t objID, uint32_t msgID, msgObjType msgType);
void setupCANBMessageObject(uint32_t objID, uint32_t msgID, msgObjType msgType);
void sendCANAMessage(uint32_t objID);
void sendCANBMessage(uint32_t objID);
bool getCANAMessage(uint32_t objID);
bool getCANBMessage(uint32_t objID);

void setupMessageObjectTypeII(uint32_t objID, uint32_t msgID, msgObjType msgType);
void sendCANAMessageTypeII(uint32_t objID);
bool getCANAMessageTypeII(uint32_t objID);


void ProcessSCIB(void);
void ProcessSCIC(void);
void ProcessSCID(void);


#ifdef __cplusplus
}
#endif /* extern "C" */

#endif  // end of MMC_PET_CONTROL_H_

//=======================================================
//                                                          End of file.
//=======================================================

