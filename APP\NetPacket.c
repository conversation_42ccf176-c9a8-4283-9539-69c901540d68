/**
 * @file NetPacket.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-21
 * @last modified 2025-08-21
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#include "NetPacket.h"
#include "crc.h"

void NetPacket_Reset(struct NetPacket *packet)
{
    packet->idx = 0;
    packet->len = 0;
}

void NetPacket_WriteByte(struct NetPacket *packet, byte_t data)
{
    packet->data[packet->idx++] = data;
    packet->len++;
}

void NetPacket_WriteUint16_LE(struct NetPacket *packet, uint16_t data)
{
    packet->data[packet->idx++] = data & 0xFF;
    packet->data[packet->idx++] = data >> 8;
    packet->len += 2;
}

void NetPacket_WriteUint32_LE(struct NetPacket *packet, uint32_t data)
{
    packet->data[packet->idx++] = data & 0xFF;
    packet->data[packet->idx++] = (data >> 8) & 0xFF;
    packet->data[packet->idx++] = (data >> 16) & 0xFF;
    packet->data[packet->idx++] = data >> 24;
    packet->len += 4;
}

void NetPacket_WriteFloat32_LE(struct NetPacket *packet, float data)
{
    // 将浮点数转换为32位无符号整数
    uint32_t var = *(uint32_t *)&data;

    // 以小端模式写入数据
    packet->data[packet->idx++] = var & 0xFF;
    packet->data[packet->idx++] = (var >> 8) & 0xFF;
    packet->data[packet->idx++] = (var >> 16) & 0xFF;
    packet->data[packet->idx++] = (var >> 24) & 0xFF;

    // 更新数据长度
    packet->len += 4;
}

void NetPacket_Init(struct NetPacket *packet, byte_t *buf, uint16_t capacity)
{
    NetPacket_Reset(packet);
    packet->header = 0x55AA;
    packet->seq = 0;
    packet->data = buf;
    packet->capacity = capacity;
}

void NetPacket_Start(struct NetPacket *packet)
{
    packet->idx = 0;
    packet->len = 0;
    NetPacket_WriteUint16_LE(packet, 0x55AA);
    NetPacket_WriteUint16_LE(packet, 0);
    NetPacket_WriteUint16_LE(packet, packet->seq);
}

void NetPacket_End(struct NetPacket *packet)
{
    // CRC-Modbus calculate
    //
    uint16_t remaining_length = packet->len + NETPACKET_CRC_PART_LEN - NETPACKET_HEADER_PART_LEN;
    packet->data[2] = remaining_length & 0xFF;
    packet->data[3] = remaining_length >> 8;
    uint16_t crc = CRC16_Modbus((byte_t *)packet->data + NETPACKET_HEADER_PART_LEN, remaining_length - NETPACKET_CRC_PART_LEN);
    packet->seq++;
    NetPacket_WriteUint16_LE(packet, crc);
}

uint16_t NetPacket_GetResidualCap(struct NetPacket *packet)
{
    return packet->capacity - packet->len;
}
