<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule configRelations="2" moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.ti.ccstudio.buildDefinitions.C2000.Debug.FLASH_CPU1">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.ti.ccstudio.buildDefinitions.C2000.Debug.FLASH_CPU1" moduleId="org.eclipse.cdt.core.settings" name="FLASH_CPU1_APP">
				<macros>
					<stringMacro name="FPU_FASTRTS_LIB_ROOT" type="VALUE_PATH_ANY" value="${ORIGINAL_PROJECT_ROOT}/lib"/>
				</macros>
				<externalSettings/>
				<extensions>
					<extension id="com.ti.ccstudio.binaryparser.CoffParser" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="com.ti.ccs.project.ErrorParser"/>
					<extension id="com.ti.ccs.errorparser.CompilerErrorParser_TI" point="com.ti.ccs.project.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.CoffErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.AsmErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.LinkErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="out" artifactName="${ProjName}" buildProperties="" cleanCommand="${CG_CLEAN_CMD}" description="" id="com.ti.ccstudio.buildDefinitions.C2000.Debug.FLASH_CPU1" name="FLASH_CPU1_APP" parent="com.ti.ccstudio.buildDefinitions.C2000.Debug">
					<folderInfo id="com.ti.ccstudio.buildDefinitions.C2000.Debug.FLASH_CPU1." name="/" resourcePath="">
						<toolChain id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.DebugToolchain.1238943972" name="TI Build Tools" secondaryOutputs="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.outputType__BIN.815524577" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.DebugToolchain" targetTool="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.linkerDebug.408533688">
							<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.459298029" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS" valueType="stringList">
								<listOptionValue builtIn="false" value="DEVICE_CONFIGURATION_ID=TMS320C28XX.TMS320F28377D"/>
								<listOptionValue builtIn="false" value="DEVICE_CORE_ID="/>
								<listOptionValue builtIn="false" value="DEVICE_ENDIANNESS=little"/>
								<listOptionValue builtIn="false" value="OUTPUT_FORMAT=COFF"/>
								<listOptionValue builtIn="false" value="RUNTIME_SUPPORT_LIBRARY="/>
								<listOptionValue builtIn="false" value="CCS_MBS_VERSION=70.0.0"/>
								<listOptionValue builtIn="false" value="LINK_ORDER=lib/rts2800_fpu32_eabi.lib;lib/rts2800_fpu32_fast_supplement_eabi.lib;"/>
								<listOptionValue builtIn="false" value="PRODUCTS="/>
								<listOptionValue builtIn="false" value="LINKER_COMMAND_FILE=28374S_FLASH_lnk.cmd"/>
								<listOptionValue builtIn="false" value="PRODUCT_MACRO_IMPORTS={}"/>
								<listOptionValue builtIn="false" value="OUTPUT_TYPE=executable"/>
							</option>
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.1422218298" name="Compiler version" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION" value="20.12.0.STS" valueType="string"/>
							<targetPlatform id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.targetPlatformDebug.390337645" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.targetPlatformDebug"/>
							<builder buildPath="${BuildDirectory}" id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.builderDebug.1520351459" keepEnvironmentInBuildfile="false" name="GNU Make" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.builderDebug"/>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.compilerDebug.621675025" name="C2000 Compiler" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.compilerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.LARGE_MEMORY_MODEL.1832548214" name="Option deprecated, set by default (--large_memory_model, -ml)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.LARGE_MEMORY_MODEL" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.UNIFIED_MEMORY.1170551926" name="Unified memory (--unified_memory, -mt)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.UNIFIED_MEMORY" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.SILICON_VERSION.454637436" name="Processor version (--silicon_version, -v)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.SILICON_VERSION" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.SILICON_VERSION.28" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.FLOAT_SUPPORT.1251279031" name="Specify floating point support (--float_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.FLOAT_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.FLOAT_SUPPORT.fpu32" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.CLA_SUPPORT.1903104185" name="Specify CLA support (--cla_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.CLA_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.CLA_SUPPORT.cla1" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.VCU_SUPPORT.2141536600" name="Specify VCU support (--vcu_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.VCU_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.VCU_SUPPORT.vcu2" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.TMU_SUPPORT.777666224" name="Specify TMU support (--tmu_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.TMU_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.TMU_SUPPORT.tmu0" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.ISR_SAVE_VCU_REGS.2136406908" name="Specify VCU register save/restore for interrupts (--isr_save_vcu_regs)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.ISR_SAVE_VCU_REGS" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.ISR_SAVE_VCU_REGS.on" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.OPT_LEVEL.806285498" name="Optimization level (--opt_level, -O)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.OPT_LEVEL" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.OPT_LEVEL.4" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.FP_MODE.251105474" name="Floating Point mode (--fp_mode)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.FP_MODE" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.FP_MODE.strict" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.INCLUDE_PATH.561137227" name="Add dir to #include search path (--include_path, -I)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.INCLUDE_PATH" valueType="includePath">
									<listOptionValue builtIn="false" value="${PROJECT_ROOT}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/APP}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/BFL}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/LIBs}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/HDL}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/W5500}"/>
									<listOptionValue builtIn="false" value="${PROJECT_ROOT}/TI_Include"/>
									<listOptionValue builtIn="false" value="${PROJECT_ROOT}/User_Include"/>
									<listOptionValue builtIn="false" value="${CG_TOOL_ROOT}/include"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/flash_api/include}"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.ADVICE__PERFORMANCE.1638199819" name="Provide advice on optimization techniques (--advice:performance)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.ADVICE__PERFORMANCE" value="--advice:performance=all" valueType="string"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DEFINE.769506002" name="Pre-define NAME (--define, -D)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DEFINE" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CPU1"/>
									<listOptionValue builtIn="false" value="_STANDALONE"/>
									<listOptionValue builtIn="false" value="_FLASH"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DEBUGGING_MODEL.665161990" name="Debugging model" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DEBUGGING_MODEL" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DEBUGGING_MODEL.SYMDEBUG__DWARF" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.C_DIALECT.1018156960" name="C Dialect" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.C_DIALECT" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.C_DIALECT.C99" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DIAG_WARNING.1958313714" name="Treat diagnostic &lt;id&gt; as warning (--diag_warning, -pdsw)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DIAG_WARNING" valueType="stringList">
									<listOptionValue builtIn="false" value="225"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DIAG_WRAP.1724398486" name="Wrap diagnostic messages (--diag_wrap) [deprecated]" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DIAG_WRAP" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DIAG_WRAP.off" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DISPLAY_ERROR_NUMBER.1295654038" name="Emit diagnostic identifier numbers (--display_error_number, -pden) [deprecated]" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.DISPLAY_ERROR_NUMBER" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.ABI.226343231" name="Application binary interface [See 'General' page to edit] (--abi)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.ABI" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.ABI.coffabi" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.OPT_FOR_SPEED.971999193" name="Speed vs. size trade-offs (--opt_for_speed, -mf)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.OPT_FOR_SPEED" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.OPT_FOR_SPEED.2" valueType="enumerated"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__C_SRCS.377614952" name="C Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__C_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__CPP_SRCS.64351973" name="C++ Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__CPP_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__ASM_SRCS.796587206" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__ASM_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__ASM2_SRCS.753993442" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__ASM2_SRCS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.linkerDebug.408533688" name="C2000 Linker" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.linkerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.STACK_SIZE.1115236950" name="Set C system stack size (--stack_size, -stack)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.STACK_SIZE" value="0x310" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.MAP_FILE.1358712680" name="Link information (map) listed into &lt;file&gt; (--map_file, -m)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.MAP_FILE" value="${ProjName}.map" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.OUTPUT_FILE.60334099" name="Specify output file name (--output_file, -o)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.OUTPUT_FILE" value="${ProjName}.out" valueType="string"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.SEARCH_PATH.863008463" name="Add &lt;dir&gt; to library search path (--search_path, -i)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.SEARCH_PATH" valueType="libPaths">
									<listOptionValue builtIn="false" value="${PROJECT_ROOT}/lib/FPUfastRTS"/>
									<listOptionValue builtIn="false" value="${PROJECT_ROOT}/lib"/>
									<listOptionValue builtIn="false" value="${CG_TOOL_ROOT}/lib"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.PRIORITY.362748525" name="Search libraries in priority order (--priority, -priority)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.PRIORITY" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.DEFINE.97463810" name="Pre-define preprocessor macro _name_ to _value_ (--define)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.DEFINE" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CLA_C"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.DIAG_WRAP.892759740" name="Wrap diagnostic messages (--diag_wrap) [deprecated]" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.DIAG_WRAP" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.DIAG_WRAP.off" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.DISPLAY_ERROR_NUMBER.349474856" name="Emit diagnostic identifier numbers (--display_error_number)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.DISPLAY_ERROR_NUMBER" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.XML_LINK_INFO.671643490" name="Detailed link information data-base into &lt;file&gt; (--xml_link_info, -xml_link_info)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.XML_LINK_INFO" value="${ProjName}_linkInfo.xml" valueType="string"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.LIBRARY.1850464209" name="Include library file or command file as input (--library, -l)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.linkerID.LIBRARY" valueType="libs">
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/lib/rts2800_fpu32.lib}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/lib/FPUfastRTS/rts2800_fpu32_fast_supplement_coff.lib}"/>
								</option>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exeLinker.inputType__CMD_SRCS.522649156" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exeLinker.inputType__CMD_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exeLinker.inputType__CMD2_SRCS.1539907369" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exeLinker.inputType__CMD2_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exeLinker.inputType__GEN_CMDS.706469116" name="Generated Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exeLinker.inputType__GEN_CMDS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.1268359007" name="C2000 Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.TOOL_ENABLE.657706762" name="Enable tool" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.TOOL_ENABLE" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.MEMWIDTH.143818205" name="Specify memory width (--memwidth, -memwidth)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.MEMWIDTH" value="16" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.ORDER.625585189" name="Specify data ordering (--order, -order)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.ORDER" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.ORDER.MS" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.ROMWIDTH.1027143225" name="Specify rom width (--romwidth, -romwidth)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.ROMWIDTH" value="16" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.OUTPUT_FORMAT.1565173921" name="Output format" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.OUTPUT_FORMAT" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.OUTPUT_FORMAT.INTEL" valueType="enumerated"/>
								<outputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.outputType__BIN.815524577" name="Binary File" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.outputType__BIN"/>
							</tool>
						</toolChain>
					</folderInfo>
					<folderInfo id="com.ti.ccstudio.buildDefinitions.C2000.Debug.FLASH_CPU1.2113252003" name="/" resourcePath="APP">
						<toolChain id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.DebugToolchain.1746481664" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.DebugToolchain" unusedChildren="">
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.459298029.1145150156" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.459298029"/>
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.1422218298.1328468100" name="Compiler version" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.1422218298"/>
							<targetPlatform id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.targetPlatformDebug" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.targetPlatformDebug"/>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.compilerDebug.1659293540" name="C2000 Compiler" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.compilerDebug.621675025">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.OPT_FOR_SPEED.1358166678" name="Speed vs. size trade-offs (--opt_for_speed, -mf)" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.OPT_FOR_SPEED" value="com.ti.ccstudio.buildDefinitions.C2000_20.12.compilerID.OPT_FOR_SPEED.5" valueType="enumerated"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__C_SRCS.2049815313" name="C Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__C_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__CPP_SRCS.868964281" name="C++ Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__CPP_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__ASM_SRCS.1642526648" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__ASM_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__ASM2_SRCS.1584452671" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.compiler.inputType__ASM2_SRCS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.linkerDebug.1079060842" name="C2000 Linker" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.exe.linkerDebug.408533688"/>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.50248750" name="C2000 Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.C2000_20.12.hex.1268359007"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="CMD/2837xD_FLASH_CLA_lnk_cpu1.cmd|APP/main.c|CMD/2837xD_FLASH_lnk_cpu1.cmd|TI_Source/F2837xD_SWPrioritizedPieVect.c" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="MMC_20210301.com.ti.ccstudio.buildDefinitions.C2000.ProjectType.FLASH_CPU1" name="C2000" projectType="com.ti.ccstudio.buildDefinitions.C2000.ProjectType"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration"/>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="FLASH_CPU1">
			<resource resourceType="PROJECT" workspacePath="/MMC_CPU1"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
</cproject>