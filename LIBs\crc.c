/**
 * @file crc.c
 * <AUTHOR> (l<PERSON><PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2022-11-08
 *
 * @copyright Copyright (c) 2022 Liu <PERSON> Personal.
 *
 */
#include "crc.h"
#include "vcu2_crc.h"
#define INIT_CRC16 0x0           //!< Initial CRC Register Value
#define INIT_CRC16_MODBUS 0xFFFF //!< Initial CRC Register Value
#define CRC_USING_HARDWARE 0

#if CRC_USING_HARDWARE == 0 
static const uint16_t crc16_modbus_table[256] = {
    0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241,
    0xC601, 0x06C0, 0x0780, 0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440,
    0xCC01, 0x0CC0, 0x0D80, 0xCD41, 0x0F00, 0xCFC1, 0xCE81, 0x0E40,
    0x0A00, 0x<PERSON><PERSON><PERSON>, 0xCB81, 0x0B40, 0xC901, 0x09C0, 0x0880, 0xC841,
    0xD801, 0x18C0, 0x1980, 0xD941, 0x1B00, 0xDBC1, 0xDA81, 0x1A40,
    0x1E00, 0xDEC1, 0xDF81, 0x1F40, 0xDD01, 0x1DC0, 0x1C80, 0xDC41,
    0x1400, 0xD4C1, 0xD581, 0x1540, 0xD701, 0x17C0, 0x1680, 0xD641,
    0xD201, 0x12C0, 0x1380, 0xD341, 0x1100, 0xD1C1, 0xD081, 0x1040,
    0xF001, 0x30C0, 0x3180, 0xF141, 0x3300, 0xF3C1, 0xF281, 0x3240,
    0x3600, 0xF6C1, 0xF781, 0x3740, 0xF501, 0x35C0, 0x3480, 0xF441,
    0x3C00, 0xFCC1, 0xFD81, 0x3D40, 0xFF01, 0x3FC0, 0x3E80, 0xFE41,
    0xFA01, 0x3AC0, 0x3B80, 0xFB41, 0x3900, 0xF9C1, 0xF881, 0x3840,
    0x2800, 0xE8C1, 0xE981, 0x2940, 0xEB01, 0x2BC0, 0x2A80, 0xEA41,
    0xEE01, 0x2EC0, 0x2F80, 0xEF41, 0x2D00, 0xEDC1, 0xEC81, 0x2C40,
    0xE401, 0x24C0, 0x2580, 0xE541, 0x2700, 0xE7C1, 0xE681, 0x2640,
    0x2200, 0xE2C1, 0xE381, 0x2340, 0xE101, 0x21C0, 0x2080, 0xE041,
    0xA001, 0x60C0, 0x6180, 0xA141, 0x6300, 0xA3C1, 0xA281, 0x6240,
    0x6600, 0xA6C1, 0xA781, 0x6740, 0xA501, 0x65C0, 0x6480, 0xA441,
    0x6C00, 0xACC1, 0xAD81, 0x6D40, 0xAF01, 0x6FC0, 0x6E80, 0xAE41,
    0xAA01, 0x6AC0, 0x6B80, 0xAB41, 0x6900, 0xA9C1, 0xA881, 0x6840,
    0x7800, 0xB8C1, 0xB981, 0x7940, 0xBB01, 0x7BC0, 0x7A80, 0xBA41,
    0xBE01, 0x7EC0, 0x7F80, 0xBF41, 0x7D00, 0xBDC1, 0xBC81, 0x7C40,
    0xB401, 0x74C0, 0x7580, 0xB541, 0x7700, 0xB7C1, 0xB681, 0x7640,
    0x7200, 0xB2C1, 0xB381, 0x7340, 0xB101, 0x71C0, 0x7080, 0xB041,
    0x5000, 0x90C1, 0x9181, 0x5140, 0x9301, 0x53C0, 0x5280, 0x9241,
    0x9601, 0x56C0, 0x5780, 0x9741, 0x5500, 0x95C1, 0x9481, 0x5440,
    0x9C01, 0x5CC0, 0x5D80, 0x9D41, 0x5F00, 0x9FC1, 0x9E81, 0x5E40,
    0x5A00, 0x9AC1, 0x9B81, 0x5B40, 0x9901, 0x59C0, 0x5880, 0x9841,
    0x8801, 0x48C0, 0x4980, 0x8941, 0x4B00, 0x8BC1, 0x8A81, 0x4A40,
    0x4E00, 0x8EC1, 0x8F81, 0x4F40, 0x8D01, 0x4DC0, 0x4C80, 0x8C41,
    0x4400, 0x84C1, 0x8581, 0x4540, 0x8701, 0x47C0, 0x4680, 0x8641,
    0x8201, 0x42C0, 0x4380, 0x8341, 0x4100, 0x81C1, 0x8081, 0x4040
};

/**
 * @brief Calculate the CRC16 checksum for the Modbus protocol
 * 
 * @param _pBuf Pointer to the data buffer
 * @param _usLen Data length (in bytes)
 * @return uint16_t The calculated CRC16 value
 */
uint16_t CRC16_Modbus(const byte_t *_pBuf, uint16_t _usLen)
{
    uint16_t crc = 0xFFFF;  // Modbus CRC16 initial value
    
    for (uint16_t i = 0; i < _usLen; i++)
    {
        uint16_t data = _pBuf[i] & 0xFF;  // Extract the lower 8 bits
        crc = (crc >> 8) ^ crc16_modbus_table[(crc ^ data) & 0xFF];
    }
    
    return crc;
}

/**
 * @brief Calculate CRC16 (Modbus) using the hardware CRC unit
 *
 * @param crc Initial CRC value
 * @param _pBuf Pointer to the data buffer
 * @param _usLen Data length
 * @return uint16_t The calculated CRC16 value
 */
uint16_t CRC16_Modbus_With(uint16_t crc, const byte_t *_pBuf, uint16_t _usLen)
{
    for (uint16_t i = 0; i < _usLen; i++)
    {
        uint16_t data = _pBuf[i] & 0xFF;  // Extract the lower 8 bits
        crc = (crc >> 8) ^ crc16_modbus_table[(crc ^ data) & 0xFF];
    }
    
    return crc;
}

/**
 * @brief Calculate the CRC16 (Modbus) of a uint16_t array - Full 16-bit version
 * Each uint16_t is split into two bytes in little-endian order for CRC calculation
 * Process the low byte first, then the high byte
 * 
 * @param _pBuf Pointer to the uint16_t data buffer
 * @param _usLen Data length (number of uint16_t elements)
 * @return uint16_t The calculated CRC16 value
 */
uint16_t CRC16_Modbus_U16_LE(const uint16_t *_pBuf, uint16_t _usLen) 
{
    uint16_t crc = 0xFFFF;  // Modbus initial value
    
    for (uint16_t i = 0; i < _usLen; i++)
    {
        // Process the low byte first (little-endian)
        uint16_t lowByte = _pBuf[i] & 0xFF;
        crc = (crc >> 8) ^ crc16_modbus_table[(crc ^ lowByte) & 0xFF];
        
        // Then process the high byte
        uint16_t highByte = (_pBuf[i] >> 8) & 0xFF;
        crc = (crc >> 8) ^ crc16_modbus_table[(crc ^ highByte) & 0xFF];
    }
    
    return crc;
}

/**
 * @brief Calculate the CRC16 (Modbus) of a uint16_t array - Big-endian version
 * Each uint16_t is split into two bytes in big-endian order for CRC calculation
 * Process the high byte first, then the low byte
 * 
 * @param _pBuf Pointer to the uint16_t data buffer
 * @param _usLen Data length (number of uint16_t elements)
 * @return uint16_t The calculated CRC16 value
 */
uint16_t CRC16_Modbus_U16_BE(const uint16_t *_pBuf, uint16_t _usLen) 
{
    uint16_t crc = 0xFFFF;  // Modbus initial value
    
    for (uint16_t i = 0; i < _usLen; i++)
    {
        // Process the high byte first (big-endian)
        uint16_t highByte = (_pBuf[i] >> 8) & 0xFF;
        crc = (crc >> 8) ^ crc16_modbus_table[(crc ^ highByte) & 0xFF];
        
        // Then process the low byte
        uint16_t lowByte = _pBuf[i] & 0xFF;
        crc = (crc >> 8) ^ crc16_modbus_table[(crc ^ lowByte) & 0xFF];
    }
    
    return crc;
}
#else
CRC_Obj CRC = {
    .seedValue = INIT_CRC16_MODBUS,
    .nMsgBytes = 0,
    .parity = CRC_parity_odd,
    .crcResult = 0,
    .pMsgBuffer = NULL,
    .pCrcTable = NULL,
    .init = (void (*)(void *))CRC_init16Bit,
    .run = (void (*)(void *))CRC_run16BitPoly1,
};

// handle(pointer) to the CRC object
CRC_Handle handleCRC = &CRC;
bool crc16_modbus_inited = false;

/**
 * @brief Calculate the CRC16 checksum for the Modbus protocol
 *
 * @param _pBuf Pointer to the data buffer
 * @param _usLen Data length
 * @return uint16_t The calculated CRC16 value, with high and low bytes swapped, suitable for direct transmission
 */
uint16_t CRC16_Modbus(const byte_t *_pBuf, uint16_t _usLen)
{
    if (!crc16_modbus_inited)
    {
        CRC.init(handleCRC);
        crc16_modbus_inited = true;
    }

    CRC.seedValue = INIT_CRC16_MODBUS;
    CRC.nMsgBytes = _usLen;
    CRC.pMsgBuffer = (uint16_t *)_pBuf;
    CRC.run(handleCRC);
    return (uint16_t)(CRC.crcResult & 0xFFFFU);
}

/**
 * @brief Calculate CRC16 (Modbus) using the hardware CRC unit
 *
 * @param crc Initial CRC value
 * @param _pBuf Pointer to the data buffer
 * @param _usLen Data length
 * @return uint16_t The calculated CRC16 value
 */
uint16_t CRC16_Modbus_With(uint16_t crc, const byte_t *_pBuf, uint16_t _usLen)
{
    if (!crc16_modbus_inited)
    {
        CRC.init(handleCRC);
        crc16_modbus_inited = true;
    }

    CRC.seedValue = crc;
    CRC.nMsgBytes = _usLen;
    CRC.pMsgBuffer = (uint16_t *)_pBuf;
    CRC.run(handleCRC);
    return (uint16_t)(CRC.crcResult & 0xFFFFU);
}
#endif // CRC_USING_HARDWARE
