/*
 * MotorDrive_150kW_DMA.c
 *
 *  Created on: 2023年5月12日
 *      Author: KUST410_2
 */

#include "MotorDrive_DMA.h"


#pragma DATA_SECTION(DMA_BUF_ADCC, "DMA_RAM_BUF");
#pragma DATA_SECTION(DMA_BUF_SPIC, "DMA_RAM_BUF");   //用于告诉编译器将变量 DMA_BUF_SPIC 放置在特定的内存区域。
#pragma DATA_SECTION(DMA_SEND_SPIC, "DMA_RAM_BUF");
Uint16 DMA_BUF_ADCC[RESULTS_BUFFER_SIZE1];
Uint16 DMA_BUF_SPIC[RESULTS_BUFFER_SIZE2];
Uint16 DMA_SEND_SPIC[SEND_BUFFER_SIZE3];

volatile Uint16 *pDMADest_CH1;      // DMA通道1目的地址指针
volatile Uint16 *pDMASource_CH1;    // DMA通道1源地址指针
volatile Uint16 *pDMADest_CH2;      // DMA通道2目的地址指针
volatile Uint16 *pDMASource_CH2;    // DMA通道2源地址指针
volatile Uint16 *pDMADest_CH3;      // DMA通道3目的地址指针
volatile Uint16 *pDMASource_CH3;    // DMA通道3源地址指针

void Init_My_DMA(void)
{
    Uint16 i;
    // Initialize DMA
    DMAInitialize();

    pDMADest_CH1 = (volatile Uint16*)DMA_BUF_ADCC;
    pDMADest_CH2 = (volatile Uint16*)DMA_BUF_SPIC;
    pDMADest_CH3 = &SpicRegs.SPITXBUF;//SPIC;

    pDMASource_CH1 = &AdccResultRegs.ADCRESULT0;//ADC-C
    pDMASource_CH2 = &SpicRegs.SPIRXBUF;//SPIC
    pDMASource_CH3 = (volatile Uint16*)DMA_SEND_SPIC;//SPIC-send
         //DMA----ADCC//
    DMACH1AddrConfig(pDMADest_CH1,pDMASource_CH1);
    DMACH1BurstConfig(RESULTS_DATASET_LEN1-1,1,1);
    DMACH1TransferConfig(RESULTS_BUFFER_NUM-1,0,1);
    DMACH1WrapConfig(0,0,RESULTS_BUFFER_NUM-1,0);

    DMACH1ModeConfig(DMA_ADCCINT1,PERINT_ENABLE,ONESHOT_DISABLE,CONT_ENABLE,//Upon receipt of a peripheral interrupt event signal,
                     SYNC_DISABLE,SYNC_SRC,OVRFLOW_DISABLE,SIXTEEN_BIT,     //the DMA will automatically send a clear signal to the
                     CHINT_END,CHINT_DISABLE);                              //interrupt source so that subsequent interrupt events will occur.
       //DMA---SPI----//
    DMACH2AddrConfig(pDMADest_CH2,pDMASource_CH2);
    DMACH2BurstConfig(RESULTS_DATASET_LEN2-1,0,1);//源地址不增加，目标地址增加
    DMACH2TransferConfig(RESULTS_BUFFER_NUM2-1,0,1);
    DMACH2WrapConfig(0,0,RESULTS_BUFFER_NUM2-1,0);

    DMACH2ModeConfig(DMA_SPICRX,PERINT_ENABLE,ONESHOT_DISABLE,CONT_ENABLE,
                     SYNC_DISABLE,SYNC_SRC,OVRFLOW_DISABLE,SIXTEEN_BIT,
                     CHINT_END,CHINT_DISABLE);

    //DMA---SPI----Send//SPI发送
    DMACH3AddrConfig(pDMADest_CH3,pDMASource_CH3);
    //DMACH3AddrConfig(&SpicRegs.SPITXBUF,pDMASource_CH3);
    DMACH3BurstConfig(SEND_DATASET_LEN3-1,1,0);//源地址增加，目标地址不增加。
    DMACH3TransferConfig(SEND_BUFFER_NUM-1,0,0);//源地址不增加，目标地址不增加。
    DMACH3WrapConfig(0,0,SEND_BUFFER_NUM-1,0);

    DMACH3ModeConfig(DMA_EPWM7A,PERINT_ENABLE,ONESHOT_DISABLE,CONT_ENABLE,//  先用DMA_ADCCINT1触发来测试。
                     SYNC_DISABLE,SYNC_SRC,OVRFLOW_DISABLE,SIXTEEN_BIT,
                     CHINT_END,CHINT_DISABLE);

    // Initialize the data buffers
    for (i=0; i<RESULTS_DATASET_LEN1*RESULTS_BUFFER_NUM; i++)
    {
        DMA_BUF_ADCC[i] = 0;
    }

    for (i=0; i<RESULTS_DATASET_LEN2*RESULTS_BUFFER_NUM; i++)
    {
        DMA_BUF_SPIC[i] = 0;
    }


    DMA_SEND_SPIC[0]=(((Uint16)AD2S1210_POSITIONMSB)<<8); //写地址，注意地址最高为1，此时返回旧数据,产生SAMPLE信号
    DMA_SEND_SPIC[1]=(((Uint16)AD2S1210_POSITIONLSB)<<8); //写地址，此时返位置高字节
    DMA_SEND_SPIC[2]=(((Uint16)AD2S1210_FAULT)<<8);       //写地址，传回位置低字节。
    DMA_SEND_SPIC[3]=(((Uint16)AD2S1210_POSITIONMSB)<<8); //写地址，传回位置高字节数据。


    // Start DMA Channel
    StartDMACH1();
    StartDMACH2();
    //StartDMACH3();// 在main函数中使能，设置旋变参数时不启动DMA.

}

void CalFilteredData(Uint16* pIa,Uint16* pIb, Uint16* pIc)
{
    Uint16 *pIabc_buf;
    Uint32 Ia_Sum=0,Ib_Sum=0,Ic_Sum=0;
    Uint16 i;

    pIabc_buf = DMA_BUF_ADCC;


    //ADC-C
    //ADCC: 0:C5---Ia      1:C3---Ib      2:C4---Ic
    //ADCC: 3:C5---Ia      4:C3---Ib      5:C4---Ic
    //ADCC: 6:C5---Ia      7:C3---Ib      8:C4---Ic
    for(i=0;i<(RESULTS_BUFFER_NUM>>1);i++)//循环RESULTS_BUFFER_NUM/2次
    {
        Ia_Sum += *pIabc_buf++;    //累加8次
        Ib_Sum += *pIabc_buf++;
        Ic_Sum += *pIabc_buf++;

        Ia_Sum += *pIabc_buf++;
        Ib_Sum += *pIabc_buf++;
        Ic_Sum += *pIabc_buf++;

        Ia_Sum += *pIabc_buf++;
        Ib_Sum += *pIabc_buf++;
        Ic_Sum += *pIabc_buf++;

        Ia_Sum += *pIabc_buf++;
        Ib_Sum += *pIabc_buf++;
        Ic_Sum += *pIabc_buf++;

        Ia_Sum += *pIabc_buf++;
        Ib_Sum += *pIabc_buf++;
        Ic_Sum += *pIabc_buf++;

        Ia_Sum += *pIabc_buf++;
        Ib_Sum += *pIabc_buf++;
        Ic_Sum += *pIabc_buf++;

        Ia_Sum += *pIabc_buf++;
        Ib_Sum += *pIabc_buf++;
        Ic_Sum += *pIabc_buf++;

        Ia_Sum += *pIabc_buf++;
        Ib_Sum += *pIabc_buf++;
        Ic_Sum += *pIabc_buf++;

    }

    (*pIa) = Ia_Sum >> 3;
    (*pIb) = Ib_Sum >> 3;
    (*pIc) = Ic_Sum >> 3;
}

int16 FirstPosition;
int16 CurrentPosition;
int32 MotPosition = 0;
void CalSPIFilteredData(Uint16* pMotPosition,Uint16* pResoFault)//电机角度接收处理   平均8次
{
    Uint16 *pReso_buf,PosMSB,PosLSB;
    Uint32 ResoFault=0;



    Uint16 i;

    pReso_buf = DMA_BUF_SPIC;
    MotPosition = 0;

    FirstPosition = (((*(pReso_buf+1)) << 8) + (*(pReso_buf+2))) >> 4;
    //SPI-C
    //data1=SpicRegs.SPIRXBUF;//第一个字为旧数据
    //data2=SpicRegs.SPIRXBUF;//取位置高字节
    //data3=SpicRegs.SPIRXBUF;//取位置低字节
    //data4=SpicRegs.SPIRXBUF;//取故障字节
    for(i=0;i<(RESULTS_BUFFER_NUM2>>3);i++)//循环RESULTS_BUFFER_NUM2/8次
    {
        pReso_buf += 1;    //累加8次
        PosMSB= *pReso_buf++;    //数据高字节
        PosLSB= *pReso_buf++;    //数据低字节
        ResoFault = *pReso_buf++;
        CurrentPosition = ((PosMSB<<8)+PosLSB)>>4;//角度值
        if ((FirstPosition - CurrentPosition) > 2048)
        	CurrentPosition += 4096;
        if ((FirstPosition - CurrentPosition) < -2048)
        	CurrentPosition -= 4096;
        MotPosition += CurrentPosition;//角度值

        pReso_buf += 1;
        PosMSB= *pReso_buf++;
        PosLSB= *pReso_buf++;
        ResoFault = *pReso_buf++;
        CurrentPosition = ((PosMSB<<8)+PosLSB)>>4;//角度值
		if ((FirstPosition - CurrentPosition) > 2048)
			CurrentPosition += 4096;
		if ((FirstPosition - CurrentPosition) < -2048)
			CurrentPosition -= 4096;
		MotPosition += CurrentPosition;//角度值

        pReso_buf += 1;
        PosMSB= *pReso_buf++;
        PosLSB= *pReso_buf++;
        ResoFault = *pReso_buf++;
        CurrentPosition = ((PosMSB<<8)+PosLSB)>>4;//角度值
		if ((FirstPosition - CurrentPosition) > 2048)
			CurrentPosition += 4096;
		if ((FirstPosition - CurrentPosition) < -2048)
			CurrentPosition -= 4096;
		MotPosition += CurrentPosition;//角度值

        pReso_buf += 1;
        PosMSB= *pReso_buf++;
        PosLSB= *pReso_buf++;
        ResoFault = *pReso_buf++;
        CurrentPosition = ((PosMSB<<8)+PosLSB)>>4;//角度值
		if ((FirstPosition - CurrentPosition) > 2048)
			CurrentPosition += 4096;
		if ((FirstPosition - CurrentPosition) < -2048)
			CurrentPosition -= 4096;
		MotPosition += CurrentPosition;//角度值

        pReso_buf += 1;
        PosMSB= *pReso_buf++;
        PosLSB= *pReso_buf++;
        ResoFault = *pReso_buf++;
        CurrentPosition = ((PosMSB<<8)+PosLSB)>>4;//角度值
		if ((FirstPosition - CurrentPosition) > 2048)
			CurrentPosition += 4096;
		if ((FirstPosition - CurrentPosition) < -2048)
			CurrentPosition -= 4096;
		MotPosition += CurrentPosition;//角度值

        pReso_buf += 1;
        PosMSB= *pReso_buf++;
        PosLSB= *pReso_buf++;
        ResoFault = *pReso_buf++;
        CurrentPosition = ((PosMSB<<8)+PosLSB)>>4;//角度值
		if ((FirstPosition - CurrentPosition) > 2048)
			CurrentPosition += 4096;
		if ((FirstPosition - CurrentPosition) < -2048)
			CurrentPosition -= 4096;
		MotPosition += CurrentPosition;//角度值

        pReso_buf += 1;
        PosMSB= *pReso_buf++;
        PosLSB= *pReso_buf++;
        ResoFault = *pReso_buf++;
        CurrentPosition = ((PosMSB<<8)+PosLSB)>>4;//角度值
		if ((FirstPosition - CurrentPosition) > 2048)
			CurrentPosition += 4096;
		if ((FirstPosition - CurrentPosition) < -2048)
			CurrentPosition -= 4096;
		MotPosition += CurrentPosition;//角度值

        pReso_buf += 1;
        PosMSB= *pReso_buf++;
        PosLSB= *pReso_buf++;
        ResoFault = *pReso_buf++;
        CurrentPosition = ((PosMSB<<8)+PosLSB)>>4;//角度值
		if ((FirstPosition - CurrentPosition) > 2048)
			CurrentPosition += 4096;
		if ((FirstPosition - CurrentPosition) < -2048)
			CurrentPosition -= 4096;
		MotPosition += CurrentPosition;//角度值

    }
    if (MotPosition < 0)
    {
    	MotPosition += 65536L;//4096L<<4; // 4096<<5
    }

    (*pMotPosition) = MotPosition >> 4;//总共2*8次，右移4位等于1次。
    (*pResoFault) = ResoFault;///故障位读取

}
