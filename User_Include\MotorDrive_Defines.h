/*
 * All global defines in this file
 */

#ifndef USER_INCLUDE_MOTORDRIVE_DEFINES_H_
#define USER_INCLUDE_MOTORDRIVE_DEFINES_H_

#define TWO_PI                  6.283185307179586476925286766559

#define VOLTAGE_FILTER_COEF     0.9f
#define VOLTAGE_INPUT_COEF     (1.0-VOLTAGE_FILTER_COEF)

#define CURRENT_FILTER_COEF     0.0f
#define CURRENT_INPUT_COEF     (1.0-CURRENT_FILTER_COEF)

#define CNT_485    1

#define GET_BIT(x, bit) ((x & (1 << bit)) >> bit)   /* 获取第bit位 */

#define Angle_COEF    360/4096          // 角度数据到实际角度转换系数。
#define Rad_COEF      3.1415926/180     // 度数到弧度的转换系数。
#define RpstoRpm_COEF_Base 9.549296f
//#define RpstoRpm_COEF 2.387324f      //4对极系数
//#define RpstoRpm_COEF 1.591512f      //6对极系数
//#define RpstoRpm_COEF 1.193662f        //8对极

//NTC 样条插值系数

#define NTCp1    -2.97271558063782e-08
#define NTCp2    0.000117169638530769
#define NTCp3    -0.182983058030583
#define NTCp4    139.978897422134




//250kW 电机参数
#define MOTOR_LD      0.00009644f//0.00012457
#define MOTOR_LQ      0.00009977f//0.00014936
#define MOTOR_FLUX    0.0862f//0.1559 永磁体磁链
#define MOTOR_RS      0.0083f//0.009
#define MOTOR_PN      8     //6
#define MOTOR_RESOLVER_ZERO 0.0f

#endif
