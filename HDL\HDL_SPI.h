/**
 * @file HDL_SPI.h
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-07-16
 * @last modified 2025-07-16
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#ifndef HDL_SPI_H
#define HDL_SPI_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>
    typedef uint16_t byte_t;
#define UNUSED(X) (void)X /* To avoid gcc/g++ warnings */

    void SPI_Init(void);
    bool HDL_SPI_WriteRead(byte_t *pTxData, byte_t *pRxData, uint16_t size);
    void HDL_SPI_SELECT();
    void HDL_SPI_DESELECT();
#ifdef __cplusplus
}
#endif
#endif //! HDL_SPI_H
