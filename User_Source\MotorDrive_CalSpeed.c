/*
 * MotorDrive_150kW_CalSpeed.c
 *
 *  Created on: 2023年3月4日
 *      Author: <PERSON><PERSON> Li
 *
 *      Modified V1.1 : 2023年5月10日
 *      角度的平均滤波器改为增量滤波器，解决角度在2*pi和0之间的跳动问题
 */


#include "MotorDrive_CalSpeed.h"
#include "F28x_Project.h"

Struct_Cal_Speed mCalSpeed;

AVG_f32 mOmegaChanging;
float mOmegaChangingBuf[SPEED_CHANGING_FILTER_ORDER];
//
void InitCalSpeed(float sample_time)
{

    mCalSpeed.Omega_Estimate = 0.0f;
    mCalSpeed.Omega_Estimate_Filtered = 0.0f;
    mCalSpeed.Omega_Estimate_Last = 0.0f;
    mCalSpeed.Omega_Estimate_Integral = 0.0f;
    mCalSpeed.Theta_Estimate = 0.0f;
    mCalSpeed.Theta_Virtual = 0.0f;
    mCalSpeed.Theta_Last = 0.0f;
    mCalSpeed.Sample_Time = sample_time;
    mCalSpeed.Inv_Sample_Time = 1.0f/sample_time;
    mCalSpeed.Omega_Kp = CAL_SPEED_OMEGA_KP;
    mCalSpeed.Omega_Ki = CAL_SPEED_OMEGA_KI;
    mCalSpeed.Omega_Kd = CAL_SPEED_OMEGA_KD;

    AVG_FILTER_init(&mOmegaChanging,0.0f,SPEED_CHANGING_FILTER_ORDER,mOmegaChangingBuf);
}
float CalSpeed(float *theta)
{
    float dTheta;

    // 判断估算角度和测量角度误差
    //dTheta = *theta - theta_estimate;


    dTheta = *theta - mCalSpeed.Theta_Last;

    if(dTheta > 1.9f * CAL_SPEED_PI)
    {
        dTheta -= 2.0f * CAL_SPEED_PI;
    }
    if(dTheta < -1.9f * CAL_SPEED_PI)
    {
        dTheta += 2.0f * CAL_SPEED_PI;
    }

    mCalSpeed.Theta_Last = *theta;
    mCalSpeed.Omega_Estimate = mCalSpeed.Omega_Estimate*0.995f + dTheta*mCalSpeed.Inv_Sample_Time*0.005f;
    return mCalSpeed.Omega_Estimate;
}

float tune_variable = 0.005f;
float CalSpeed_Model_Simple(float theta)
{
    float dTheta;



    mCalSpeed.Theta_Virtual = mCalSpeed.Theta_Virtual + mCalSpeed.Omega_Estimate * mCalSpeed.Sample_Time;


    if(mCalSpeed.Theta_Virtual - theta > 1.1f * CAL_SPEED_PI)
        mCalSpeed.Theta_Virtual -= 2.0f * CAL_SPEED_PI;
    if(mCalSpeed.Theta_Virtual - theta < -1.1f * CAL_SPEED_PI)
        mCalSpeed.Theta_Virtual += 2.0f * CAL_SPEED_PI;

    mCalSpeed.Theta_Virtual += (theta-mCalSpeed.Theta_Virtual)*0.014f;

    while(mCalSpeed.Theta_Virtual<0.0f)
        mCalSpeed.Theta_Virtual += 2.0f * CAL_SPEED_PI;

    while(mCalSpeed.Theta_Virtual>2.0f * CAL_SPEED_PI)
        mCalSpeed.Theta_Virtual -= 2.0f * CAL_SPEED_PI;

    dTheta = mCalSpeed.Theta_Virtual - mCalSpeed.Theta_Last;

    if(dTheta > 1.2f * CAL_SPEED_PI)
    {
        dTheta -= 2.0f * CAL_SPEED_PI;
    }
    if(dTheta < -1.2f * CAL_SPEED_PI)
    {
        dTheta += 2.0f * CAL_SPEED_PI;
    }


    mCalSpeed.Omega_Estimate = mCalSpeed.Omega_Estimate*0.9f + dTheta*mCalSpeed.Inv_Sample_Time*0.1f;// 0.9f
    mCalSpeed.Theta_Virtual += (mCalSpeed.Omega_Estimate - mCalSpeed.Omega_Estimate_Last)*tune_variable;
    mCalSpeed.Theta_Last = mCalSpeed.Theta_Virtual;

//    AVG_FILTER_calc(&mOmegaChanging,mCalSpeed.Omega_Estimate-mCalSpeed.Omega_Estimate_Last);
    mCalSpeed.Omega_Estimate_Last = mCalSpeed.Omega_Estimate;

    mCalSpeed.Omega_Estimate_Filtered = mCalSpeed.Omega_Estimate_Filtered*0.95f + mCalSpeed.Omega_Estimate*0.05f;


    return mCalSpeed.Omega_Estimate_Filtered;
}

void AngleDACOut(float theta)
{
    EPwm4Regs.CMPA.bit.CMPA = 50 + (Uint16)(theta*57.2957f+0.5f);
}

void CurrentDACOut(Uint16 current)
{
	EPwm4Regs.CMPA.bit.CMPA = current/6;
}

float CalSpeed_Model(float theta)
{
    // 计算角度变化
    mCalSpeed.Theta_diff = theta - mCalSpeed.Theta_Last;

    // 判断是否在2PI和0之间发生了跳变，有跳变则进行2PI的处理
    if(mCalSpeed.Theta_diff > 1.9f*CAL_SPEED_PI)
    {
        mCalSpeed.Theta_diff -= 2.0f*CAL_SPEED_PI;
    }
    else if(mCalSpeed.Theta_diff < -1.9f*CAL_SPEED_PI)
    {
        mCalSpeed.Theta_diff += 2.0f*CAL_SPEED_PI;
    }
    else
    {
        // 如果角度变化过大，则认为是数据错误，通过内部模型计算角度变化量
        if(mCalSpeed.Theta_diff>0.1f*CAL_SPEED_PI || mCalSpeed.Theta_diff<-0.1f*CAL_SPEED_PI)
            mCalSpeed.Theta_diff = mCalSpeed.Omega_Estimate * mCalSpeed.Sample_Time;
    }
    // 将新角度存入，用于下个周期计算角度变化量
    mCalSpeed.Theta_Last = theta;
    // 根据角度变化量，计算内部虚拟角度值
    mCalSpeed.Theta_Virtual += mCalSpeed.Theta_diff;
    // 计算虚拟角度值和观测角度值之间的差值
    mCalSpeed.Theta_Err = mCalSpeed.Theta_Virtual - mCalSpeed.Theta_Estimate;

    mCalSpeed.Omega_Estimate_Integral += mCalSpeed.Theta_Err * mCalSpeed.Omega_Ki;
    mCalSpeed.Omega_Estimate_Integral = __fmax(mCalSpeed.Omega_Estimate,-CAL_SPEED_MAX_OMEGA);
    mCalSpeed.Omega_Estimate_Integral = __fmin(mCalSpeed.Omega_Estimate,CAL_SPEED_MAX_OMEGA);
    mCalSpeed.Omega_Estimate = mCalSpeed.Omega_Estimate_Integral + mCalSpeed.Theta_Err * mCalSpeed.Omega_Kp
            - (mCalSpeed.Omega_Estimate-mCalSpeed.Omega_Estimate_Last) * mCalSpeed.Omega_Kd;

    mCalSpeed.Omega_Estimate_Last = mCalSpeed.Omega_Estimate;

    mCalSpeed.Theta_Estimate += mCalSpeed.Omega_Estimate * mCalSpeed.Sample_Time;

    if(mCalSpeed.Theta_Estimate > 2.0f*CAL_SPEED_PI && mCalSpeed.Theta_Virtual > 2.0f*CAL_SPEED_PI)
    {
        mCalSpeed.Theta_Estimate -= 2.0f * CAL_SPEED_PI;
        mCalSpeed.Theta_Virtual -= 2.0f * CAL_SPEED_PI;
    }
    if(mCalSpeed.Theta_Estimate < -2.0f*CAL_SPEED_PI && mCalSpeed.Theta_Virtual < -2.0f*CAL_SPEED_PI)
    {
        mCalSpeed.Theta_Estimate += 2.0f * CAL_SPEED_PI;
        mCalSpeed.Theta_Virtual += 2.0f * CAL_SPEED_PI;
    }


    return mCalSpeed.Omega_Estimate;
}
