/**
 * @file HDL_CPU_Time.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2024-04-26
 * @last modified 2024-04-26
 *
 * @copyright Copyright (c) 2024 Liu <PERSON> Personal.
 *
 */
#include "HDL_CPU_Time.h"
#include "CPU_Define.h"
#include <limits.h>
#include <stddef.h>

//
// Function Prototype statements
//
__interrupt void cpu_timer2_isr(void);

static CPU_Time_Callback_t _gCPUTickCallback =
    NULL; // CPU tick定时器的每次中断回调的函数指针。
/* 保存 TIM定时中断到后执行的回调函数指针 */
static CPU_Time_Callback_t s_TIM_CallBack1;
static bool s_TIM1Busy = false;
static bool g_TimerInited = false;
//
// ConfigCpuTimer - This function initializes the selected timer to the period
// specified by the "Freq" and "Period" parameters. The "Freq" is entered as
// "MHz" and the period in "uSeconds". The timer is held in the stopped state
// after configuration.
//
static void ConfigTheCpuTimer(struct CPUTIMER_VARS *Timer, uint16_t TDDR,
                              Uint32 PRD, int enableInt)
{
  //
  // Initialize timer period
  //
  Timer->CPUFreqInMHz = 200;
  Timer->PeriodInUSec = PRD + 1;

  //
  // Set pre-scale counter to divide by Freq (SYSCLKOUT)
  //
  Timer->RegsAddr->TPRH.bit.TDDRH = TDDR >> 8;
  Timer->RegsAddr->TPR.bit.TDDR = TDDR;

  // Ftimer = SYSCLKOUT / (PRD + 1) / (TPR + 1)
  //
  // Counter decrements PRD+1 times each period
  //
  Timer->RegsAddr->PRD.all = PRD;

  //
  // Initialize timer control register
  //

  //
  // 1 = Stop timer, 0 = Start/Restart Timer
  //
  Timer->RegsAddr->TCR.bit.TSS = 1;

  Timer->RegsAddr->TCR.bit.TRB = 1; // 1 = reload timer
  Timer->RegsAddr->TCR.bit.SOFT = 1;
  Timer->RegsAddr->TCR.bit.FREE = 1; // Timer Free Run

  //
  // 0 = Disable/ 1 = Enable Timer Interrupt
  //
  Timer->RegsAddr->TCR.bit.TIE = enableInt > 0 ? 1 : 0;

  //
  // Reset interrupt counter
  //
  Timer->InterruptCount = 0;
}

void HDL_CPU_Time_Init()
{

  if (g_TimerInited)
  {
    return;
  }
  //
  // Interrupts that are used in this example are re-mapped to
  // ISR functions found within this file.
  //
  EALLOW; // This is needed to write to EALLOW protected registers
  PieVectTable.TIMER2_INT = &cpu_timer2_isr;
  EDIS; // This is needed to disable write to EALLOW protected registers

  //
  // Step 4. Initialize the Device Peripheral. This function can be
  //         found in DSP2833x_CpuTimers.c
  // Initialize address pointers to respective timer registers:
  CpuTimer2.RegsAddr = &CpuTimer2Regs;

#if (CPU_FRQ_200MHZ)
  //
  // Configure CPU-Timer 2:
  // 200MHz CPU Freq
  //
  ConfigTheCpuTimer(&CpuTimer2, 200 - 1, 0xFFFFFFFFUL, 0); // 1us
#endif

  //
  // To ensure precise timing, use write-only instructions to write to the
  // entire register. Therefore, if any of the configuration bits are changed
  // in ConfigCpuTimer and InitCpuTimers (in DSP2833x_CpuTimers.h), the
  // below settings must also be updated.
  //
  // To start or restart the CPU-timer, set TSS to 0. At reset, TSS is cleared
  // to 0 and the CPU-timer immediately starts
  CpuTimer2Regs.TCR.bit.TSS = 0; // write-only instruction to set TSS bit = 0

  //
  // Step 5. User specific code, enable interrupts
  //

  //
  // Enable CPU int1 which is connected to CPU-Timer 0, CPU int13
  // which is connected to CPU-Timer 1, and CPU int 14, which is connected
  // to CPU-Timer 2:
  //
  IER |= M_INT14; // CPU-Timer2 (for TI/RTOS use)

  g_TimerInited = true;
}

UsTimer_t HDL_CPU_Time_GetUsTick()
{
  //  UsTimer_t ret = 0xFFFFFFFFUL - CpuTimer2.RegsAddr->TIM.all;
  return ~CpuTimer2.RegsAddr->TIM.all; // 按位取反，等效于 0xFFFFFFFF - x
}

void HDL_CPU_Time_ResetUsTick()
{
  //
  // Reload all counter register with period value
  //
  CpuTimer2.RegsAddr->TCR.bit.TRB = 1;
}

void HDL_CPU_Time_SetCPUTickCallback(CPU_Time_Callback_t _pCallBack);

/**
 * @brief
 * Microsecond delay function using the hardware timer's registers. 
 * Note that in Debug mode, even if the execution is stopped at a 
 * breakpoint, this timer continues to run. Additionally, 
 * it is necessary to determine the bit width of the timer 
 * counter register used, which can be referenced by 
 * @US_TIMER_BITWIDE. This macro definition needs to be 
 * modified when porting the program.
 *
 * @param DelayUs
 */
void HDL_CPU_Time_DelayUs(UsTimer_t DelayUs)
{
  UsTimer_t tickstart = HDL_CPU_Time_GetUsTick();
  UsTimer_t wait = DelayUs;

  while ((HDL_CPU_Time_GetUsTick() - tickstart) < wait)
  {
  }
}

/**
 * @brief This function handles CPU_US_TIM global interrupt.设置CPU
 * tick定时器的每次中断回调 的函数。
 * @retval None
 */
void HDL_CPU_Time_SetCPUTickCallback(CPU_Time_Callback_t _pCallBack)
{
  _gCPUTickCallback = _pCallBack;
}

//
// cpu_timer2_isr -
//
__interrupt void cpu_timer2_isr(void)
{
  EALLOW;
  CpuTimer2.InterruptCount++;

  // Disable this interrupt
  CpuTimer2.RegsAddr->TCR.bit.TIE = 0;

  /* 先关闭中断，再执行回调函数。因为回调函数可能需要重启定时器 */
  if (s_TIM_CallBack1 != NULL)
  {
    s_TIM_CallBack1();
  }

  s_TIM1Busy = false;

  //
  // The CPU acknowledges the interrupt.
  //
  EDIS;
}
