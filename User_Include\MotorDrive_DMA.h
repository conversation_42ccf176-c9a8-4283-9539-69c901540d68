/*
 * MotorDrive_DMA.h
 *
 *  Created on: 
 *      Author: KUST410_2
 *      
 */

#ifndef USER_INCLUDE_MOTORDRIVE_DMA_H_
#define USER_INCLUDE_MOTORDRIVE_DMA_H_

#ifdef __cplusplus
extern "C" {
#endif

//  Includes
#include "F28x_Project.h"


#ifdef _FLASH
#pragma CODE_SECTION(CalFilteredData, ".TI.ramfunc");
#pragma CODE_SECTION(CalSPIFilteredData, ".TI.ramfunc");


#endif

#define RESULTS_DATASET_LEN1    12   //ADCC
#define RESULTS_DATASET_LEN2    4   //SPIC
#define SEND_DATASET_LEN3       4   //SPIC--send
#define RESULTS_BUFFER_NUM      16
#define RESULTS_BUFFER_NUM2     16
#define SEND_BUFFER_NUM         1   

#define RESULTS_BUFFER_SIZE1    (RESULTS_DATASET_LEN1*RESULTS_BUFFER_NUM)
#define RESULTS_BUFFER_SIZE2    (RESULTS_DATASET_LEN2*RESULTS_BUFFER_NUM2)
#define SEND_BUFFER_SIZE3       (SEND_DATASET_LEN3*SEND_BUFFER_NUM)

void Init_My_DMA(void);
void CalFilteredData(Uint16* pIa,Uint16* pIb, Uint16* pIc);
void CalSPIFilteredData(Uint16* pPosition,Uint16* pFault);

#ifdef __cplusplus
}
#endif /* extern "C" */

#endif /* USER_INCLUDE_MOTORDRIVE_350KW_DMA_H_ */
