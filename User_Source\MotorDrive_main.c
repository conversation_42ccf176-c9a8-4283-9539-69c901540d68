//=======================================================
//    Project：         MotorDrive_150kW
//    File:                 MotorDrive_150kW_main.c
//    Created on:     2022
//    By：                SLu
//    Describe：       主程序
//=======================================================

//====================== Include ========================//
#include "CPU_Define.h"
#include "BFL_DebugPin.h"
#include "scheduler.h"
#include "HDL_CPU_Time.h"
#include "APP_Net.h"
#include "ShareParasCPU.h"

//CPU配置:0单核，1双核
#define DUAL_CORE_CPU  (1)
#if DUAL_CORE_CPU
#pragma DATA_SECTION(ParasToCPU2,"CPU1_To_CPU2");
PQC_IPC_ToCPU2 ParasToCPU2 = PQC_IPC_ToCPU2_DEFAULTS;
#pragma DATA_SECTION(ParasToCPU1,"CPU2_To_CPU1");
PQC_IPC_ToCPU1 ParasToCPU1;
#endif // DUAL_CORE_CPU

extern uint16_t switch_loadstart, switch_loadsize, switch_runstart;

void main(void)
{

    /*
     * 1. 基本时钟、中断初始化
     * 2. IO初始化。
     * 3. 初始化CPU2。
     * 3. 尝试加载保存的参数。
     * 5. 其他外设初始化。
     */
    // Step 1. Initialize System Control:
    InitSysCtrl(); // PLL, WatchDog, enable Peripheral Clocks

    // 复位CPU2
    EALLOW;
    DevCfgRegs.CPU2RESCTL.all = 0xa5a50001;  // KEY=0xa5a5, RESET=1
    EDIS;

    // load switch address table into RAM
    memcpy(&switch_runstart, &switch_loadstart, (size_t)&switch_loadsize);
    InitUserStoreVar();

    Uint32 uid_u32;
    uid_u32 = GetUID0();
    if (uid_u32 == 0x052339C3)
    {
        //1号电机
        mVar_RAM.Motor_Parameters.Motor_Resolver_Zero = 101;
    }
    else if (uid_u32 == 0x04F05988)
    {
        //2号电机
        mVar_RAM.Motor_Parameters.Motor_Resolver_Zero = 214;
    }else if (uid_u32 == 3)
    {
        //3号电机
        mVar_RAM.Motor_Parameters.Motor_Resolver_Zero = 214;
    }else if (uid_u32 == 4)
    {
        //4号电机
        mVar_RAM.Motor_Parameters.Motor_Resolver_Zero = 214;
    }else if (uid_u32 == 5)
    {
        //5号电机
        mVar_RAM.Motor_Parameters.Motor_Resolver_Zero = 214;
    }
    Init_Motor_Ctrl_Variables();

    // Step 2. Initialize GPIO:
    InitGpio();
    InitMyGpio(); // 用户IO初始化vc

    // Step 3. Clear all interrupts and initialize PIE vector table:
    DINT;               // Disable CPU interrupts
    InitPieCtrl();      // Initialize the PIE control registers to their default state.
    IER = 0x0000;       // Disable CPU interrupts
    IFR = 0x0000;       // Clear all CPU interrupt flags
    InitPieVectTable(); // Initialize the PIE vector table with pointers to the shell Interrupt Service Routines (ISR).

    // Interrupts that are used are re-mapped to ISR functions found within this file.
    EALLOW;
    PieVectTable.ADCA1_INT = &Adca1_Isr;

    EDIS;

    MotorDrive_Ctrl_PI_init(); // 控制参数初始化

    // Step 4. Initialize all the Device Peripherals:
    InitPeripheral(); // 初始化外设：

    // Ensure DMA is connected to Peripheral Frame 2 bridge (EALLOW protected)
    //
    EALLOW;
    CpuSysRegs.SECMSEL.bit.PF2SEL = 1; // 这一句很关键，没有这一句不好使。
    EDIS;


    APP_Net_Init(); // Initialize network settings

    // 释放CPU2复位
    EALLOW;
    DevCfgRegs.CPU2RESCTL.all = 0xa5a50000;  // KEY=0xa5a5, RESET=0
    EDIS;
#if DUAL_CORE_CPU
    // 针对CPU2 Bootlader的补丁，这样才能在软件复位CPU2之后正常启动CPU2
    ParasToCPU2.CPU2_UpgradeEnable = 0;
    ParasToCPU2.CPU2_EntryApp_Enable = 1;
#endif // DUAL_CORE_CPU

    BFL_DebugPin_Set(DEBUG_PIN_1);
    HDL_CPU_Time_DelayUs(200000);
    BFL_DebugPin_Toggle(DEBUG_PIN_1);
    HDL_CPU_Time_DelayUs(200000);
    BFL_DebugPin_Toggle(DEBUG_PIN_1);
    HDL_CPU_Time_DelayUs(200000);
    BFL_DebugPin_Set(DEBUG_PIN_1);
    // Wait for CPU2 to start up
// #ifdef _STANDALONE
#ifdef _FLASH
    //    Send boot command to allow the CPU02 application to begin execution
    IPCBootCPU2(C1C2_BROM_BOOTMODE_BOOT_FROM_FLASH);
#else
    //    Send boot command to allow the CPU02 application to begin execution
    IPCBootCPU2(C1C2_BROM_BOOTMODE_BOOT_FROM_RAM);
// #endif //_STANDALONE
#endif //_FLASH
    APP_Net_Wait_CPU2_Init(100000);

    MCV.Iset_q_ref = 0;
    
    InitCANACANB(); // 初始化CANA和CANB

    INIT_TIMER0(); // 继电器定时
    INIT_TIMER1(); // 故障返回定时

    // Step 5. User specific code, enable interrupts
    // Enable CPU INT:
    IER |= M_INT1; // Enable CPU INT1 which is connected to CPU-Timer 0 & ADC1

    // Enable PIE INT:
    PieCtrlRegs.PIEIER1.bit.INTx1 = 1; // Enable ADC1           in the PIE: Group 1 interrupt 1

    // 复位CPLD和驱动复位
    CPLD_FLT_CLR;
    RST1_LOW = 1; // 驱动器复位引脚为低
    DELAY_US(100000);
    DELAY_US(100000);
    //  CPLD 复位结束
    Dis_CPLD_FLT_CLR;
    RST1_HIGH = 1; // 驱动器复位引脚为高

    InitCalSpeed(1.0f / ((5000.0f / (float)mVar_RAM.EPWM_PERIOD_Base) * 10000.0f)); // 旋变滤波初始化
    ABC_DQ0_POS_F_init(&abc_dq0_pos1);                                              // ABC_DQ0变换初始化
    ABC_DQ0_POS_F_init(&abc_dq0_pos2);                                              // ABC_DQ0变换初始化

    iPARK_F_init(&ipark1); // 反park变换初始化

    ScibRegs.SCICTL1.bit.SWRESET = 0; // reset SCI.
    ScicRegs.SCICTL1.bit.SWRESET = 0; // reset SCI.
    ScidRegs.SCICTL1.bit.SWRESET = 0; // reset SCI.
    F28x_usDelay(1);
    ScibRegs.SCICTL1.bit.SWRESET = 1; // Re-eanble SCI.
    ScicRegs.SCICTL1.bit.SWRESET = 1; // Re-eanble SCI.
    ScidRegs.SCICTL1.bit.SWRESET = 1; // Re-eanble SCI.
    while (ScibRegs.SCIFFRX.bit.RXFFST > 0)
    {
        (void)ScibRegs.SCIRXBUF.all; // 读出FIFO中所有数据，但并不使用只是清空
    }
    while (ScicRegs.SCIFFRX.bit.RXFFST > 0)
    {
        (void)ScicRegs.SCIRXBUF.all; // 读出FIFO中所有数据，但并不使用只是清空
    }
    while (ScidRegs.SCIFFRX.bit.RXFFST > 0)
    {
        (void)ScidRegs.SCIRXBUF.all; // 读出FIFO中所有数据，但并不使用只是清空
    }

    AD2S1210Init();

    PeriodREC_t rec1 = 0;
    while (1)
    {
        // Poll the network for incoming data
        APP_Net_Poll();
        if (period_query_user(&rec1, MS_TO_US(500)))
        {
            BFL_DebugPin_Toggle(DEBUG_PIN_1);
        }

        ProcessCANA();
        ProcessCANB();

        ProcessSCIB(); // RXIN_4-----驱动3
        ProcessSCIC(); // RXIN_3-----驱动2
        ProcessSCID(); // RXIN_2-----驱动1
        Process_SW1();

        INV_Ubase = 1.732f / MCV.Vbus_filter;
    }
}

//=======================================================
//                End of file.
//=======================================================
