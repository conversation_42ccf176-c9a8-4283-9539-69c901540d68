/**
 * @file HDL_SPI.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-07-24
 * @last modified 2025-07-24
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#include "HDL_SPI.h"
#include "F28x_Project.h"
#include "w5500.h"
#include <string.h>
#include "HDL_CPU_Time.h"

// SPI配置参数
#define SPI_BAUDRATE 100000UL     // 12.5MHz SPI时钟
#define SPI_CLOCKSOURCE 200000000 // 200MHz LSPCLK

// GPIO引脚定义
#define W5500_INT_GPIO GPIO73 // INT_L
#define W5500_RST_GPIO GPIO74 // RST_L
#define SPIA_SCS_GPIO GPIO61  // SPIA_SCS
#define SPIA_MOSI_GPIO GPIO58 // SPIA_MOSI
#define SPIA_MISO_GPIO GPIO59 // SPIA_MISO
#define SPIA_SCLK_GPIO GPIO60 // SPIA_SCLK
/* ---------- SPIA DMA 缓冲区 ---------- */
//
// Globals
//
#pragma DATA_SECTION(sdata, "ramgs_dmach4"); // map the TX data to memory
#pragma DATA_SECTION(rdata, "ramgs_dmach5"); // map the RX data to memory
#define SPIA_DMA_LEN 512u              // 一次 DMA 的字(word)数
Uint16 sdata[SPIA_DMA_LEN];            // Send data buffer
Uint16 rdata[SPIA_DMA_LEN];            // Receive data buffer
volatile Uint16 *DMADest;
volatile Uint16 *DMASource;

//
// Defines
//
#define BURST (FIFO_LVL - 1)                     // burst size should be less than 8
#define TRANSFER ((SPIA_DMA_LEN / FIFO_LVL) - 1) // [(MEM_BUFFER_SIZE/FIFO_LVL)-1]
#define FIFO_LVL 1                               // FIFO Interrupt Level

// 函数声明

// SPI初始化函数
void SPI_CLK_Init(void)
{
    EALLOW;
    // 使能SPI-A时钟
    CpuSysRegs.PCLKCR8.bit.SPI_A = 1;
//    ClkCfgRegs.LOSPCP.bit.LSPCLKDIV = 0;
    EDIS;
}

void SPI_GPIO_Init(void)
{
#ifdef CPU1
    // 配置GPIO引脚功能
    EALLOW;

    // SPIA_MOSI
    GpioCtrlRegs.GPBMUX2.bit.SPIA_MOSI_GPIO = 3;  // 设置为SPI功能
    GpioCtrlRegs.GPBGMUX2.bit.SPIA_MOSI_GPIO = 3; // 高位设置
    GpioCtrlRegs.GPBDIR.bit.SPIA_MOSI_GPIO = 0;   // 输出
    GpioCtrlRegs.GPBPUD.bit.SPIA_MOSI_GPIO = 0;   // 使能上拉

    // SPIA_SCLK
    GpioCtrlRegs.GPBMUX2.bit.SPIA_SCLK_GPIO = 3;  // 设置为SPI功能
    GpioCtrlRegs.GPBGMUX2.bit.SPIA_SCLK_GPIO = 3; // 高位设置
    GpioCtrlRegs.GPBDIR.bit.SPIA_SCLK_GPIO = 0;   // 输出
    GpioCtrlRegs.GPBPUD.bit.SPIA_SCLK_GPIO = 0;   // 使能上拉

    // SPIA_MISO
    GpioCtrlRegs.GPBMUX2.bit.SPIA_MISO_GPIO = 3;  // 设置为SPI功能
    GpioCtrlRegs.GPBGMUX2.bit.SPIA_MISO_GPIO = 3; // 高位设置
    GpioCtrlRegs.GPBDIR.bit.SPIA_MISO_GPIO = 0;   // 输入
    GpioCtrlRegs.GPBPUD.bit.SPIA_MISO_GPIO = 0;   // 使能上拉
    GpioCtrlRegs.GPBQSEL2.bit.SPIA_MISO_GPIO = 3; // 异步输入

    // 普通IO作为片选信号（不是SPIA_SCS）
    GpioCtrlRegs.GPBMUX2.bit.GPIO61 = 0;       // 设置为SPI功能
    GpioCtrlRegs.GPBGMUX2.bit.GPIO61 = 0;      // 高位设置
    GpioCtrlRegs.GPBDIR.bit.SPIA_SCS_GPIO = 1; // 输出
    GpioCtrlRegs.GPBPUD.bit.SPIA_SCS_GPIO = 0; // 使能上拉
    GpioDataRegs.GPBSET.bit.SPIA_SCS_GPIO = 1; // 初始化为高电平(未选中)

    EDIS;
#endif // CPU1
}

// SPI外设级别复位
void SPIA_Peripheral_Reset(void)
{
#ifdef CPU1
    EALLOW;
    // 1. 通过SOFTPRES8寄存器复位SPI-A外设
    // SOFTPRES8.bit.SPI_A = 1 表示复位SPI-A外设
    DevCfgRegs.SOFTPRES8.bit.SPI_A = 1; // 置位进行复位
    HDL_CPU_Time_DelayUs(10);                       // 保持复位状态
    DevCfgRegs.SOFTPRES8.bit.SPI_A = 0; // 清零释放复位
    HDL_CPU_Time_DelayUs(100);                      // 等待复位完成
    EDIS;
#endif
}

// SPI初始化函数
void SPI_Init(void)
{
    SpiaRegs.SPIFFTX.bit.SPIRST = 0; // 复位RX FIFO
    SpiaRegs.SPIFFTX.bit.SPIRST = 1; // 释放复位
    //
    // Initialize SPI FIFO registers
    //
    SpiaRegs.SPIFFRX.all = 0x2040;          // RX FIFO enabled, clear FIFO int
    SpiaRegs.SPIFFRX.bit.RXFIFORESET = 0;   // 复位RX FIFO
    SpiaRegs.SPIFFRX.bit.RXFIFORESET = 1;   // 释放复位
                                            //    SpiaRegs.SPIFFRX.bit.RXFFINTCLR = 1;   // 释放复位
    SpiaRegs.SPIFFRX.bit.RXFFIL = FIFO_LVL; // Set RX FIFO level

    SpiaRegs.SPIFFTX.all = 0xE040;          // FIFOs enabled, TX FIFO released,
    SpiaRegs.SPIFFTX.bit.TXFIFO = 0;        // 释放复位
    SpiaRegs.SPIFFTX.bit.TXFIFO = 1;        // 释放复位
                                            //    SpiaRegs.SPIFFTX.bit.TXFFINTCLR = 1;
    SpiaRegs.SPIFFTX.bit.TXFFIL = FIFO_LVL; // Set TX FIFO level

    // 配置SPI寄存器
    SpiaRegs.SPICCR.bit.SPISWRESET = 0; // SPI复位状态

    // 配置SPI控制寄存器
    SpiaRegs.SPICCR.bit.CLKPOLARITY = 0; // 时钟极性 - 空闲时为低电平
    SpiaRegs.SPICCR.bit.SPICHAR = 7;     // 8位数据传输 (7+1=8)
    SpiaRegs.SPICCR.bit.SPILBK = 0;      // 禁用环回模式
    SpiaRegs.SPICCR.bit.HS_MODE = 1;

    SpiaRegs.SPICTL.bit.MASTER_SLAVE = 1;  // 主模式
    SpiaRegs.SPICTL.bit.CLK_PHASE = 1;     // 时钟相位 - 第一个边沿采样
    SpiaRegs.SPICTL.bit.OVERRUNINTENA = 0; // 禁用溢出中断
    SpiaRegs.SPICTL.bit.SPIINTENA = 0;
    SpiaRegs.SPICTL.bit.TALK = 1; // 使能发送

    // 配置SPI时钟
    // Uint16 spi_brr = ((200E6 / 4) / SPI_BAUDRATE) - 1;
    SpiaRegs.SPIBRR.bit.SPI_BIT_RATE = 0; // spi_brr;

    // 配置优先级控制
    SpiaRegs.SPIPRI.bit.FREE = 1; // 自由运行模式
    SpiaRegs.SPIPRI.bit.SOFT = 0;

    SpiaRegs.SPIFFCT.all = 0x0; // 禁用FIFO延迟

    // 释放SPI复位
    SpiaRegs.SPICCR.bit.SPISWRESET = 1;
}

void SPI_DMA_Init(void)
{
    DMASource = (volatile Uint16 *)sdata;
    DMADest = (volatile Uint16 *)rdata;

    /* 2. CH4：SPIA-RX  DMA 配置 -------------------- */
    DMACH4AddrConfig(DMADest, &SpiaRegs.SPIRXBUF);
    DMACH4BurstConfig(BURST, 0, 1);
    DMACH4TransferConfig(TRANSFER, 0, 1);
    DMACH4ModeConfig(DMA_SPIARX, PERINT_ENABLE, ONESHOT_DISABLE, CONT_DISABLE,
                     SYNC_DISABLE, SYNC_SRC, OVRFLOW_DISABLE, SIXTEEN_BIT,
                     CHINT_END, CHINT_DISABLE);

    /* 3. CH5：SPIA-TX  DMA 配置 -------------------- */
    DMACH5AddrConfig(&SpiaRegs.SPITXBUF, DMASource);
    DMACH5BurstConfig(BURST, 1, 0);       // Burst size, src step, dest step
    DMACH5TransferConfig(TRANSFER, 1, 0); // transfer size, src step, dest step
    DMACH5ModeConfig(DMA_SPIATX, PERINT_ENABLE, ONESHOT_DISABLE, CONT_DISABLE,
                     SYNC_DISABLE, SYNC_SRC, OVRFLOW_DISABLE, SIXTEEN_BIT,
                     CHINT_END, CHINT_DISABLE);

    DmaRegs.CH5.CONTROL.bit.HALT = 1;
    DmaRegs.CH4.CONTROL.bit.HALT = 1;
}

/* 用户调用：启动一次 SPIA DMA 收发 */
bool SPIA_TransferDMA(uint16_t *tx, uint16_t len)
{
    if (DmaRegs.CH4.CONTROL.bit.RUNSTS)
    {
        return false; // 上一条还没完
    }

    if (len > SPIA_DMA_LEN)
        return false;

    /* 拷贝用户数据到 DMA 缓冲区 */
    for (uint16_t i = 0; i < len; i++)
    {
        sdata[i] = tx[i] << 8;
    }
    /* 重新配置计数，防止长度变化 */
    DMACH4TransferConfig((len / FIFO_LVL) - 1, 0, 1);
    DMACH5TransferConfig((len / FIFO_LVL) - 1, 1, 0);

    /* 先开 RX，再开 TX，避免丢首字节 */
    StartDMACH4();
    StartDMACH5();

    return true;
}

// W5500 GPIO初始化
void W5500_GPIO_Init(void)
{
#ifdef CPU1
    EALLOW;

    // INT_L (中断输入)
    GpioCtrlRegs.GPCMUX1.bit.W5500_INT_GPIO = 0;  // GPIO功能
    GpioCtrlRegs.GPCDIR.bit.W5500_INT_GPIO = 0;   // 输入
    GpioCtrlRegs.GPCPUD.bit.W5500_INT_GPIO = 0;   // 使能上拉
    GpioCtrlRegs.GPCQSEL1.bit.W5500_INT_GPIO = 0; // 同步输入

    // RST_L (复位输出)
    GpioCtrlRegs.GPCMUX1.bit.W5500_RST_GPIO = 0; // GPIO功能
    GpioCtrlRegs.GPCDIR.bit.W5500_RST_GPIO = 1;  // 输出
    GpioCtrlRegs.GPCPUD.bit.W5500_RST_GPIO = 0;  // 使能上拉
    GpioDataRegs.GPCSET.bit.W5500_RST_GPIO = 1;  // 初始化为高电平

    EDIS;
#endif // CPU1
}

// W5500复位函数
void W5500_Reset(void)
{
    // 拉低复位信号
    GpioDataRegs.GPCCLEAR.bit.GPIO74 = 1;
    HDL_CPU_Time_DelayUs(10000); // 延时10ms

    // 拉高复位信号
    GpioDataRegs.GPCSET.bit.GPIO74 = 1;
    HDL_CPU_Time_DelayUs(10000); // 延时10ms，等待W5500启动
}

void HDL_SPI_SELECT()
{
    GpioDataRegs.GPBCLEAR.bit.SPIA_SCS_GPIO = 1;
}

void HDL_SPI_DESELECT()
{
    GpioDataRegs.GPBSET.bit.SPIA_SCS_GPIO = 1;
}

/**
 * @brief 将W5500驱动所需的外设控制权分配给CPU2
 *
 * 该函数将W5500以太网控制器驱动所需的外设控制权从CPU1转移到CPU2，
 * 包括SPI_A外设和相关的GPIO引脚控制权。转移完成后，CPU2将负责
 * W5500的所有通信和控制操作。
 *
 * 转移的外设包括：
 * - SPI_A外设：用于与W5500进行SPI通信
 * - GPIO73：W5500控制引脚1
 * - GPIO74：W5500控制引脚2
 * - GPIO61：W5500控制引脚3（通常为片选CS引脚）
 *
 * @note 调用此函数后，CPU1将无法访问上述外设
 * @note 此函数应在系统初始化阶段调用，且只调用一次
 */
void Assign_W5500_Control_To_CPU2()
{
#ifdef CPU1
    SPI_CLK_Init();
    EALLOW;
    DevCfgRegs.CPUSEL6.bit.SPI_A = 1; // 将外设控制权给CPU2
    EDIS;
    // 普通IO
    GPIO_SetupPinMux(73, GPIO_MUX_CPU2, 0);
    GPIO_SetupPinMux(74, GPIO_MUX_CPU2, 0);
    // SPI所有权已经转移到CPU2不需要单独配置SPIA的IO
    EALLOW;
    //    GpioCtrlRegs.GPBCSEL4.bit.GPIO58 = GPIO_MUX_CPU2;
    //    GpioCtrlRegs.GPBCSEL4.bit.GPIO59 = GPIO_MUX_CPU2;
    //    GpioCtrlRegs.GPBCSEL4.bit.GPIO60 = GPIO_MUX_CPU2;
    // 软件片选比较好调试
    GpioCtrlRegs.GPBCSEL4.bit.GPIO61 = GPIO_MUX_CPU2;

    // 道爷我成了，这个GSx RAM的权限也要自己配置
    MemCfgRegs.GSxMSEL.bit.MSEL_GS8 = 1;
    MemCfgRegs.GSxMSEL.bit.MSEL_GS9 = 1;
    EDIS;
#endif // CPU1
}

void W5500_IO_Init(void)
{
    // 初始化W5500 GPIO
    W5500_GPIO_Init();

    // 初始化SPI GPIO
    SPI_GPIO_Init();
}

void W5500_Init(void)
{
    W5500_IO_Init();
    EALLOW;
    // 使能DMA时钟
    CpuSysRegs.PCLKCR0.bit.DMA = 1;
    // 使能SPI-A时钟
    CpuSysRegs.PCLKCR8.bit.SPI_A = 1;
    ClkCfgRegs.LOSPCP.bit.LSPCLKDIV = 0;
    EDIS;

    // 复位W5500
    W5500_Reset();
    SPIA_Peripheral_Reset();
    /* 0. 统一初始化 DMA 控制器（仅一次） */
    DMAInitialize();
    SPI_DMA_Init();
    SPI_Init();
}

void W5500_Init_CPU1(void)
{
    W5500_IO_Init();
    SPIA_Peripheral_Reset();
    Assign_W5500_Control_To_CPU2();
}

void W5500_Init_CPU2(void)
{
    // 复位W5500
    W5500_Reset();

    EALLOW;
    // 使能DMA时钟
    CpuSysRegs.PCLKCR0.bit.DMA = 1;
    // 使能SPI-A时钟
    CpuSysRegs.PCLKCR8.bit.SPI_A = 1;
    ClkCfgRegs.LOSPCP.bit.LSPCLKDIV = 0;
    EDIS;

    DMAInitialize();
    SPI_DMA_Init();
    SPI_Init();
}

bool HDL_SPI_WriteRead(byte_t *pTxData, byte_t *pRxData, uint16_t size)
{
    SPIA_TransferDMA((uint16_t *)pTxData, size);

    while (DmaRegs.CH4.CONTROL.bit.RUNSTS)
    {
    }
    memcpy(pRxData, rdata, size);
    return true;
}

bool HDL_SPI_WriteRead_Blocked(byte_t *pTxData, byte_t *pRxData, uint16_t size)
{
    for (uint32_t i = 0; i < size; i++)
    {
        // 等待发送FIFO不满
        while (SpiaRegs.SPIFFTX.bit.TXFFST != 0)
            ;
        if (pTxData == NULL)
        {
            SpiaRegs.SPITXBUF = 0xFFU << 8;
        }
        else
        {
            SpiaRegs.SPITXBUF = pTxData[i] << 8;
        }
        // 等待接收FIFO有数据
        while (SpiaRegs.SPIFFRX.bit.RXFFST == 0)
            ;
        if (pRxData != NULL)
        {
            pRxData[i] = SpiaRegs.SPIRXBUF;
        }
        else
        {
            uint16_t tmp = SpiaRegs.SPIRXBUF;
            UNUSED(tmp);
        }
    }
    return true;
}
