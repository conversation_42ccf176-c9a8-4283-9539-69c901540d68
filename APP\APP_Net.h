/**
 * @file APP_Net.h
 * <AUTHOR> (liu<PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2025-08-18
 * @last modified 2025-08-18
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#ifndef APP_NET_H
#define APP_NET_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>
#include "APP_Net_Regs.h"
#include "GSxRAM_Sample_Point_Queue.h"

    bool APP_Net_Reg_IsDifferent(uint16_t regAddr, uint32_t reg_value);
    bool APP_Net_WriteReg(uint16_t regAddr, uint32_t reg_value);
    bool APP_Net_BurstWriteReg(const SysRegs_t *src_regs);
    uint32_t APP_Net_ReadReg_From_CPU1(uint16_t regAddr);
    uint32_t APP_Net_ReadReg_From_CPU2(uint16_t regAddr);
    bool APP_Net_BurstReadReg_From_CPU1(SysRegs_t *dst_regs);
    bool APP_Net_BurstReadReg_From_CPU2(SysRegs_t *dst_regs);
    bool APP_Net_IsRegsOccupied();
    void APP_Net_Init();
    void APP_Net_Poll(void);
    bool APP_Net_Is_Param_Load_Success();
    bool APP_IsCPU1_Runing();
    bool APP_IsCPU2_Runing();
    void APP_Net_Wait_CPU2_Init(uint32_t timeout_us);
#include "APP_Net_Regs_Sync.h"
#ifdef __cplusplus
}
#endif
#endif //! APP_NET_H
