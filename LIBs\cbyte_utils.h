/**
 * @file cbyte_utils.h
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-28
 * @last modified 2025-08-28
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#ifndef CBYTE_UTILS_H
#define CBYTE_UTILS_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>

/**
 * 通用位操作和数据拼接宏定义
 * 兼容C99标准，支持不同CPU架构
 * 不依赖任何编译器扩展
 */

/* ========== 数据拼接宏 ========== */
#define PACK_U32_FROM_U8(byte3, byte2, byte1, byte0) \
    ((uint32_t)(((uint32_t)(byte3) << 24) | ((uint32_t)(byte2) << 16) | ((uint32_t)(byte1) << 8) | (uint32_t)(byte0)))

/* 将两个uint8_t拼接为uint16_t (高字节在前) */
#define PACK_U16_FROM_U8(high_u8, low_u8) \
    ((uint16_t)(((uint16_t)(high_u8) << 8) | (uint16_t)(low_u8)))

/* 将两个uint16_t拼接为uint32_t (高字在前) */
#define PACK_U32_FROM_U16(high_u16, low_u16) \
    ((uint32_t)(((uint32_t)(high_u16) << 16) | (uint32_t)(low_u16)))

/* 将两个uint32_t拼接为uint64_t (高字在前) */
#define PACK_U64_FROM_U32(high_u32, low_u32) \
    ((uint64_t)(((uint64_t)(high_u32) << 32) | (uint64_t)(low_u32)))

/* 将四个uint8_t拼接为uint32_t (按字节顺序: b3,b2,b1,b0) */
#define PACK_U32_FROM_4U8(b3, b2, b1, b0) \
    ((uint32_t)(((uint32_t)(b3) << 24) |  \
                ((uint32_t)(b2) << 16) |  \
                ((uint32_t)(b1) << 8) |   \
                (uint32_t)(b0)))

/* 将八个uint8_t拼接为uint64_t */
#define PACK_U64_FROM_8U8(b7, b6, b5, b4, b3, b2, b1, b0) \
    ((uint64_t)(((uint64_t)(b7) << 56) |                  \
                ((uint64_t)(b6) << 48) |                  \
                ((uint64_t)(b5) << 40) |                  \
                ((uint64_t)(b4) << 32) |                  \
                ((uint64_t)(b3) << 24) |                  \
                ((uint64_t)(b2) << 16) |                  \
                ((uint64_t)(b1) << 8) |                   \
                (uint64_t)(b0)))

/* 将四个uint16_t拼接为uint64_t */
#define PACK_U64_FROM_4U16(w3, w2, w1, w0) \
    ((uint64_t)(((uint64_t)(w3) << 48) |   \
                ((uint64_t)(w2) << 32) |   \
                ((uint64_t)(w1) << 16) |   \
                (uint64_t)(w0)))

/* ========== 数据拆分宏 ========== */

/* 从uint16_t拆分出高字节 */
#define UNPACK_U16_HIGH_U8(val) ((uint8_t)((val) >> 8))

/* 从uint16_t拆分出低字节 */
#define UNPACK_U16_LOW_U8(val) ((uint8_t)((val) & 0xFF))

/* 从uint32_t拆分出高16位 */
#define UNPACK_U32_HIGH_U16(val) ((uint16_t)((val) >> 16))

/* 从uint32_t拆分出低16位 */
#define UNPACK_U32_LOW_U16(val) ((uint16_t)((val) & 0xFFFF))

/* 从uint64_t拆分出高32位 */
#define UNPACK_U64_HIGH_U32(val) ((uint32_t)((val) >> 32))

/* 从uint64_t拆分出低32位 */
#define UNPACK_U64_LOW_U32(val) ((uint32_t)((val) & 0xFFFFFFFF))

/* 从uint32_t中拆分出指定位置的字节 (0为最低字节) */
#define UNPACK_U32_GET_U8(val, pos) ((uint8_t)(((val) >> ((pos) * 8)) & 0xFF))

/* 从uint64_t中拆分出指定位置的字节 (0为最低字节) */
#define UNPACK_U64_GET_U8(val, pos) ((uint8_t)(((val) >> ((pos) * 8)) & 0xFF))

/* 从uint64_t中拆分出指定位置的uint16_t (0为最低字) */
#define UNPACK_U64_GET_U16(val, pos) ((uint16_t)(((val) >> ((pos) * 16)) & 0xFFFF))

/* ========== 位操作宏 ========== */

/* 设置第n位为1 */
#define SET_BIT(val, n) ((val) | (1U << (n)))

/* 清除第n位为0 */
#define CLEAR_BIT(val, n) ((val) & ~(1U << (n)))

/* 切换第n位 */
#define TOGGLE_BIT(val, n) ((val) ^ (1U << (n)))

/* 检查第n位是否为1 */
#define TEST_BIT(val, n) (((val) & (1U << (n))) != 0)

/* 获取第n位的值 (返回0或1) */
#define GET_BIT(val, n) (((val) >> (n)) & 1U)

/* 设置多个位的掩码 */
#define SET_BITS(val, mask) ((val) | (mask))

/* 清除多个位的掩码 */
#define CLEAR_BITS(val, mask) ((val) & ~(mask))

/* 切换多个位的掩码 */
#define TOGGLE_BITS(val, mask) ((val) ^ (mask))

/* 测试掩码中的任何位是否被设置 */
#define TEST_ANY_BITS(val, mask) (((val) & (mask)) != 0)

/* 测试掩码中的所有位是否都被设置 */
#define TEST_ALL_BITS(val, mask) (((val) & (mask)) == (mask))

/* ========== 位域提取和设置宏 ========== */

/* 创建位域掩码 (width位宽度，从第0位开始) */
#define BITMASK(width) ((1U << (width)) - 1U)

/* 创建位域掩码 (width位宽度，从第pos位开始) */
#define BITMASK_AT(width, pos) (BITMASK(width) << (pos))

/* 提取位域 (从第pos位开始，width位宽度) */
#define EXTRACT_BITS(val, pos, width) \
    (((val) >> (pos)) & BITMASK(width))

/* 设置位域 (将newval写入从第pos位开始的width位) */
#define SET_BITFIELD(val, pos, width, newval) \
    (((val) & ~BITMASK_AT(width, pos)) |      \
     (((newval) & BITMASK(width)) << (pos)))

/* ========== 字节序转换宏 ========== */

/* 16位字节序交换 */
#define BSWAP_U16(val) \
    ((uint16_t)(((uint16_t)(val) << 8) | ((uint16_t)(val) >> 8)))

/* 32位字节序交换 */
#define BSWAP_U32(val)                                 \
    ((uint32_t)(((uint32_t)(val) << 24) |              \
                (((uint32_t)(val) & 0xFF00U) << 8) |   \
                (((uint32_t)(val) & 0xFF0000U) >> 8) | \
                ((uint32_t)(val) >> 24)))

/* 64位字节序交换 */
#define BSWAP_U64(val)                                         \
    ((uint64_t)(((uint64_t)BSWAP_U32((uint32_t)(val)) << 32) | \
                BSWAP_U32((uint32_t)((val) >> 32))))

/* ========== 实用工具宏 ========== */

/* 计算数组元素个数 */
#define ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))

/* 获取结构体成员的偏移量 */
#define OFFSET_OF(type, member) ((size_t)&(((type *)0)->member))

/* 对齐到指定边界 (boundary必须是2的幂) */
#define ALIGN_UP(val, boundary) \
    (((val) + (boundary) - 1) & ~((boundary) - 1))

#define ALIGN_DOWN(val, boundary) \
    ((val) & ~((boundary) - 1))

/* 最小值和最大值 */
#define MIN(a, b) (((a) < (b)) ? (a) : (b))
#define MAX(a, b) (((a) > (b)) ? (a) : (b))

/* 限制值在指定范围内 */
#define CLAMP(val, min_val, max_val) \
    MAX((min_val), MIN((val), (max_val)))

/* 判断是否是2的幂 */
#define IS_POWER_OF_2(val) (((val) != 0) && (((val) & ((val) - 1)) == 0))

/* 向上舍入到最近的2的幂 */
#define ROUND_UP_POW2_U32(val) \
    do                         \
    {                          \
        (val)--;               \
        (val) |= (val) >> 1;   \
        (val) |= (val) >> 2;   \
        (val) |= (val) >> 4;   \
        (val) |= (val) >> 8;   \
        (val) |= (val) >> 16;  \
        (val)++;               \
    } while (0)
#ifdef __cplusplus
}
#endif
#endif //! CBYTE_UTILS_H
