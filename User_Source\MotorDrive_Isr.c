//=======================================================
//    Project：         MMC_PET_CPU1
//    File:                 MMC_PET_Isr.c
//    Created on:     2021
//    By：                GCY
//    Describe：       中断服务函数
//=======================================================
//====================== Include ========================//
#include "MotorDrive_Isr.h"
#include "GSxRAM_Sample_Point_Queue.h"
#include "APP_Net.h"
#include "BFL_DebugPin.h"

//====================== Defines ========================//

//====================Variable Definition=====================//
ABC_DQ0_POS_F abc_dq0_pos1;
ABC_DQ0_POS_F abc_dq0_pos2;
SPLL_3ph_SRF_F spll1;
MTPA_F MTPA_Idq1;
iPARK_F ipark1;

// SCI

Uint16 AdcaResult[6];
Uint16 AdcbResult[6];
Uint16 AdccResult[6];
Uint16 AdcdResult[6];

volatile struct CAN_TXSTATUS1_STRUC mCANStatus1Tran = {0, 0, 0, 0, 0, 0, 0, 0};
volatile struct CAN_TXSTATUS2_STRUC mCANStatus2Tran = {0, 0, 0, 0, 0, 0, 0, 0};
volatile struct CAN_DRV_STATUS_STRUC mCANDRVStatusTran = {0, 0, 0, 0, 0, 0, 0, 0};

volatile struct CAN_TXSTATUS1_STRUC mFaultCANStatus1Tran = {0, 0, 0, 0, 0, 0, 0, 0};
volatile struct CAN_TXSTATUS2_STRUC mFaultCANStatus2Tran = {0, 0, 0, 0, 0, 0, 0, 0};
volatile struct CAN_DRV_STATUS_STRUC mFaultCANDRVStatusTran = {0, 0, 0, 0, 0, 0, 0, 0};

MOTOR_CONTROL_VARIABLES MCV;

Uint16 RotTX_ZeroEN_FLG = 0; // 0:当前未启动旋变校准,1:当前已启动旋变校准
Uint16 SYS_OpertionMode = 2; // 模式---1----电流环模式，2---速度环模式, 0---其他
Uint16 SYS_RUN_RSEN = 0;     // 上位机使能位

Uint16 FaultFlag = 0;     // 错误标志位，0----无错误，1---有错误
Uint16 Enable = 0;        // 使能标志位， 0----不使能，1----使能
Uint16 DCtoDCFLTFlag = 0; // DC-DC模块故障标志，0-----无故障，其它值----有故障
Uint32 CANTimeOutCNT = 0;
Uint32 CANBTimeOutCNT = 0;
Uint16 PWMEnFlg = 2; // PWM 使能标志位,0----不使能，1----使能,2-----其它。
Uint16 ROTTX_ZERO_PulseCNT = 0;

Uint16 FaultDataLogEnable = 1; // 0---不使能，1-----使能

Uint16 Fault_temp1_Flag = 0, Fault_temp2_Flag = 0, Fault_temp3_Flag = 0, Fault_temp4_Flag = 0;
Uint16 CPLDFaultFlag = 0, OVDCFaultFlag = 0, UVDCFaultFlag = 0, OCIbusFaultFlag = 0, OCIarmPFaultFlag = 0, OCIarmNFaultFlag = 0, OCIacFaultFlag = 0; // 故障标志位
Uint16 OVACFaultFlag_Vab = 0, OVACFaultFlag_Vac = 0, OVACFaultFlag_Vbc = 0, OCIarmPFaultFlag_aP = 0, OCIarmPFaultFlag_aN = 0, OCIarmPFaultFlag_bP = 0, OCIarmPFaultFlag_bN = 0;
Uint16 OCFaultFlag_Ia = 0, OCFaultFlag_Ib = 0, OCFaultFlag_Ic = 0;
Uint16 OTFaultFlag_DRV1 = 0, OTFaultFlag_DRV2 = 0, OTFaultFlag_DRV3 = 0;
Uint16 OTFaultFlag_PT1 = 0, OTFaultFlag_PT2 = 0, OTFaultFlag_PT3 = 0, OTFaultFlag_PT4 = 0, OTFaultFlag_PT5 = 0;

Uint16 Comm_Err_Counter = 0;
//=======================================================
// 函数名称:  Adca1_Isr()
// 功能:   ADCA中断1
// 输入:
// 返回:
// 备注:
//=======================================================
Uint16 my_test_dac = 1;

// 电机旋变初始角度，二期四台电机角度不一致，需要烧程序时进行改动
// float test_zero = 243.0f;//01电机角度
float test_zero = 294.0f; // 02电机角度
// float test_zero = 263.0f;//03电机角度
// float test_zero = 271.0f;//04电机角度

float compensation_coef = 2.0f;

interrupt void Adca1_Isr(void)
{
    GPIO16(0);
    //    BFL_DebugPin_Reset(DEBUG_PIN_1);
    // ADCA: 0:A1---3.3V    1:A3----NTC1   2:A5---NTC2  3:C14----PT1    4:A1---C15----PT4    5:----
    // ADCB: 0:B1---Ibus    1:B1---Ibus    2:B2----5V   3:B2----5V      4:B2----5V           5:----
    // ADCC: 0:C5---Ia      1:C3---Ib      2:C4---Ic
    // ADCC: 3:C5---Ia      4:C3---Ib      5:C4---Ic
    // ADCC: 6:C5---Ia      7:C3---Ib      8:C4---Ic
    // ADCD: 0:D2---Vbus1   1:D0---Vbus2   2:D4----PT2  3: D5---PT3  4:D3---PT5              5:----

    // 电机位置MotorPosition需要对DMA数据做平均后使用
    CalSPIFilteredData(&MCV.MotorPosition, &MCV.ResovlerFault); // 得到旋变的角度和故障位

    CalRadandSpeed(); // 根据旋变解码器数据并计算角度和转速

    //-----------------Adca采样配置-----------------
    AdcaResult[0] = AdcaResultRegs.ADCRESULT0; // 3.3V
    AdcaResult[1] = AdcaResultRegs.ADCRESULT1; // NTC1
    AdcaResult[2] = AdcaResultRegs.ADCRESULT2; // NTC2
    AdcaResult[3] = AdcaResultRegs.ADCRESULT3; // PT1
    AdcaResult[4] = AdcaResultRegs.ADCRESULT4; // PT4

    //-------------Adcb采样配置-----------------
    AdcbResult[0] = AdcbResultRegs.ADCRESULT0; // Ibus
    AdcbResult[1] = AdcbResultRegs.ADCRESULT1; // Ibus
    AdcbResult[2] = AdcbResultRegs.ADCRESULT2; // 5V
    AdcbResult[3] = AdcbResultRegs.ADCRESULT3; // 5V
    AdcbResult[4] = AdcbResultRegs.ADCRESULT4; // 5V

    //-----------------Adcc采样配置-----------------
    // 通过DMA采样
    // Ia,Ib,Ic需要对DMA数据做平均后使用    通过DMA采样
    CalFilteredData(&AdccResult[0], &AdccResult[1], &AdccResult[2]); //
    // AdccResult[0] = (AdccResultRegs.ADCRESULT0 + AdccResultRegs.ADCRESULT3 + AdccResultRegs.ADCRESULT6 + AdccResultRegs.ADCRESULT9)>>2;
    // AdccResult[1] = (AdccResultRegs.ADCRESULT1 + AdccResultRegs.ADCRESULT4 + AdccResultRegs.ADCRESULT7 + AdccResultRegs.ADCRESULT10)>>2;
    // AdccResult[2] = (AdccResultRegs.ADCRESULT2 + AdccResultRegs.ADCRESULT5 + AdccResultRegs.ADCRESULT8 + AdccResultRegs.ADCRESULT11)>>2;

    switch (my_test_dac)
    {
    case 3:
        CurrentDACOut(AdccResult[0]);
        // Pre_CH1_ON=1;
        break;
    case 4:
        CurrentDACOut(AdccResult[1]);
        // Pre_CH1_OFF=1;
        break;
    case 5:
        CurrentDACOut(AdccResult[2]);
        // Pre_CH2_ON=1;
        break;

    case 6:
        CurrentDACOut(AdccResult[2]);
        // Pre_CH2_OFF=1;
        break;

    default:
        break;
    }
    // AdccResult[2] = (AdccResultRegs.ADCRESULT0 + AdccResultRegs.ADCRESULT6)>>1 ;      // Ic

    //-----------------Adcd采样配置-----------------
    AdcdResult[0] = AdcdResultRegs.ADCRESULT0; // Vbus1
    AdcdResult[1] = AdcdResultRegs.ADCRESULT1; // Vbus2
    AdcdResult[2] = AdcdResultRegs.ADCRESULT2; // PT2
    AdcdResult[3] = AdcdResultRegs.ADCRESULT3; // PT3
    AdcdResult[4] = AdcdResultRegs.ADCRESULT4; // PT5
                                               // PT100系数
    // 1号电机使用0.06432，1.907系数，234号电机使用0.049347，1.02111系数

    MCV.mPT1 = MCV.mPT1 * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (float)(0.06432 * (float)AdcaResult[3] - 1.907); // PT1(低通滤波后)

    // mPT2   = mPT2  * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (float)(0.06432*(float)AdcdResult[2]-1.907);//PT2(低通滤波后)    对应硬件的34脚
    MCV.mPT2 = MCV.mPT2 * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (float)(0.049347 * (float)AdcdResult[2] - 1.02111); // PT2(低通滤波后)    对应硬件的34脚

    MCV.mPT3 = MCV.mPT3 * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (float)(0.06432 * (float)AdcdResult[3] - 1.907); // PT3(低通滤波后)

    // mPT4   = mPT4  * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (float)(0.06432*(float)AdcaResult[4]-1.907);//PT4(低通滤波后)   对应硬件的56脚
    MCV.mPT4 = MCV.mPT4 * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (float)(0.049347 * (float)AdcaResult[4] - 1.02111); // PT4(低通滤波后)   对应硬件的56脚

    MCV.mPT5 = MCV.mPT5 * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (float)(0.049347 * (float)AdcdResult[4] - 1.02111); // PT5(低通滤波后)   对应硬件的12脚
    // mPT5   = mPT5  * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (float)(0.06432*(float)AdcdResult[4]-1.907);//PT5(低通滤波后)   对应硬件的12脚

    MCV.Vdd_P_5V = MCV.Vdd_P_5V * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (0.003443 * (float)(AdcbResult[2]) - 0);   //+5V电压(低通滤波后)
    MCV.Vdd_P_3V3 = MCV.Vdd_P_3V3 * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (0.002198 * (float)(AdcaResult[0]) - 0); //+3.3V电压(低通滤波后)

    MCV.mNTC1 = MCV.mNTC1 * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (float)(AdcaResult[1]); // NTC1(低通滤波后)

    MCV.mNTC2 = MCV.mNTC2 * VOLTAGE_FILTER_COEF + VOLTAGE_INPUT_COEF * (float)(AdcaResult[2]); // NTC2(低通滤波后)

    MCV.mTempNTC1 = NTCp1 * MCV.mNTC1 * MCV.mNTC1 * MCV.mNTC1 + NTCp2 * MCV.mNTC1 * MCV.mNTC1 + NTCp3 * MCV.mNTC1 + NTCp4; // 与实际值可能会差三度
    MCV.mTempNTC2 = NTCp1 * MCV.mNTC2 * MCV.mNTC2 * MCV.mNTC2 + NTCp2 * MCV.mNTC2 * MCV.mNTC2 + NTCp3 * MCV.mNTC2 + NTCp4;

    MCV.Ia = MCV.Ia * CURRENT_FILTER_COEF + CURRENT_INPUT_COEF * (0.098675f * (float)(AdccResult[0]) - 1610.2f);  // 电流Ia
    MCV.Ib = MCV.Ib * CURRENT_FILTER_COEF + CURRENT_INPUT_COEF * (0.0969375f * (float)(AdccResult[1]) - 1583.5f); // 电流Ib
    MCV.Ic = MCV.Ic * CURRENT_FILTER_COEF + CURRENT_INPUT_COEF * (0.097975f * (float)(AdccResult[2]) - 1598.3f);  // 电流Ic

    // Vbus1
    if (AdcdResult[0] < 60) // 50V以下采用不同K值拟合，保证归零及50V以上电压正常
    {
        MCV.Vbus = MCV.Vbus * 0.997f + 0.003f * (1.666f * (float)(AdcdResult[0]) - 50.0f); // Vbus1   线性分段拟合
        MCV.Vbus_protect = MCV.Vbus_protect * 0.9f + 0.1f * (1.666f * (float)(AdcdResult[0]) - 50.0f);
    }
    else
    {
        MCV.Vbus = MCV.Vbus * 0.997f + 0.003f * (0.80679f * (float)(AdcdResult[0]) + 0.983f); // Vbus1   线性分段拟合
        MCV.Vbus_protect = MCV.Vbus_protect * 0.9f + 0.1f * (0.80679f * (float)(AdcdResult[0]) + 0.983f);
    }

    // debug, must delete at real run
    // Vbus = 600.0f;
    if (MCV.Vbus < 0.1)
        MCV.Vbus = 0.1;

    if (MCV.Vbus < 10)
    {
        MCV.Vbus_filter = 10.0f;
    }
    else
    {
        MCV.Vbus_filter = MCV.Vbus_filter * 0.999f + MCV.Vbus * 0.001f;
    }
    // Vbus_filter = 110.0f;

    MCV.Ibus = MCV.Ibus * CURRENT_FILTER_COEF + CURRENT_INPUT_COEF * (0.79061f * (float)(AdcbResult[0]) - 1604.73337f); // 电流Ibus

    FaultProcessing(); // 故障保护

    StatusDataLog(); // 保存状态数据

    MCTRL_SM_Control(); // 状态机

    AdcaRegs.ADCINTFLGCLR.bit.ADCINT1 = 1; // clear INT1 flag
    // Check if overflow has occurred
    if (1 == AdcaRegs.ADCINTOVF.bit.ADCINT1)
    {
        AdcaRegs.ADCINTOVFCLR.bit.ADCINT1 = 1; // clear INT1 overflow flag
        AdcaRegs.ADCINTFLGCLR.bit.ADCINT1 = 1; // clear INT1 flag
    }
    // Acknowledge this interrupt to receive more interrupts from group 1
    PieCtrlRegs.PIEACK.all = PIEACK_GROUP1;
    //    static uint16_t cnt = 0;
    //    uint16_t freeSize = GSxRam_Sampling_Point_Queue_FreeSize_1_2();
    //    if (freeSize > 0)
    //    {
    //        volatile ADCSamplePoint_t *pPoint = GSxRam_Queue_Sampling_Point_1_2_GetWriteNodePointer();
    //        pPoint->type = 0xA2;
    //        for (int i = 0; i < ADC_SAMPLE_CH_NUM; i++)
    //        {
    //            pPoint->ch[i] = cnt++ & 0xFF;
    //        }
    //        GSxRam_Queue_Sampling_Point_1_2_EnqueueWriteNodePointer();
    //    }

    uint16_t freeSize = GSxRam_Sampling_Point_Queue_FreeSize_1_2();
    if (freeSize > 0)
    {
        volatile ADCSamplePoint_t *pPoint = GSxRam_Queue_Sampling_Point_1_2_GetWriteNodePointer();
        pPoint->type = 0xA2;
        pPoint->ch[0] = MCV.Ia;
        pPoint->ch[1] = MCV.Ib;
        pPoint->ch[2] = MCV.Ic;
        pPoint->ch[3] = MCV.mEtheta;
        pPoint->ch[4] = MCV.mEthetaZero;
        pPoint->ch[5] = MCV.mEtheta1;
        pPoint->ch[6] = MCV.mDuty;
        pPoint->ch[7] = MCV.mSpeed;
        pPoint->ch[8] = MCV.Ibus;
        pPoint->ch[9] = MCV.mPT1;
        GSxRam_Queue_Sampling_Point_1_2_EnqueueWriteNodePointer();
    }
    //    BFL_DebugPin_Set(DEBUG_PIN_1);
    GPIO16(1); // 28.24us
}

//=======================================================
// 函数名称: StatusData()
// 功能:    保存状态数据
// 输入:
// 返回:
// 备注:
//=======================================================
void StatusDataLog(void) // 保存状态数据
{
    // Status1//
    int16 temp1 = 0;
    Uint16 temp2 = 0;
    //    temp2=(Uint16)(mDuty*100);//占空比
    //   temp2=(Uint16)(ResovlerFault);//旋变故障位
    //    mCANStatus1Tran.Duty=(temp2)&0xff;

    mCANStatus1Tran.OCFLT.bit.OC_PhaseC_Hard = ~Ic_Err_IN;
    mCANStatus1Tran.OCFLT.bit.OC_PhaseB_Hard = ~Ib_Err_IN;
    mCANStatus1Tran.OCFLT.bit.OC_PhaseA_Hard = ~Ia_Err_IN;
    mCANStatus1Tran.OCFLT.bit.OC_IBus_Hard = ~Ibus_Err_IN;

    mCANStatus1Tran.OCFLT.bit.OC_PhaseC_Soft = OCFaultFlag_Ic;
    mCANStatus1Tran.OCFLT.bit.OC_PhaseB_Soft = OCFaultFlag_Ib;
    mCANStatus1Tran.OCFLT.bit.OC_PhaseA_Soft = OCFaultFlag_Ia;
    mCANStatus1Tran.OCFLT.bit.OC_IBus_Soft = OCIbusFaultFlag;

    temp1 = (int16)(-MCV.mSpeed); //  转速，顺时针旋转取负号
    mCANStatus1Tran.SpeedH = (temp1 >> 8) & 0xff;
    mCANStatus1Tran.SpeedL = temp1 & 0xff;

    if (OCIbusFaultFlag || OCFaultFlag_Ia || OCFaultFlag_Ib || OCFaultFlag_Ic)
        mCANStatus1Tran.SYSSTATUS.bit.INV_OC = 1; // 软件过流汇总
    else
        mCANStatus1Tran.SYSSTATUS.bit.INV_OC = 0; // 软件过流汇总
    if (OVDCFaultFlag)
        mCANStatus1Tran.SYSSTATUS.bit.Bus_OV = 1; // 软件过压保护
    else
        mCANStatus1Tran.SYSSTATUS.bit.Bus_OV = 0; // 软件过压保护
    if (UVDCFaultFlag)
        mCANStatus1Tran.SYSSTATUS.bit.Bus_UV = 1; // 软件欠压保护
    else
        mCANStatus1Tran.SYSSTATUS.bit.Bus_UV = 0; // 软件欠压保护
    // to do
    mCANStatus1Tran.SYSSTATUS.bit.IGBT_FLT = (mMsgDATA_D.errcode.bit.Fault_all) || (mMsgDATA_C.errcode.bit.Fault_all) || (mMsgDATA_B.errcode.bit.Fault_all); // 软件驱动器保护
    mCANStatus1Tran.SYSSTATUS.bit.INV_OT = OTFaultFlag_DRV1 || OTFaultFlag_DRV2 || OTFaultFlag_DRV3;                                                         // 软件逆变器过温保护
    // mCANStatus1Tran.SYSSTATUS.bit.MOT_OT=OTFaultFlag_PT1||OTFaultFlag_PT2||OTFaultFlag_PT3||OTFaultFlag_PT4||OTFaultFlag_PT5;//软件电机过热

    //    mCANStatus1Tran.SYSSTATUS.bit.IGBT_FLT=0;
    //    mCANStatus1Tran.SYSSTATUS.bit.INV_OT=0;
    //    mCANStatus1Tran.SYSSTATUS.bit.MOT_OT=0;
    mCANStatus1Tran.SYSSTATUS.bit.Phase_FLT = 0;
    mCANStatus1Tran.SYSSTATUS.bit.SelfCheck_FLT = 0;

    temp2 = (Uint16)MCV.Vbus; // 母线电压
    mCANStatus1Tran.VbusH = (temp2 >> 8) & 0xff;
    mCANStatus1Tran.VbusL = temp2 & 0xff;
    //    mCANStatus1Tran.VbusH = mMCTRL_State;
    //    mCANStatus1Tran.VbusL = (Uint16)(Set_Speed_Ref*0.05f);

    // temp1=(int16)Ibus;//母线电流
    temp1 = (int16)(fabsf(ids)); // ID实际值
    mCANStatus1Tran.IbusH = (temp1 >> 8) & 0xff;
    mCANStatus1Tran.IbusL = temp1 & 0xff;
    //    mCANStatus1Tran.IbusH = (OCFaultFlag_Ic<<3)  | (OTFaultFlag_PT1<<2) | (OTFaultFlag_PT2 <<1) | OTFaultFlag_PT3;
    //    mCANStatus1Tran.IbusL = (CPLDFaultFlag<<7) | (OVDCFaultFlag <<6 ) | (OCIbusFaultFlag <<5) | (OTFaultFlag_DRV1<<4)
    //                    | (OTFaultFlag_DRV2<<3) | (OTFaultFlag_DRV3<<2) | (OCFaultFlag_Ia <<1) | (OCFaultFlag_Ib);

    // Status2//

    mCANStatus2Tran.MOTSTATUS.bit.OVSpeed = 0;
    mCANStatus2Tran.MOTSTATUS.bit.Sensor_FLT = 0;

    // temp2=(Uint16)Iarms;
    // temp2=(Uint16)mPT2;
    // temp2=(Uint16)(fabs(ipark1.d));
    temp2 = (Uint16)(fabsf(Iqref)); // Iq
    mCANStatus2Tran.IaH = (temp2 >> 8) & 0xff;
    mCANStatus2Tran.IaL = temp2 & 0xff;

    // temp2=(Uint16)Ibrms;
    // temp2=(Uint16)mPT4; //Iq 实际值
    temp2 = (Uint16)(fabsf(ipark1.q));
    mCANStatus2Tran.IbH = (temp2 >> 8) & 0xff;
    mCANStatus2Tran.IbL = temp2 & 0xff;

    // temp2=(Uint16)Icrms;
    temp2 = (Uint16)MCV.mPT5;
    // temp2=(Uint16)(fabs(Iqref));//Iq
    mCANStatus2Tran.IcH = (temp2 >> 8) & 0xff;
    mCANStatus2Tran.IcL = temp2 & 0xff;

    // 驱动信息反馈
    mCANDRVStatusTran.ERRORCODE1.all = mMsgDATA_D.errcode.all;
    mCANDRVStatusTran.Temp1 = mMsgDATA_D.Temperature;
    mCANDRVStatusTran.ERRORCODE2.all = mMsgDATA_C.errcode.all;
    mCANDRVStatusTran.Temp2 = mMsgDATA_C.Temperature;
    mCANDRVStatusTran.ERRORCODE3.all = mMsgDATA_B.errcode.all;
    mCANDRVStatusTran.Temp3 = mMsgDATA_B.Temperature;

    // temp2=(Uint16)(mEthetaAVG*10);//校准输出角度mMCTRL_State
    temp2 = (Uint16)(MCV.ResovlerFault); // 旋变故障位
    // temp2=(Uint16)(mMCTRL_State);//旋变故障位
    mCANDRVStatusTran.RSVD1 = (temp2 >> 8) & 0xff;
    mCANDRVStatusTran.RSVD2 = temp2 & 0xff;
}

//=======================================================
// 函数名称: FaultStatusData()
// 功能:    故障时刻状态数据保存，用于故障诊断
// 输入:
// 返回:
// 备注:
//=======================================================
void FaultStatusDataLog(void) // 故障时刻状态数据保存，用于故障诊断
{
    // Status1//
    int16 temp1 = 0;
    Uint16 temp2 = 0;
    temp2 = (Uint16)(MCV.mDuty * 100);
    temp2 = (Uint16)(MCV.ResovlerFault); // 旋变故障位
    // mFaultCANStatus1Tran.OCFLT.all=(temp2)&0xff;

    temp1 = (int16)(-MCV.mSpeed); // 顺时针旋转取负号
    mFaultCANStatus1Tran.SpeedH = (temp1 >> 8) & 0xff;
    mFaultCANStatus1Tran.SpeedL = temp1 & 0xff;

    if (OCIbusFaultFlag || OCFaultFlag_Ia || OCFaultFlag_Ib || OCFaultFlag_Ic)
        mFaultCANStatus1Tran.SYSSTATUS.bit.INV_OC = 1;
    else
        mFaultCANStatus1Tran.SYSSTATUS.bit.INV_OC = 0;
    if (OVDCFaultFlag)
        mFaultCANStatus1Tran.SYSSTATUS.bit.Bus_OV = 1;
    else
        mFaultCANStatus1Tran.SYSSTATUS.bit.Bus_OV = 0;

    if (UVDCFaultFlag)
        mFaultCANStatus1Tran.SYSSTATUS.bit.Bus_UV = 1;
    else
        mFaultCANStatus1Tran.SYSSTATUS.bit.Bus_UV = 0;
    // to do
    mFaultCANStatus1Tran.SYSSTATUS.bit.IGBT_FLT = (mMsgDATA_D.errcode.bit.Fault_all) || (mMsgDATA_C.errcode.bit.Fault_all) || (mMsgDATA_B.errcode.bit.Fault_all);
    mFaultCANStatus1Tran.SYSSTATUS.bit.INV_OT = OTFaultFlag_DRV1 || OTFaultFlag_DRV2 || OTFaultFlag_DRV3; //
    mFaultCANStatus1Tran.SYSSTATUS.bit.MOT_OT = OTFaultFlag_PT1 || OTFaultFlag_PT2 || OTFaultFlag_PT3 || OTFaultFlag_PT4 || OTFaultFlag_PT5;

    // mCANStatus1Tran.SYSSTATUS.bit.IGBT_FLT=0;
    // mCANStatus1Tran.SYSSTATUS.bit.INV_OT=0;
    // mCANStatus1Tran.SYSSTATUS.bit.MOT_OT=0;
    mFaultCANStatus1Tran.SYSSTATUS.bit.Phase_FLT = 0;
    mFaultCANStatus1Tran.SYSSTATUS.bit.SelfCheck_FLT = 0;

    temp2 = (Uint16)MCV.Vbus;
    mFaultCANStatus1Tran.VbusH = (temp2 >> 8) & 0xff;
    mFaultCANStatus1Tran.VbusL = temp2 & 0xff;
    //    mCANStatus1Tran.VbusH = mMCTRL_State;
    //    mCANStatus1Tran.VbusL = (Uint16)(Set_Speed_Ref*0.05f);

    // temp1=(int16)Ibus;
    temp1 = (int16)(fabsf(ids)); // Id
    mFaultCANStatus1Tran.IbusH = (temp1 >> 8) & 0xff;
    mFaultCANStatus1Tran.IbusL = temp1 & 0xff;
    //    mCANStatus1Tran.IbusH = (OCFaultFlag_Ic<<3)  | (OTFaultFlag_PT1<<2) | (OTFaultFlag_PT2 <<1) | OTFaultFlag_PT3;
    //    mCANStatus1Tran.IbusL = (CPLDFaultFlag<<7) | (OVDCFaultFlag <<6 ) | (OCIbusFaultFlag <<5) | (OTFaultFlag_DRV1<<4)
    //                    | (OTFaultFlag_DRV2<<3) | (OTFaultFlag_DRV3<<2) | (OCFaultFlag_Ia <<1) | (OCFaultFlag_Ib);

    // Status2//

    mFaultCANStatus2Tran.MOTSTATUS.bit.OVSpeed = 0;
    mFaultCANStatus2Tran.MOTSTATUS.bit.Sensor_FLT = 0;

    // temp2=(Uint16)Iarms;
    temp2 = (Uint16)(fabsf(ipark1.d)); // Ud
    mFaultCANStatus2Tran.IaH = (temp2 >> 8) & 0xff;
    mFaultCANStatus2Tran.IaL = temp2 & 0xff;

    // temp2=(Uint16)Ibrms;
    temp2 = (Uint16)(fabsf(ipark1.q)); // Uq
    mFaultCANStatus2Tran.IbH = (temp2 >> 8) & 0xff;
    mFaultCANStatus2Tran.IbL = temp2 & 0xff;

    // temp2=(Uint16)Icrms;
    temp2 = (Uint16)(fabsf(Iqref)); // Iq
    mFaultCANStatus2Tran.IcH = (temp2 >> 8) & 0xff;
    mFaultCANStatus2Tran.IcL = temp2 & 0xff;

    mFaultCANDRVStatusTran.ERRORCODE1.all = mMsgDATA_D.errcode.all;
    mFaultCANDRVStatusTran.Temp1 = mMsgDATA_D.Temperature;
    mFaultCANDRVStatusTran.ERRORCODE2.all = mMsgDATA_C.errcode.all;
    mFaultCANDRVStatusTran.Temp2 = mMsgDATA_C.Temperature;
    mFaultCANDRVStatusTran.ERRORCODE3.all = mMsgDATA_B.errcode.all;
    mFaultCANDRVStatusTran.Temp3 = mMsgDATA_B.Temperature;

    // temp2=(Uint16)(mEthetaAVG*10);//校准输出角度
    temp2 = (Uint16)(MCV.ResovlerFault); // 旋变故障位
    mCANDRVStatusTran.RSVD1 = (temp2 >> 8) & 0xff;
    mCANDRVStatusTran.RSVD2 = temp2 & 0xff;
}

//=======================================================
// 函数名称: CalRadandSpeed()
// 功能:    根据旋变解码器数据计算角度和转速
// 输入:
// 返回:
// 备注:
//=======================================================

void CalRadandSpeed(void)
{
    MCV.mAngle = MCV.MotorPosition; // DMA处理后的数据

    MCV.mEtheta1 = ((float)MCV.mAngle) * Angle_COEF; // 152.402344;//旋变输出电角度计算。   解码器没有输出角度，输出的是12位的模拟量转数字量的数据，需要转换成实际角度
    if (my_test_dac == 0)
        AngleDACOut(MCV.mEtheta1 * Rad_COEF);

    MCV.mEtheta = 360.0f - MCV.mEtheta1 + mVar_RAM.Motor_Parameters.Motor_Resolver_Zero ; // test_zero;   //现在的接线方式是从左往右CBA，
    //MCV.mEtheta = MCV.mEtheta1 + mVar_RAM.Motor_Parameters.Motor_Resolver_Zero;   //这句话是改便接线顺序的，改为ABC
    // MCV.mEtheta = MCV.mEtheta1;               // 实验室四对极电机，手动调整电机角度和旋变角度的差值为0，
    MCV.mEtheta = fmodf(MCV.mEtheta, 360.0f); // 限制角度范围是0-360
    MCV.mEthetaRad = MCV.mEtheta * Rad_COEF;  // 旋变输出电角度   弧度值
    if (my_test_dac == 1)
        AngleDACOut(MCV.mEthetaRad);

    // 转速计算//
    MCV.we = CalSpeed_Model_Simple(MCV.mEthetaRad);
    MCV.mSpeed = MCV.we * mVar_RAM.Motor_Parameters.Motor_RpstoRpm_COEF; // RpstoRpm_COEF   单位转换系数和极对数有关
    // 先进行角度位置的估算，然后再单位转换为每分钟转速，PRM=角速度（弧度每秒）*（60/2*p*pi）   p是极对数    pi是3.1415926
    // 4对极   RpstoRpm_COEF=60/2*4*3.1415926=2.387324         25.1327408
    // 6对极   RpstoRpm_COEF=60/2*6*3.1415926=1.591512
    // 8对极   RpstoRpm_COEF=60/2*8*3.1415926=1.193662

    MCV.mEthetaRad = mCalSpeed.Theta_Virtual + 0.00005f * compensation_coef * MCV.we; // 滤波器高速角度补偿

    if (my_test_dac == 2)
        AngleDACOut(MCV.mSpeed * 0.0167f);
}

//=======================================================
// 函数名称: SSIWrite(Uint16 a)
// 功能:    向读取旋变解码器发送时钟，读取数据
// 输入:
// 返回:
// 备注:
//=======================================================
void SSIWrite(Uint16 a)
{
    SpicRegs.SPITXBUF = a;
    SpicRegs.SPITXBUF = a;
}

//=======================================================
// 函数名称: SPIWrite(Uint16 a)
// 功能:    SPI 写地址和数据
// 输入:
// 返回:
// 备注:
//=======================================================
void SPIWrite(Uint16 a)
{
    //    SpicRegs.SPICCR.bit.SPISWRESET = 0;         // reset the SPI
    //    SpicRegs.SPICCR.bit.SPICHAR = (1-1);       // 16-bit character----1位数据
    //    SpicRegs.SPICCR.bit.CLKPOLARITY = 1;        // Clock polarity (0 == rising, 1 == falling)
    //    SpicRegs.SPICTL.bit.CLK_PHASE = 0;          // Clock phase (0 == normal, 1 == delayed)
    //    SpicRegs.SPICCR.bit.SPISWRESET = 1;         // Release the SPI from reset

    SpicRegs.SPITXBUF = a << 8;
}
//=======================================================
// 函数名称: FaultProcessing()
// 功能:    故障处理
// 输入:
// 返回:
// 备注:
//=======================================================
void FaultProcessing(void)
{
    // Uint16 temp1_Flag=0,temp2_Flag=0,temp3_Flag=0,temp4_Flag=0;
    if (CPLD_Fault_All_FLG == 0)
        CPLDFaultFlag = 1; // CPLD总故障反馈

    // FPGAFaultFlag=0;//to do

    if (MCV.Vbus_protect > mVar_RAM.Motor_Protect_Values.OV_INV_BUS ||
        MCV.Vbus_In > mVar_RAM.Motor_Protect_Values.OV_Input)
        OVDCFaultFlag = 1; // 过压 500
    else if (MCV.Vbus_protect < mVar_RAM.Motor_Protect_Values.UV_INV_BUS ||
             MCV.Vbus_In < mVar_RAM.Motor_Protect_Values.UV_Input)
        UVDCFaultFlag = 1; // 欠压---to do

    if (MCV.Ibus > mVar_RAM.Motor_Protect_Values.OC_IBUS_Positive ||
        MCV.Ibus < mVar_RAM.Motor_Protect_Values.OC_IBUS_Negative)
        OCIbusFaultFlag = 1; // 输入直流过流

    // PT100保护
    // 小U只有三路PT100，用于检测电机三相绕组温度，去除PT1和PT3
    // if(mPT1>190) OTFaultFlag_PT1=1;
    // else if(mPT1<160) OTFaultFlag_PT1=0;

    if (MCV.mPT2 > mVar_RAM.Motor_Protect_Values.OT_MOTOR)
        OTFaultFlag_PT2 = 1;
    else if (MCV.mPT2 < mVar_RAM.Motor_Protect_Values.OT_MOTOR_Recovery)
        OTFaultFlag_PT2 = 0;

    // if(mPT3>190) OTFaultFlag_PT3=1;
    // else if(mPT3<160) OTFaultFlag_PT3=0;

    if (MCV.mPT4 > mVar_RAM.Motor_Protect_Values.OT_MOTOR)
        OTFaultFlag_PT4 = 1;
    else if (MCV.mPT4 < mVar_RAM.Motor_Protect_Values.OT_MOTOR_Recovery)
        OTFaultFlag_PT4 = 0;

    if (MCV.mPT5 > mVar_RAM.Motor_Protect_Values.OT_MOTOR)
        OTFaultFlag_PT5 = 1;
    else if (MCV.mPT5 < mVar_RAM.Motor_Protect_Values.OT_MOTOR_Recovery)
        OTFaultFlag_PT5 = 0;

    // 驱动过温

    if (mMsgDATA_D.Temperature > mVar_RAM.Motor_Protect_Values.OT_IGBT)
        OTFaultFlag_DRV1 = 1;
    else if (mMsgDATA_D.Temperature < mVar_RAM.Motor_Protect_Values.OT_IGBT_Recovery)
        OTFaultFlag_DRV1 = 0;

    if (mMsgDATA_C.Temperature > mVar_RAM.Motor_Protect_Values.OT_IGBT)
        OTFaultFlag_DRV2 = 1;
    else if (mMsgDATA_C.Temperature < mVar_RAM.Motor_Protect_Values.OT_IGBT_Recovery)
        OTFaultFlag_DRV2 = 0;

    if (mMsgDATA_B.Temperature > mVar_RAM.Motor_Protect_Values.OT_IGBT)
        OTFaultFlag_DRV3 = 1;
    else if (mMsgDATA_B.Temperature < mVar_RAM.Motor_Protect_Values.OT_IGBT_Recovery)
        OTFaultFlag_DRV3 = 0;

    if ((CPLDFaultFlag || OVDCFaultFlag || OCIbusFaultFlag) == 1)
        Fault_temp1_Flag = 1; //
    if ((OTFaultFlag_DRV1 || OTFaultFlag_DRV2 || OTFaultFlag_DRV3) == 1)
        Fault_temp2_Flag = 1; // 逆变器过热标志位

    // if((OTFaultFlag_PT1||OTFaultFlag_PT2||OTFaultFlag_PT3||OTFaultFlag_PT4||OTFaultFlag_PT5)==1)   Fault_temp3_Flag=1;//电机过热标志位

    // 三相电流保护

    if ((MCV.Ia > mVar_RAM.Motor_Protect_Values.OC_Phase_Positive) || (MCV.Ia < mVar_RAM.Motor_Protect_Values.OC_Phase_Negative))
        OCFaultFlag_Ia = 1;
    if ((MCV.Ib > mVar_RAM.Motor_Protect_Values.OC_Phase_Positive) || (MCV.Ib < mVar_RAM.Motor_Protect_Values.OC_Phase_Negative))
        OCFaultFlag_Ib = 1;
    if ((MCV.Ic > mVar_RAM.Motor_Protect_Values.OC_Phase_Positive) || (MCV.Ic < mVar_RAM.Motor_Protect_Values.OC_Phase_Negative))
        OCFaultFlag_Ic = 1;
    if ((OCFaultFlag_Ia || OCFaultFlag_Ib || OCFaultFlag_Ic) == 1)
        Fault_temp4_Flag = 1; // 三相电流保护汇总

    // 保存数据
    mCANStatus2Tran.INVSTATUS.bit.NTC1_Err = OTFaultFlag_DRV1;
    mCANStatus2Tran.INVSTATUS.bit.NTC2_Err = OTFaultFlag_DRV2;
    mCANStatus2Tran.INVSTATUS.bit.NTC3_Err = OTFaultFlag_DRV3;
    // mCANStatus2Tran.INVSTATUS.bit.NTC4_Err=~(NTC1_Err_IN&NTC2_Err_IN);

    // 硬件保护
    mCANStatus2Tran.INVSTATUS.bit.Vbus_Err = ~Vbus_Err_IN;
    mCANStatus2Tran.INVSTATUS.bit.Ibus_Err = ~Ibus_Err_IN;

    mCANStatus2Tran.INVSTATUS.bit.Ia_Err = ~Ia_Err_IN;
    mCANStatus2Tran.INVSTATUS.bit.Ib_Err = ~Ib_Err_IN;
    mCANStatus2Tran.MOTSTATUS.bit.Ic_Err = ~Ic_Err_IN;

    mCANStatus2Tran.MOTSTATUS.bit.FLT1_Flag = Fault_temp1_Flag;
    mCANStatus2Tran.MOTSTATUS.bit.FLT2_Flag = Fault_temp2_Flag;
    mCANStatus2Tran.MOTSTATUS.bit.FLT3_Flag = Fault_temp3_Flag;
    mCANStatus2Tran.MOTSTATUS.bit.FLT4_Flag = Fault_temp4_Flag;
    mCANStatus2Tran.MOTSTATUS.bit.CPLD_FLT = CPLDFaultFlag;

    if ((Fault_temp1_Flag || Fault_temp2_Flag || Fault_temp3_Flag || Fault_temp4_Flag) == 1)
    {
        FaultFlag = 1;
        // DSP_FLT_FLG_SET=1;//Send fault signal to FPGA
        PWMSoftOutputOff(); // Disable the PWM signals

        // Fault_LED_ON=1;//故障LED指示，  1-有故障

        // Pre_CH2_OFF=1;//继电器K2断开
        Pre_CH1_OFF = 1; // 继电器K1断开

        if (FaultDataLogEnable == 1)
        {
            mFaultCANStatus2Tran.INVSTATUS.bit.NTC1_Err = OTFaultFlag_DRV1;
            mFaultCANStatus2Tran.INVSTATUS.bit.NTC2_Err = OTFaultFlag_DRV2;
            mFaultCANStatus2Tran.INVSTATUS.bit.NTC3_Err = OTFaultFlag_DRV3;
            // mFaultCANStatus2Tran.INVSTATUS.bit.NTC4_Err=~(NTC1_Err_IN&NTC2_Err_IN);

            mFaultCANStatus2Tran.INVSTATUS.bit.Vbus_Err = ~Vbus_Err_IN;
            mFaultCANStatus2Tran.INVSTATUS.bit.Ibus_Err = ~Ibus_Err_IN;
            mFaultCANStatus2Tran.INVSTATUS.bit.Ia_Err = ~Ia_Err_IN;
            mFaultCANStatus2Tran.INVSTATUS.bit.Ib_Err = ~Ib_Err_IN;

            mFaultCANStatus2Tran.MOTSTATUS.bit.Ic_Err = ~Ic_Err_IN;

            mFaultCANStatus2Tran.MOTSTATUS.bit.FLT1_Flag = Fault_temp1_Flag;
            mFaultCANStatus2Tran.MOTSTATUS.bit.FLT2_Flag = Fault_temp2_Flag;
            mFaultCANStatus2Tran.MOTSTATUS.bit.FLT3_Flag = Fault_temp3_Flag;
            mFaultCANStatus2Tran.MOTSTATUS.bit.FLT4_Flag = Fault_temp4_Flag;
            mFaultCANStatus2Tran.MOTSTATUS.bit.CPLD_FLT = CPLDFaultFlag;

            FaultStatusDataLog();

            FaultDataLogEnable = 0; // 保持第一次发生故障时候的数据，直达收到故障清除指令。
        }
    }
}
//=======================================================
// 函数名称: FaultReset()
// 功能:    故障标志位清零
// 输入:
// 返回:
// 备注:
//=======================================================
void FaultReset(void)
{
    OVDCFaultFlag = 0;
    OCIbusFaultFlag = 0;
    CPLDFaultFlag = 0;
    OTFaultFlag_DRV1 = 0;
    OTFaultFlag_DRV2 = 0;
    OTFaultFlag_DRV3 = 0;

    Fault_temp1_Flag = 0;
    Fault_temp3_Flag = 0;
    Fault_temp4_Flag = 0;
    Fault_temp2_Flag = 0;

    //    OCIarmPFaultFlag_aP=0;
    //    OCIarmPFaultFlag_aN=0;
    //    OCIarmPFaultFlag_bP=0;
    //    OCIarmPFaultFlag_bN=0;
    OCFaultFlag_Ia = 0;
    OCFaultFlag_Ib = 0;
    OCFaultFlag_Ic = 0;

    FaultFlag = 0;
}
//=======================================================
// 函数名称: MCTRL_SM_Control()
// 功能:  主控状态机
// 输入:
// 返回:
// 备注:
//=======================================================
void MCTRL_SM_Control()
{
    if ((FaultFlag == 1) && (mMCTRL_State != MCTRL_SM_ID_FAULT_RET))
        mMCTRL_State = MCTRL_SM_ID_FAULT; // 有故障，则进入故障状态

    CANTimeOutCNT++;

    if (CANTimeOutCNT >= 50000) // 定时5s,未收到CAN 数据则驱动器停止运行， 该计数器值在 CAN通讯处理程序中清零（收到CAN数据）
    {
        if ((mMCTRL_State != MCTRL_SM_ID_FAULT_RET) && (mMCTRL_State != MCTRL_SM_ID_FAULT))
        {
            mMCTRL_State = MCTRL_SM_ID_MD_PWM_OFF; // CAN数据超时，电机关闭
        }
        //   	Set_Speed_Ref =0;
        CANTimeOutCNT = 50000;
    }

    CANBTimeOutCNT++; //

    if (CANBTimeOutCNT >= 50000) // 定时5s,未收到CANB 数据则驱动器停止运行， 该计数器值在 CAN通讯处理程序中清零（收到CAN数据）
    {

        DCtoDCFLTFlag = 1; //********DC-DC模块通讯故障，故障标志位置位
        CANBTimeOutCNT = 50000;
    }

    // DCtoDCFLTFlag=1;
    if (mMCTRL_State != MCTRL_SM_ID_MD_RUN) // 非运行状态，UD UQ清零
    {
        Motor_Control_Ud = 0;
        Motor_Control_Uq = 0;
    }

    switch (mMCTRL_State) // 状态机开始执行
    {
    case MCTRL_SM_ID_IDLE: // Idle state
        PWMSoftOutputOff();
        // FaultReset();
        // SYS_OpertionMode=0;//待机模式
        // CPLD_FLT_FLG_ENABLE=1;//Enable FPGA to lock the fault signals

        if (FaultFlag == 0x0) // 无故障
        {
            SYS_RUN_RSEN = 1; // 上位机使能位置位

            Pre_CH1_OFF = 1; // 继电器K1断开
            Pre_CH2_OFF = 1; // 继电器K2断开

            if (CAN_CMD2FLG == 0x01) // 收到0x02报文，进入驱动器运行
            {

                if (CAN_CMD2FLG == 1)
                    CAN_CMD2OKFLG = 1; // 该指令执行完，该字节被置1
                CAN_CMD2FLG = 0;       // 该指令执行完，该位被置0

                if (MCV.Set_Speed_Ref == 0)
                    mMCTRL_State = MCTRL_SM_ID_MD_PWM_OFF; // 转速设置为0则驱动器停止运行
                else
                    mMCTRL_State = MCTRL_SM_ID_Speed_Mode_SEL; /// 进入高低速运行选择状态
            }

            if (CAN_CMD5FLG == 0x01)
            {
                CAN_CMD5OKFLG = 1; // 该指令执行完，该字节被置1
                CAN_CMD5FLG = 0;       // 该指令执行完，该位被置0

                if (RotTX_ZeroEN_FLG == 1)
                {
                    RotTX_ZeroEN_FLG = 0;
                    mMCTRL_State = MCTRL_SM_ID_ROTTX_ZERO_EN_PRE;
                }
            }
        }
        break;

    case MCTRL_SM_ID_Speed_Mode_SEL: // 高低速模式选择
        if (Enable == 0)
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE;
        else
        {
            if (MCV.Set_Speed_Ref == 0)
                mMCTRL_State = MCTRL_SM_ID_MD_PWM_OFF; // 转速设置为0则驱动器停止运行
            else if ((fabsf(MCV.Set_Speed_Ref) < mVar_RAM.Motor_Parameters.Driver_Low_Speed_Ref) && (DCtoDCFLTFlag == 0) && (MCV.Vbus > mVar_RAM.Motor_Parameters.Driver_Low_Speed_VBus_Min))
                mMCTRL_State = MCTRL_SM_ID_LoSpeed_MODE; // 转速设定低于DRIVER_LOW_SPEED_REF
                                                         // 电压大于15V且DC-DC模块无故障，则进入低速模式
                                                         // 进入低俗模式的唯一入口
            else
            {
                mMCTRL_State = MCTRL_SM_ID_HiSpeed_MODE;
                Pre_CH2_ON = 1;                              // 继电器K2闭合，母线电容通过充电电阻充电。
                CpuTimer0Regs.TIM.all = mVar_RAM.TIMER0_PRD; // 定时500ms，左移2位是2秒，左移2位*4倍
                CpuTimer0Regs.TCR.bit.TSS = 0;               // 避免两个继电器同时闭合
            }
        }
        break;
    case MCTRL_SM_ID_LoSpeed_MODE: // 低速模式
        if (Enable == 0)
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE;
        else
        {

            if (MCV.Set_Speed_Ref == 0)
                mMCTRL_State = MCTRL_SM_ID_MD_PWM_OFF; // 转速设置为0则驱动器停止运行
            else
            {
                mSpeedCtrlMode = LoSpeedMode;      // 低速模式
                PWMEnFlg = 1;                      // 使能PWM输出
                mMCTRL_State = MCTRL_SM_ID_MD_RUN; /// 进入低速运行模式状态
            }
        }
        break;
    case MCTRL_SM_ID_HiSpeed_MODE: // 高速模式
        if (Enable == 0)
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE;
        else
        {
            mSpeedCtrlMode = HiSpeedMode; // 高速模式

            if (CpuTimer0Regs.TCR.bit.TIF == 1) // 等待定时时间
            {

                CpuTimer0Regs.TCR.bit.TIF == 0;
                mMCTRL_State = MCTRL_SM_ID_Bus_CHARGE; // 无故障，进入子模块充电准备
            }
        }
        break;

    case MCTRL_SM_ID_Bus_CHARGE: // 进入子模块充电准备，进入条件无故障
        if ((Enable == 0) || (PreCh2FB_STATUS == 0))
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE; // K2没有闭合，说明故障
        else
        {

            if (MCV.Vbus > mVar_RAM.Motor_Parameters.Driver_High_VBus_Threshold) // 电压阈值------to  do
            {
                // PWMSoftOutputOff();
                mMCTRL_State = MCTRL_SM_ID_Bus_CHARGE_DELAY;
                CpuTimer0Regs.TIM.all = mVar_RAM.TIMER0_PRD; // 定时0.5S
                CpuTimer0Regs.TCR.bit.TSS = 0;               // 达到设定电压后再充电1S
            }
        }
        break;
    case MCTRL_SM_ID_Bus_CHARGE_DELAY: // 充电延时
        if (Enable == 0)
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE;
        else
        {

            if (CpuTimer0Regs.TCR.bit.TIF == 1) // 等待定时时间
            {

                CpuTimer0Regs.TCR.bit.TIF == 0;
                // PWMSoftOutputOff();
                Pre_CH1_ON = 1;                                   // 继电器K1闭合,电容预充电过程结束
                mMCTRL_State = MCTRL_SM_ID_Bus_CHARGE_OVER_PRE;   // 跳转到充电延时结束状态
                CpuTimer0Regs.TIM.all = mVar_RAM.TIMER0_PRD >> 1; // 定时250ms
                CpuTimer0Regs.TCR.bit.TSS = 0;                    // K1闭合后，再充电250ms
            }
        }
        break;
    case MCTRL_SM_ID_Bus_CHARGE_OVER_PRE: // 充电延时
        if (Enable == 0)
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE;
        else
        {

            if (CpuTimer0Regs.TCR.bit.TIF == 1) // 等待定时时间
            {
                CpuTimer0Regs.TCR.bit.TIF == 0;
                mMCTRL_State = MCTRL_SM_ID_Bus_CHARGE_OVER;
            }
        }
        break;

    case MCTRL_SM_ID_Bus_CHARGE_OVER: // 充电结束状态
        if ((Enable == 0) || (PreCh1FB_STATUS == 0))
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE; //
        else if ((MCV.Vbus < mVar_RAM.Motor_Parameters.Driver_High_VBus_Threshold) && (mSpeedCtrlMode == HiSpeedMode))
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE; // 母线电压低压阈值----to  do
        else
        {

            if (MCV.Set_Speed_Ref == 0)
                mMCTRL_State = MCTRL_SM_ID_MD_PWM_OFF; // 转速设置为0则驱动器停止运行
            else
            {
                PWMEnFlg = 1;                      // 使能PWM输出
                mMCTRL_State = MCTRL_SM_ID_MD_RUN; /// 进入运行状态
            }
        }
        break;

    case MCTRL_SM_ID_MD_RUN: // Motor Driver 运行

        if ((Enable == 0) || ((PreCh1FB_STATUS == 0) && (mSpeedCtrlMode == HiSpeedMode)))
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE;
        else if ((MCV.Vbus < mVar_RAM.Motor_Parameters.Driver_High_VBus_Threshold) && (mSpeedCtrlMode == HiSpeedMode))
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE; // 母线电压低压阈值----to  do
        else
        {
            MotorDriverRunControl(); // motor driver运行控制
            if (PWMEnFlg == 1)       // 只有效一次
            {
                PWMSoftOutputOn(); // PWM 使能
                PWMEnFlg = 0;
            }

            if (CAN_CMD2FLG == 0x01) // 如果收到02报文，则更新输出转速
            {
                if (CAN_CMD2FLG == 1)
                    CAN_CMD2OKFLG = 1; // 该指令执行完，该字节被置1
                CAN_CMD2FLG = 0;       // 该指令执行完，该位被置0

                if (MCV.Set_Speed_Ref == 0)
                    mMCTRL_State = MCTRL_SM_ID_MD_PWM_OFF;
                else if ((fabsf(MCV.Set_Speed_Ref) < mVar_RAM.Motor_Parameters.Driver_Low_Speed_Ref) && (mSpeedCtrlMode == HiSpeedMode) && (DCtoDCFLTFlag == 0))
                {
                    if (fabsf(MCV.mSpeed) < fabsf(MCV.Set_Speed_Ref) && MCV.mSpeed * MCV.Set_Speed_Ref > 0)
                        mMCTRL_State = MCTRL_SM_ID_MD_PWM_OFF; // 模式发生改变,返回高低速选择
                }
                else if ((fabsf(MCV.Set_Speed_Ref) >= mVar_RAM.Motor_Parameters.Driver_Low_Speed_Ref) && (mSpeedCtrlMode == LoSpeedMode))
                    mMCTRL_State = MCTRL_SM_ID_MD_PWM_OFF; // 模式发生改变，返回高低速选择
                else
                {
                    ;
                }
            }
        }
        break;

    case MCTRL_SM_ID_MD_PWM_OFF: // 停止状态

        ASR_PI.Ui = 0; // 控制环的积分量归零。
        ACR_d_PID.Ui = 0;
        ACR_d_PID.Ud = 0;
        ACR_d_PID.Fdb_last = 0;
        ACR_q_PID.Ui = 0;
        ACR_q_PID.Ud = 0;
        ACR_q_PID.Fdb_last = 0;
        MCV.RampSet_Speed_Ref = 0;
        ipark1.d = 0; //
        ipark1.q = 0;
        // mDuty=0;

        PWMSoftOutputOff(); // PWM输出停止

        mMCTRL_State = MCTRL_SM_ID_IDLE; // 返回IDLE

        break;

    case MCTRL_SM_ID_SYSTEM_STOP_PRE:
        SYS_RUN_RSEN = 0;   // 上位机使能位置0，该位与按键SW1共同决定系统的使能。
        PWMSoftOutputOff(); // PWM OFF

        ASR_PI.Ui = 0; // 控制环的积分量归零。
        ACR_d_PID.Ui = 0;
        ACR_d_PID.Ud = 0;
        ACR_d_PID.Fdb_last = 0;
        ACR_q_PID.Ui = 0;
        ACR_q_PID.Ud = 0;
        ACR_q_PID.Fdb_last = 0;
        MCV.RampSet_Speed_Ref = 0;
        ipark1.d = 0; // ；
        ipark1.q = 0;

        mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP;
        CpuTimer0Regs.TIM.all = mVar_RAM.TIMER0_PRD;
        CpuTimer0Regs.TCR.bit.TSS = 0; // CPU定时器1计数开始
        break;
    case MCTRL_SM_ID_SYSTEM_STOP:
        if (CpuTimer0Regs.TCR.bit.TIF == 1) // CPU定时器0定时300ms
        {
            CpuTimer0Regs.TCR.bit.TIF == 0;
            Pre_CH1_OFF = 1; // 继电器K1断开
            Pre_CH2_OFF = 1; // 继电器K2断开
            mMCTRL_State = MCTRL_SM_ID_IDLE;

            CAN_CMD1FLG = 0; // 指令1收到标志位
            CAN_CMD2FLG = 0; // 指令2收到标志位
            CAN_CMD3FLG = 0;
            CAN_CMD4FLG = 0;
            CAN_CMD5FLG = 0;
            CAN_CMD6FLG = 0;
            CAN_CMD7FLG = 0;
            CAN_CMD8FLG = 0;
            CAN_CMD9FLG = 0;
            CAN_CMD10FLG = 0;
            CAN_CMD11FLG = 0;
            CAN_CMD12FLG = 0;
            CAN_CMD30FLG = 0;

            CAN_CMD1OKFLG = 0; // 0---未执行完，1-----执行完。
            CAN_CMD2OKFLG = 0; // 指令2收到标志位
            CAN_CMD3OKFLG = 0;
            CAN_CMD4OKFLG = 0;
            CAN_CMD5OKFLG = 0;
            CAN_CMD6OKFLG = 0;
            CAN_CMD7OKFLG = 0;
            CAN_CMD8OKFLG = 0;
            CAN_CMD9OKFLG = 0;
            CAN_CMD10OKFLG = 0;
            CAN_CMD11OKFLG = 0;
            CAN_CMD12OKFLG = 0;
            CAN_CMD30OKFLG = 0;
        }
        break;

    //------------故障模式-------//
    case MCTRL_SM_ID_FAULT: // 故障，收到0x01报文清除故障后进入MCTRL_SM_ID_IDLE

        PWMSoftOutputOff(); // PWM OFF

        ASR_PI.Ui = 0; // 控制环的积分量归零。
        ACR_d_PID.Ui = 0;
        ACR_d_PID.Ud = 0;
        ACR_d_PID.Fdb_last = 0;
        ACR_q_PID.Ui = 0;
        ACR_q_PID.Ud = 0;
        ACR_q_PID.Fdb_last = 0;
        MCV.RampSet_Speed_Ref = 0;
        ipark1.d = 0; // ；
        ipark1.q = 0;

        // if(CAN_CMD3FLG==0x01)//如果收到03报文，清除故障后进入MCTRL_SM_ID_FAULT_RET
        {

            CpuTimer1Regs.TIM.all = mVar_RAM.TIMER1_PRD << 6; // delay 0.1*64=6.4 seconds
            CpuTimer1Regs.TCR.bit.TSS = 0;                    // CPU定时器1计数开始

            mMCTRL_State = MCTRL_SM_ID_FAULT_RET; // MCTRL_SM_ID_IDLE;  等待故障返回
            if (CAN_CMD3FLG == 1)
                CAN_CMD3OKFLG = 1; // 该指令执行完，该字节被置1
            CAN_CMD3FLG = 0;       // 该指令执行完，该位被置0

            // Fault_LED_OFF=1;//故障LED指示， 1-无故障
            // CPLD_FLT_FLG_CLEAR=1;//Clear FPGA fault signals

            CAN_CMD1FLG = 0;
            CAN_CMD2FLG = 0; // 指令2收到标志位

            CAN_CMD4FLG = 0;
            CAN_CMD5FLG = 0;
            CAN_CMD6FLG = 0;
            CAN_CMD7FLG = 0;
            CAN_CMD8FLG = 0;
            CAN_CMD9FLG = 0;
            CAN_CMD10FLG = 0;
            CAN_CMD11FLG = 0;
            CAN_CMD12FLG = 0;

            CAN_CMD30FLG = 0;

            CAN_CMD40FLG = 0; // 指令40收到标志位.

            // SCI_CMD1OKFLG=0;//0---未执行完，1-----执行完。
            CAN_CMD1OKFLG = 0; // 0---未执行完，1-----执行完。
            CAN_CMD2OKFLG = 0; // 指令2收到标志位
            CAN_CMD3OKFLG = 0;
            CAN_CMD4OKFLG = 0;
            CAN_CMD5OKFLG = 0;
            CAN_CMD6OKFLG = 0;
            CAN_CMD7OKFLG = 0;
            CAN_CMD8OKFLG = 0;
            CAN_CMD9OKFLG = 0;
            CAN_CMD10OKFLG = 0;
            CAN_CMD11OKFLG = 0;
            CAN_CMD12OKFLG = 0;

            CAN_CMD30OKFLG = 0;
            CAN_CMD40OKFLG = 0; // 指令40执行完成标志位.
        }
        break;

    case MCTRL_SM_ID_FAULT_RET: // 故障复位完成后等待100ms进入MCTRL_SM_ID_IDLE

        if (CpuTimer1Regs.TCR.bit.TIF == 1) // CPU定时器1定时100ms
        {
            CpuTimer1Regs.TCR.bit.TIF == 0;
            CPLD_FLT_CLR; // 清除CPLD故障

            RST1_LOW = 1; // 驱动器复位引脚为低

            FaultReset();

            FaultDataLogEnable = 1; // 再次使能故障数据存储

            if (FaultFlag == 0)
            {
                Dis_CPLD_FLT_CLR; // CPLD故障清除完成
                RST1_HIGH = 1;    // 驱动器复位引脚为高

                mMCTRL_State = MCTRL_SM_ID_IDLE;
            }
        }
        break;

        // 零位校准//
    case MCTRL_SM_ID_ROTTX_ZERO_EN_PRE:
        if (Enable == 0)
        {
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE;
        }
        else
        {
            if (Pre_CH2_Status == 0)
            {
                Pre_CH2_ON = 1;                                     // 继电器K2闭合，母线电容通过充电电阻充电。
                CpuTimer0Regs.TIM.all = (mVar_RAM.TIMER0_PRD << 1); // 定时500ms，左移2位是2秒，左移2位*4倍
                CpuTimer0Regs.TCR.bit.TSS = 0;                      // 避免两个继电器同时闭合
            }
            else // K2已经闭合
            {
                if (CpuTimer0Regs.TCR.bit.TIF == 1) // 等待定时时间
                {
                    CpuTimer0Regs.TCR.bit.TIF = 0;
                    Pre_CH1_ON = 1;
                    CpuTimer0Regs.TIM.all = (mVar_RAM.TIMER0_PRD << 1); // 定时500ms，左移2位是2秒，左移2位*4倍
                    CpuTimer0Regs.TCR.bit.TSS = 0;                      // 避免两个继电器同时闭合
                    mMCTRL_State = MCTRL_SM_ID_ROTTX_ZERO_EN_PRE2;      // 无故障，进入第二阶段闭合主继电器
                }
            }
        }

        break;
    case MCTRL_SM_ID_ROTTX_ZERO_EN_PRE2:
        if (Enable == 0)
        {
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE;
        }
        else
        {
            if (Pre_CH1_Status == 0) // 此时继电器1应该已经闭合
            {
                mMCTRL_State = MCTRL_SM_ID_ROTTX_ZERO_EN_PRE;
            }
            else
            {
                if (CpuTimer0Regs.TCR.bit.TIF == 1) // 等待定时时间
                {
                    CpuTimer0Regs.TCR.bit.TIF = 0;
                    EPwm2Regs.CMPA.bit.CMPA = (Uint16)mVar_RAM.EPWM_PERIOD_Base; // 上管常关，下管常通--正计数法
                    EPwm3Regs.CMPA.bit.CMPA = (Uint16)mVar_RAM.EPWM_PERIOD_Base; // 上管常关，下管常通
                    PWMSoftOutputOn();                                           // PWM 使能
                    mMCTRL_State = MCTRL_SM_ID_ROTTX_ZERO_PULSE_ON;
                    ROTTX_ZERO_PulseCNT = 0;
                    MCV.mEthetaZero = 0.0f;
                }
            }
        }
        break;
    case MCTRL_SM_ID_ROTTX_ZERO_PULSE_ON:
        if (Enable == 0)
        {
            mMCTRL_State = MCTRL_SM_ID_SYSTEM_STOP_PRE;
        }
        else
        {
            if (fabsf(MCV.Ia) < mVar_RAM.Rottx_Zero_Current)
                EPwm1Regs.CMPA.bit.CMPA = (Uint16)(0.8f * mVar_RAM.EPWM_PERIOD_Base); // 发小脉冲,5%占空比
            else
            {
                EPwm1Regs.CMPA.bit.CMPA = (Uint16)mVar_RAM.EPWM_PERIOD_Base;
                ROTTX_ZERO_PulseCNT++;
                switch (ROTTX_ZERO_PulseCNT)
                {
                case 200:
                    MCV.mEthetaZero += 0.2f * MCV.mEtheta1;
                    break;
                case 300:
                    MCV.mEthetaZero += 0.2f * MCV.mEtheta1;
                    break;
                case 400:
                    MCV.mEthetaZero += 0.2f * MCV.mEtheta1;
                    break;
                case 500:
                    MCV.mEthetaZero += 0.2f * MCV.mEtheta1;
                    break;
                case 600:
                    MCV.mEthetaZero += 0.2f * MCV.mEtheta1;
                    break;
                case 601:
                    PWMSoftOutputOff(); // PWM 停止
                    mMCTRL_State = MCTRL_SM_ID_IDLE;
                    mVar_RAM.Motor_Parameters.Motor_Resolver_Zero = MCV.mEthetaZero;
                    break;
                default:
                    break;
                }
            }
        }

        break;
    default:
        break;
    }
}

//=======================================================
// 函数名称:   MMCRunCtrol()
// 功能:   MMC运行控制
// 输入:
// 返回:
// 备注:
//=======================================================
void MotorDriverRunControl(void)
{

    Controlloops(); // 转速电流双环控制。
    SVDPWM();       // SVPWM
    PWMUpdate();    // PWM更新函数
}

//=======================================================
// 函数名称:AVG_FILTER_init()
// 功能:    平均值滤波
// 输入:
// 返回:
// 备注:
//=======================================================
void AVG_FILTER_init(AVG_f32 *avg_filter, float init_value, Uint16 order, float *pbuf)
{
    Uint16 i;
    avg_filter->filter_order = order;
    avg_filter->filter_order_inv = 1.0 / ((float)order);
    avg_filter->pfilter_buffer = pbuf;
    for (i = 0; i < avg_filter->filter_order; i++)
    {
        avg_filter->pfilter_buffer[i] = init_value;
    }
    avg_filter->filter_sum = ((float)order) * ((float)init_value);
    avg_filter->filter_output = init_value;
    avg_filter->filter_index = 0;
}
//=======================================================
//                End of file.
//=======================================================
