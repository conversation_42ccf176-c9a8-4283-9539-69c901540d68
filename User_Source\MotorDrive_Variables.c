/*
 * user_variables_store.c
 *
 *  Created on: 2024年9月24日
 *      Author: lsqba
 */


#include "F28x_Project.h"
#include "MotorDrive_Defines.h"
#include "MotorDrive_Variables.h"
#include "F021_F2837xD_C28x.h"
#include "flash_programming_dcsm_c28.h"
#include "cnet.h"

//
// Defines
//
#define DEVICE_IP_U32       IPV4_TO_UINT32(192, 168, 137, 99)
#define GATEWAY_IP_U32      IPV4_TO_UINT32(192, 168, 137, 2)
#define PC_IP_U32           IPV4_TO_UINT32(192, 168, 137, 2)
#define NETWORK_MASK_U32    IPV4_TO_UINT32(255, 255, 255, 0)
#define DEICVE_PORT         (16011)
#define PC_PORT             (16011)

#define DSP_EPWM_PERIOD_14_2K       3500
#define DSP_EPWM_PERIOD_10K         5000

#define DSP_EPWM_DEADBAND_1U        250


#define DSP_TIMER_PERIOD_500MS      100000000ul
#define DSP_TIMER_PERIOD_100MS       20000000ul

#define DSP_FLASH_FINISH_FLAG       0xAA55AA55ul

#define ROTTX_ZERO_CURRENT          50.0f


// Motor PID Control parameters
#define PID_SPEED_PARAM_KP         1.5f
#define PID_SPEED_PARAM_KI         0.0001f
#define PID_SPEED_PARAM_KD         0.0f
#define PID_SPEED_PARAM_KD_FILTER  0.0f
#define PID_SPEED_PARAM_MAX        0.0f     // Maximum and minimum value of speed loop will be set dynamically
#define PID_SPEED_PARAM_MIN        0.0f

#define PID_ID_PARAM_KP            0.5f
#define PID_ID_PARAM_KI            0.0002f
#define PID_ID_PARAM_KD            0.0001f
#define PID_ID_PARAM_KD_FILTER     0.95f
#define PID_ID_PARAM_MAX           0.0f
#define PID_ID_PARAM_MIN           0.0f  

#define PID_IQ_PARAM_KP            0.5f
#define PID_IQ_PARAM_KI            0.0002f
#define PID_IQ_PARAM_KD            0.0001f
#define PID_IQ_PARAM_KD_FILTER     0.95f
#define PID_IQ_PARAM_MAX           0.0f
#define PID_IQ_PARAM_MIN           0.0f

// Motor Limit Values
#define MOTOR_IQ_MAX               700.0f
#define MOTOR_IQ_MIN               -700.0f
#define MOTOR_IQ_MAX_LOWSPEED      60.0f
#define MOTOR_IQ_MIN_LOWSPEED      -60.0f

#define MOTOR_ID_MAX               0.0f
#define MOTOR_ID_MIN               -300.0f
#define MOTOR_ID_MAX_LOWSPEED      0.0f
#define MOTOR_ID_MIN_LOWSPEED      0.0f

#define MOTOR_SPEED_MAX            3650.0f
#define MOTOR_SPEED_MIN            -3650.0f



//保护阈值定义//
#define MOTOR_IARM_OC_P         700   //  桥臂流过流正保护值
#define MOTOR_IARM_OC_N        -700   //  桥臂流过流负保护值
#define MOTOR_IDCIN_OC_P        580   //  输入直流电流过流正保护值
#define MOTOR_IDCIN_OC_N       -200   //  输入直流电流过流负保护值
#define MOTOR_VINVBUS_OV        930   //  逆变器母线电压过压保护值
#define MOTOR_VINVBUS_OV_RE     800   //  逆变器母线电压过压恢复值
#define MOTOR_VINPUT_OV         930   //  输入电压过压保护值
#define MOTOR_VINPUT_OV_RE      800   //  输入电压过压恢复值
#define MOTOR_VINVBUS_UV        -10   //  欠压保护值
#define MOTOR_VINPUT_UV         -10   //  欠压保护值
#define MOTOR_OT_IGBT_VALUE     95    //  IGBT过温保护值
#define MOTOR_OT_IGBT_RECOVERY_VALUE      60  // IGBT过温恢复值
#define MOTOR_OT_MOTOR_VALUE    190   //  电机绕组过温保护值
#define MOTOR_OT_MOTOR_RECOVERY_VALUE     150     // 电机绕组过温恢复值


/* 驱动器配置参数 */
#define DRIVER_LOW_SPEED_REF    320     // 转速为低速的参考限值
#define DRIVER_LOW_SPEED_VBUS_MIN   45      // 低速下最低母线电压
#define DRIVER_HIGH_VBUS_THRESHOLD  25//550     // 高压母线阈值，用于投切继电器
#define DRIVER_HIGH_VBUS_DIFF       20      // 输入和逆变器电容上电压差，用于判断是否投切继电器



#ifdef _FLASH
#pragma DATA_SECTION(mVar_Store_InUse, ".user_var_store");
#endif

#ifdef _FLASH
#pragma DATA_SECTION(mVar_Store_Backup, ".user_var_store_backup");
#endif


#define GRAB_FLASH_SEMAPHORE_ZONE1()   \
    do {                               \
        EALLOW;                        \
        DcsmCommonRegs.FLSEM.all = 0xA501; \
        EDIS;                          \
    } while (0)

#define GRAB_FLASH_SEMAPHORE_ZONE2()   \
    do {                               \
        EALLOW;                        \
        DcsmCommonRegs.FLSEM.all = 0xA502; \
        EDIS;                          \
    } while (0)    

#define RELEASE_FLASH_SEMAPHORE()   \
    do {                               \
        EALLOW;                        \
        DcsmCommonRegs.FLSEM.all = 0xA500; \
        EDIS;                          \
    } while (0)

// 硬编码初始化
const USER_VAR_STORE mVar_Store_InUse = {
    .dev_ip = DEVICE_IP_U32,
    .gw_ip = GATEWAY_IP_U32,
    .pc_ip = PC_IP_U32,
    .network_mask = NETWORK_MASK_U32,
    .port = (((uint32_t)DEICVE_PORT << 16) | PC_PORT),
    .EPWM_PERIOD_Base = DSP_EPWM_PERIOD_14_2K,
    .EPWM_DB = DSP_EPWM_DEADBAND_1U,
    .TIMER0_PRD = DSP_TIMER_PERIOD_500MS,
    .TIMER1_PRD = DSP_TIMER_PERIOD_100MS,
    .DutyMAX = 0,                   // Calculated from EPWM_PERIOD_Base, no need to store in flash
    .DutyMIN = 0,                   // Calculated from EPWM_PERIOD_Base, no need to store in flash
    .Rottx_Zero_Current = ROTTX_ZERO_CURRENT,
    // 初始化PID参数
    .PID_Parameters = {
        // 速度环PID参数
        .PID_Speed = {
            .PID_Kp = PID_SPEED_PARAM_KP,         // 比例系数
            .PID_Ki = PID_SPEED_PARAM_KI,         // 积分系数
            .PID_Kd = PID_SPEED_PARAM_KD,        // 微分系数
            .PID_Kd_Filter = PID_SPEED_PARAM_KD_FILTER,  // 微分滤波系数
        },
        
        // Id环PID参数（用于电流环d轴控制）
        .PID_Id = {
            .PID_Kp = PID_ID_PARAM_KP,         // 比例系数
            .PID_Ki = PID_ID_PARAM_KI,         // 积分系数
            .PID_Kd = PID_ID_PARAM_KD,         // 微分系数（电流环常不启用微分）
            .PID_Kd_Filter = PID_ID_PARAM_KD_FILTER,  // 微分滤波系数
        },
        
        // Iq环PID参数（用于电流环q轴控制）
        .PID_Iq = {
            .PID_Kp = PID_IQ_PARAM_KP,         // 比例系数
            .PID_Ki = PID_IQ_PARAM_KI,         // 积分系数
            .PID_Kd = PID_IQ_PARAM_KD,         // 微分系数（电流环常不启用微分）
            .PID_Kd_Filter = PID_IQ_PARAM_KD_FILTER,  // 微分滤波系数
        }
    },
    // 初始化限值参数
    .Motor_Limits = {
        .Motor_Id_Max = MOTOR_ID_MAX,
        .Motor_Id_Min = MOTOR_ID_MIN,
        .Motor_Id_Max_Lowspeed = MOTOR_ID_MAX_LOWSPEED,
        .Motor_Id_Min_Lowspeed = MOTOR_ID_MIN_LOWSPEED,

        .Motor_Iq_Max = MOTOR_IQ_MAX,
        .Motor_Iq_Min = MOTOR_IQ_MIN,
        .Motor_Iq_Max_Lowspeed = MOTOR_IQ_MAX_LOWSPEED,
        .Motor_Iq_Min_Lowspeed = MOTOR_IQ_MIN_LOWSPEED,

        .Motor_Speed_Max = MOTOR_SPEED_MAX,
        .Motor_Speed_Min = MOTOR_SPEED_MIN,
    },
    // 初始化电机参数
    .Motor_Parameters = {
        .Motor_Ld = MOTOR_LD,
        .Motor_Lq = MOTOR_LQ,
        .Motor_Flux = MOTOR_FLUX,
        .Motor_Rs = MOTOR_RS,
        .Motor_Pn = MOTOR_PN,
        .Motor_Resolver_Zero = MOTOR_RESOLVER_ZERO,
        .Motor_RpstoRpm_COEF = RpstoRpm_COEF_Base/MOTOR_PN,

        .Driver_Low_Speed_Ref = DRIVER_LOW_SPEED_REF,
        .Driver_Low_Speed_VBus_Min = DRIVER_LOW_SPEED_VBUS_MIN,
        .Driver_High_VBus_Threshold = DRIVER_HIGH_VBUS_THRESHOLD,
        .Driver_High_VBus_Diff = DRIVER_HIGH_VBUS_DIFF,
    },
    // 初始化保护阈值
    .Motor_Protect_Values = {
        .OC_Phase_Positive = MOTOR_IARM_OC_P,
        .OC_Phase_Negative = MOTOR_IARM_OC_N,
        .OC_IBUS_Positive = MOTOR_IDCIN_OC_P,
        .OC_IBUS_Negative = MOTOR_IDCIN_OC_N,
        .OV_INV_BUS = MOTOR_VINVBUS_OV,
        .OV_Input = MOTOR_VINPUT_OV,
        .UV_INV_BUS = MOTOR_VINVBUS_UV,
        .UV_Input = MOTOR_VINPUT_UV,
        .OT_IGBT = MOTOR_OT_IGBT_VALUE,
        .OT_IGBT_Recovery = MOTOR_OT_IGBT_RECOVERY_VALUE,
        .OT_MOTOR = MOTOR_OT_MOTOR_VALUE,
        .OT_MOTOR_Recovery = MOTOR_OT_MOTOR_RECOVERY_VALUE,
    },

    .mEncrypt = ENCRYPT_INIT_VALUE,
    .mFinishFlag = DSP_FLASH_FINISH_FLAG,
};

const USER_VAR_STORE mVar_Store_Backup;

USER_VAR_STORE mVar_RAM;

Uint16 mIsValidProgram;
Uint32 EncryptCalculation()
{
    Uint32 mUID;
    mUID = GetUID0();
    mUID += ENCRYPT_INIT_VALUE;
    mUID ^= ENCRYPT_INIT_VALUE;
    return mUID;
}

Uint16 IsValidProgram()
{
    return mIsValidProgram;
}

void CheckValidProgram()
{
    // 计算吻合
    if(mVar_RAM.mEncrypt == EncryptCalculation())
    {
        mIsValidProgram = 1;
    }
    else
    {
        mIsValidProgram = 0;
    }
}

void configVariableRAM(void)   // some variables in RAM should be configured after load into ram
{
    mVar_RAM.DutyMAX = 0.94f* (float)mVar_RAM.EPWM_PERIOD_Base;
    mVar_RAM.DutyMIN = 0.06f* (float)mVar_RAM.EPWM_PERIOD_Base;
    mVar_RAM.Motor_Parameters.Motor_RpstoRpm_COEF = RpstoRpm_COEF_Base / mVar_RAM.Motor_Parameters.Motor_Pn;
    mVar_RAM.Motor_Parameters.Motor_Ld_Inv = 1.0f / mVar_RAM.Motor_Parameters.Motor_Ld;
    mVar_RAM.Motor_Parameters.Motor_Lq_Inv = 1.0f / mVar_RAM.Motor_Parameters.Motor_Lq;
    mVar_RAM.Motor_Parameters.Motor_Pn_Inv = 1.0f / mVar_RAM.Motor_Parameters.Motor_Pn;
}

Uint16 CompareFlashData()     // Compare InUse and Backup data, return 0 the same, return n means number of different Uint16
{
    Uint16 *pBuf1,*pBuf2;
    Uint16 flag = 0,i=0;
    pBuf1 = (Uint16*)&mVar_Store_InUse;
    pBuf2 = (Uint16*)&mVar_Store_Backup;
    for(i=0;i<sizeof(USER_VAR_STORE)/sizeof(Uint16);i++)
    {
        if((*pBuf1) != (*pBuf2))    // the same
        {
            flag++;
        }
        pBuf1++;
        pBuf2++;
    }
    return flag;
}

void loadVariableToRAMDefault()
{
    loadVariableToRAM((void*)&mVar_Store_InUse);
}

void saveParamToFlash()
{
    SeizeFlashPump();
    GRAB_FLASH_SEMAPHORE_ZONE1();
    initFlashAPI();
    writeVariableFlash((void *)&mVar_Store_InUse);
    RELEASE_FLASH_SEMAPHORE();
    ReleaseFlashPump();
    // 计算加密结果，判断程序是否合法
    CheckValidProgram();
}

void InitUserStoreVar()
{
    mIsValidProgram = 0;
    SeizeFlashPump();
    GRAB_FLASH_SEMAPHORE_ZONE1();
    initFlashAPI();
    // Check mVar_Store_InUse status
    switch(mVar_Store_InUse.mFinishFlag)
    {
    case DSP_FLASH_FINISH_FLAG:     // mVar_Store_InUse is valid
        // Initialize mVar_RAM
        loadVariableToRAM((void*)&mVar_Store_InUse);
        configVariableRAM();
        if(CompareFlashData()>0)    // Backup data is different from InUse data
        {
            writeVariableFlash((void*)&mVar_Store_Backup); // backup data
        }
        
        break;
    default:    // Last wrote wrong
        if(mVar_Store_Backup.mFinishFlag == DSP_FLASH_FINISH_FLAG) // backup flash data is OK
        {
            loadVariableToRAM((void*)&mVar_Store_Backup);
            configVariableRAM();
            writeVariableFlash((void*)&mVar_Store_InUse);
        }
        else
        {
            // don't implement this, otherwise just erase flash sector will crack the encrypt
        }
        break;
    }

    RELEASE_FLASH_SEMAPHORE();
    ReleaseFlashPump();
    // 计算加密结果，判断程序是否合法
    CheckValidProgram();
}

void Init_Motor_Ctrl_Variables()
{
    // 计算扭矩常数 Kt = 1.5 * P * ψf
    MCV.Kt_inv = 1.5f * mVar_RAM.Motor_Parameters.Motor_Flux * mVar_RAM.Motor_Parameters.Motor_Pn;
    
    // 检查Kt是否有效，避免除零错误
    if (fabsf(MCV.Kt_inv) > 1e-6f)
    {
        MCV.inv_Kt = 1.0f / MCV.Kt_inv;
    }
    else
    {
        // Kt无效时的默认处理
        MCV.inv_Kt = 0.0f;
    }
}

//
// initFlashAPI - Initializes the flash API
//
#ifdef _FLASH
#pragma CODE_SECTION(initFlashAPI, ".TI.ramfunc");
#endif
void initFlashAPI(void)
{
    //
    // Enable writing to the EALLOW protected registers
    //
    EALLOW;
    //Flash_initModule(FLASH0CTRL_BASE, FLASH0ECC_BASE, 2);
    //
    // oReturnCheck is used to store the status of the flash API
    //
    Fapi_StatusType oReturnCheck;


    //
    // Initialize the flash API
    //
    oReturnCheck = Fapi_initializeAPI(F021_CPU0_BASE_ADDRESS,
                                      200);

    //
    // Check the status of the flash API for an error
    //
    if(oReturnCheck != Fapi_Status_Success)
    {
        __asm("    ESTOP0");
    }

    //
    // Initialize the Flash Memory Controller (FMC) and banks for an erase or
    // program command
    //
    oReturnCheck = Fapi_setActiveFlashBank(Fapi_FlashBank0);

    //
    // Check the status of the flash API for an error
    //
    if(oReturnCheck != Fapi_Status_Success)
    {
        __asm("    ESTOP0");
    }

    //
    // Disable writing to the EALLOW protected registers
    //
    EDIS;

    
}

// 读取Flash到RAM
void loadVariableToRAM(void *pBuf_src_start)
{
    Uint16 *pBuf1;
    Uint16 *pBuf2;
    Uint16 i;
    pBuf2 = (Uint16*)pBuf_src_start;
    pBuf1 = (Uint16*)&mVar_RAM;
    for(i=0;i<sizeof(USER_VAR_STORE)/sizeof(Uint16);i++)
    {
        *pBuf1++ = *pBuf2++;
    }
}

// 擦除Flash，必须在RAM中执行，不能开中断避免读写Flash
#ifdef _FLASH
#pragma CODE_SECTION(writeVariableFlash, ".TI.ramfunc");
#endif
void writeVariableFlash(void *pBuf_des_start)
{
    Fapi_StatusType oReturnCheck;
    Fapi_FlashStatusType oFlashStatus;
    Fapi_FlashStatusWordType oFlashStatusWord;


    Uint16 *pBuf_src_start;
    Uint16 *pBuf_src;
    Uint16 *pBuf_dec;

    Uint16 i,total_len;

    pBuf_src_start = (Uint16 *)(&mVar_RAM);



    EALLOW;

    // 擦除表所在Sector
    oReturnCheck = Fapi_issueAsyncCommandWithAddress(Fapi_EraseSector, (Uint32 *)(pBuf_des_start));

    //
    // Wait until FSM is done with erase sector operation
    //
    while(Fapi_checkFsmForReady() != Fapi_Status_FsmReady){}
    if(oReturnCheck != Fapi_Status_Success)
    {
        __asm("    ESTOP0");
    }

    //
    // Read FMSTAT contents to know the status of FSM
    // after erase command to see if there are any erase operation
    // related errors
    //
    oFlashStatus = Fapi_getFsmStatus();
    if (oFlashStatus!=Fapi_Status_Success)
    {
        __asm("    ESTOP0");
    }

    //
    // Do blank check.
    // Verify that the sector is erased.
    //
    oReturnCheck = Fapi_doBlankCheck((Uint32 *)(pBuf_des_start), Bzero_16KSector_u32length,&oFlashStatusWord);
    if(oReturnCheck != Fapi_Status_Success)
    {
        __asm("    ESTOP0");
    }


    total_len = sizeof(USER_VAR_STORE)/sizeof(Uint16);
    pBuf_src = (Uint16 *)pBuf_src_start;
    pBuf_dec = (Uint16 *)pBuf_des_start;

    for(i=0;i<total_len/8;i++)
    {
        //Fapi_setupBankSectorEnable(FLASH_WRAPPER_PROGRAM_BASE+FLASH_O_CMDWEPROTB,0b111111111101);
        oReturnCheck = Fapi_issueProgrammingCommand((Uint32 *)pBuf_dec,
                                                        pBuf_src,
                                                        8,
                                                        0,0,Fapi_AutoEccGeneration);

        while (Fapi_checkFsmForReady() != Fapi_Status_FsmReady) {}

        if(oReturnCheck != Fapi_Status_Success)
        {
            __asm("    ESTOP0");
        }

        oFlashStatus = Fapi_getFsmStatus();
        if(oFlashStatus != Fapi_Status_Success)
        {
            __asm("    ESTOP0");
        }
        pBuf_src += 8;
        pBuf_dec += 8;
    }

    if((total_len%8) > 0)
    {
        // Fapi_setupBankSectorEnable(FLASH_WRAPPER_PROGRAM_BASE+FLASH_O_CMDWEPROTB,0b111111111101);
        oReturnCheck = Fapi_issueProgrammingCommand((Uint32 *)pBuf_dec,
                                                        pBuf_src,
                                                        total_len%8,
                                                        0,0,Fapi_AutoEccGeneration);

        while (Fapi_checkFsmForReady() != Fapi_Status_FsmReady) {}

        if(oReturnCheck != Fapi_Status_Success)
        {
            __asm("    ESTOP0");
        }

        oFlashStatus = Fapi_getFsmStatus();
        if(oFlashStatus != Fapi_Status_Success)
        {
            __asm("    ESTOP0");
        }
    }


    oReturnCheck = Fapi_doVerify((Uint32 *)(pBuf_des_start),
                                 total_len>>1,
                                 (Uint32 *)(pBuf_src_start),
                                 &oFlashStatusWord);

    if(oReturnCheck != Fapi_Status_Success)
    {
        __asm("    ESTOP0");
    }
    Fapi_flushPipeline();
    EDIS;
}
