/**
 * @file APP_Net_User_CPU1.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-27
 * @last modified 2025-08-27
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#include "APP_Net.h"
#include "BFL_DebugPin.h"
#include "scheduler.h"
#include "HDL_CPU_Time.h"
#include "CPU_Define.h"
#include "ccommon.h"
#include <string.h>
#include <stddef.h>
#include "BFL_DebugPin.h"

#include "NetPacket.h"
#include "GSxRAM_Queue.h"
#include "GSxRAM_Define.h"
#include "APP_Net_Regs.h"
#include "APP_Net_Store.h"
#include "APP_Net_Regs_Sync.h"
#include "GSxRAM_Sample_Point_Queue.h"
#include "cnet.h"
#include "MotorDrive_Isr.h"

#if defined(CPU1)
// 收到网口设置配置寄存器消息
void APP_Net_On_Config_Register_Set(uint16_t wroteRegAddr, uint32_t wroteRegData);
void APP_Net_On_Command(uint16_t cmdAddr, uint32_t cmdData);

void APP_Net_Poll_User_CPU1(void)
{
    static PeriodREC_t rec1 = 0;
    static PeriodREC_t rec2 = 0;
    //    static uint16_t cnt = 0;
    if (period_query_user(&rec1, 65))
    {
        // 这里是模拟15kHz上传数据，测试显示(Debug Pin Set 为低电平，示波器测量)每次发送全部10给采样点耗时如下：
        // {10.123us，debug，O0优化）
        // {7.523us，APP部分代码O5优化，其他部分代码O0优化，离线运行）
        // {6.123us，APP部分代码O5优化，其他部分代码O0优化，
        // APP_Net_SendADCData_Uint16、GSxRam_Queue_FreeSize_1_2_FD0、GSxRam_Queue_Enqueue_1_2变为ramfunc, 离线运行}

        // {6.023us，APP部分代码O5优化，其他部分代码O0优化，
        // APP_Net_SendADCData_Uint16、GSxRam_Queue_FreeSize_1_2_FD0、GSxRam_Queue_Enqueue_1_2变为ramfunc, 离线运行，
        // GSxRam_Queue_FreeSize_1_2_FD0 取余优化+寄存器变量优化}
        // {4.323us，APP部分代码O5优化，其他部分代码O0优化，
        // APP_Net_SendADCData_Uint16、GSxRam_Queue_FreeSize_1_2_FD0、GSxRam_Queue_Enqueue_1_2变为ramfunc, 离线运行，
        // GSxRam_Queue_FreeSize_1_2_FD0 、GSxRam_Queue_Enqueue_1_2_FD0 取余优化+寄存器变量优化}
        // BFL_DebugPin_Set(DEBUG_PIN_1);
        // ADCSamplePointU16_t data;
        // data.type = 0xA1;
        // for (int i = 0; i < ADC_SAMPLE_CH_NUM; i++)
        // {
        //     data.ch[i] = cnt++ & 0xFF;
        // }
        // bool ret = APP_Net_SendADCData_Uint16(&data);
        // BFL_DebugPin_Reset(DEBUG_PIN_1);
        // {2.4630us，上述优化方法基础上，修改为按照采样点队列，避免单字节数据入队，直接提取待写入节点的内存地址写入
        // 采样数据，避免采样点的拷贝}
        // 使用浮点数传递数据给CPU2，由CPU2按照类型决定如何通过网卡发送到上位机，比了CPU1数据类型转换的耗时。
        //        BFL_DebugPin_Set(DEBUG_PIN_1);
        //        uint16_t freeSize = GSxRam_Sampling_Point_Queue_FreeSize_1_2();
        //        if (freeSize > 0)
        //        {
        //            volatile ADCSamplePoint_t *pPoint = GSxRam_Queue_Sampling_Point_1_2_GetWriteNodePointer();
        //            pPoint->type = 0xA2;
        //            for (int i = 0; i < ADC_SAMPLE_CH_NUM; i++)
        //            {
        //                pPoint->ch[i] = cnt++ & 0xFF;
        //            }
        //            GSxRam_Queue_Sampling_Point_1_2_EnqueueWriteNodePointer();
        //        }
        //        BFL_DebugPin_Reset(DEBUG_PIN_1);
        // ADCSamplePointFP32_t data;
        // g_cpu1_to_cpu2_info.sample_point_gsqueus_type == 0xA2;
        // data.type = 0xA2;
        // for (int i = 0; i < ADC_SAMPLE_CH_NUM; i++)
        // {
        //     data.ch[i] = (cnt++ & 0xFF) + 0.2f;
        // }
        // bool ret = APP_Net_SendADCData_Float32(&data);
    }

    if (period_query_user(&rec2, MS_TO_US(200)))
    {
        // Sync status register value to CPU2
        APP_Net_Regs_Sync_CPU1_TO_CPU2();
    }

    if (GSxRam_Queue_Size_2_1(0) > 0)
    {
        // The register value is need to change
        uint16_t wroteRegAddr = GSxRam_Queue_Dequeue_2_1(0);
        uint32_t wroteRegValue = APP_Net_ReadReg_From_CPU2(wroteRegAddr);
        APP_Net_On_Config_Register_Set(wroteRegAddr, wroteRegValue);
    }

    if (GSxRam_Queue_Size_2_1(1) > 0)
    {
        // Receive commands
        uint16_t cmdAddr = GSxRam_Queue_Dequeue_2_1(1);
        uint16_t cmdDataL = GSxRam_Queue_Dequeue_2_1(1);
        uint16_t cmdDataH = GSxRam_Queue_Dequeue_2_1(1);
        uint32_t cmd = ((uint32_t)cmdDataH << 16) | cmdDataL;
        APP_Net_On_Command(cmdAddr, cmd);
    }
}

#include "MotorDrive_Variables.h"
#include "MotorDrive_Isr.h"
#include "cbyte_utils.h"
#include "APP_Net_User_CPU1.h"

const void *regAddrToVarAddr[SYS_CFG_REG_NUM] = {
    [0] = NULL,
    [1] = &mVar_RAM.dev_ip,
    [2] = &mVar_RAM.gw_ip,
    [3] = &mVar_RAM.network_mask,
    [4] = NULL,
    [5] = &mVar_RAM.pc_ip,
    [6] = &mVar_RAM.port,
    [7] = NULL,
    [8] = &Vdcset_ref,
    [9] = &Iset_d_ref,
    [10] = &mVar_RAM.TIMER0_PRD,
    [11] = &mVar_RAM.TIMER1_PRD,
    [12] = &mVar_RAM.DutyMAX,
    [13] = &mVar_RAM.DutyMIN,
    [14] = &mVar_RAM.Rottx_Zero_Current,
    [15] = &mVar_RAM.PID_Parameters.PID_Speed.PID_Kp,
    [16] = &mVar_RAM.PID_Parameters.PID_Speed.PID_Ki,
    [17] = &mVar_RAM.PID_Parameters.PID_Speed.PID_Kd,
    [18] = &mVar_RAM.PID_Parameters.PID_Speed.PID_Kd_Filter,
    [19] = &mVar_RAM.PID_Parameters.PID_Id.PID_Kp,
    [20] = &mVar_RAM.PID_Parameters.PID_Id.PID_Ki,
    [21] = &mVar_RAM.PID_Parameters.PID_Id.PID_Kd,
    [22] = &mVar_RAM.PID_Parameters.PID_Id.PID_Kd_Filter,
    [23] = &mVar_RAM.PID_Parameters.PID_Iq.PID_Kp,
    [24] = &mVar_RAM.PID_Parameters.PID_Iq.PID_Ki,
    [25] = &mVar_RAM.PID_Parameters.PID_Iq.PID_Kd,
    [26] = &mVar_RAM.PID_Parameters.PID_Iq.PID_Kd_Filter,
    [27] = &mVar_RAM.Motor_Parameters.Motor_Rs,
    [28] = &mVar_RAM.Motor_Parameters.Motor_Pn,
    [29] = &mVar_RAM.Motor_Parameters.Motor_Resolver_Zero,
    [30] = &mVar_RAM.Motor_Parameters.Motor_RpstoRpm_COEF,
    [31] = &Idref,
    [32] = &Iqref,
    [33] = &mVar_RAM.Motor_Limits.Motor_Id_Max,
    [34] = &mVar_RAM.Motor_Limits.Motor_Id_Min,
    [35] = &mVar_RAM.Motor_Limits.Motor_Iq_Min_Lowspeed,
    [36] = &mVar_RAM.Motor_Limits.Motor_Speed_Max,
    [37] = &mVar_RAM.Motor_Limits.Motor_Speed_Min,
    [38] = &MCV.Iset_q_ref,
    [39] = &mVar_RAM.Motor_Limits.Reserved2,
    [40] = &mVar_RAM.Motor_Parameters.Motor_Ld,
    [41] = &mVar_RAM.Motor_Parameters.Motor_Ld_Inv,
    [42] = &mVar_RAM.Motor_Parameters.Motor_Lq,
    [43] = &mVar_RAM.Motor_Parameters.Motor_Lq_Inv,
    [44] = &mVar_RAM.Motor_Parameters.Motor_Flux,
    [45] = &mVar_RAM.Motor_Parameters.Motor_Rs,
    [46] = &mVar_RAM.Motor_Parameters.Motor_Pn,
    [47] = &mVar_RAM.Motor_Parameters.Motor_Pn_Inv,
    [48] = &mVar_RAM.Motor_Parameters.Motor_Resolver_Zero,
    [49] = &MCV.Set_Speed_Ref,
    [50] = &mVar_RAM.mEncrypt,
};

void APP_Net_Regs_Sync_CPU1_TO_CPU2()
{
    // Sync cpu1's default variable to registers, for first save
    APP_Net_WriteReg(0, GetUID0());                         // UID
    APP_Net_WriteReg(1, mVar_RAM.dev_ip); // Self IP
    APP_Net_WriteReg(2, mVar_RAM.gw_ip);  // Gateway IP
    APP_Net_WriteReg(3, mVar_RAM.network_mask);  // Netmask
    APP_Net_WriteReg(4, GetUID0());                         // MAC Address Low 4 bytes, here use UID
    APP_Net_WriteReg(5, mVar_RAM.pc_ip);  // Dest IP
    APP_Net_WriteReg(6, mVar_RAM.port); // Dest IP
    APP_Net_WriteReg(7, 0);    // 保留

    // USER_VAR_STORE mVar_RAM
    extern USER_VAR_STORE mVar_RAM;
    extern const USER_VAR_STORE mVar_Store_InUse;

    int addr = 8;
    APP_Net_WriteReg(addr++, Vdcset_ref);//8
    APP_Net_WriteReg(addr++, Iset_d_ref);//9
    APP_Net_WriteReg(addr++, mVar_RAM.TIMER0_PRD); //10
    APP_Net_WriteReg(addr++, mVar_RAM.TIMER1_PRD); //11
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.DutyMAX)); //12
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.DutyMIN)); //13
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Rottx_Zero_Current));//14
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Speed.PID_Kp)); //15
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Speed.PID_Ki)); //16
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Speed.PID_Kd)); //17
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Speed.PID_Kd_Filter)); //18
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Id.PID_Kp)); //19
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Id.PID_Ki)); //20
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Id.PID_Kd)); //21
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Id.PID_Kd_Filter)); //22
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Iq.PID_Kp)); //23
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Iq.PID_Ki)); //24
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Iq.PID_Kd)); //25
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.PID_Parameters.PID_Iq.PID_Kd_Filter)); //26
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_Rs)); //27
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_Pn)); //28
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_Resolver_Zero)); //29
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_RpstoRpm_COEF)); //30
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(Idref)); //31
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(Iqref)); //32
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Limits.Motor_Id_Max)); //33
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Limits.Motor_Id_Min)); //34
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Limits.Motor_Iq_Min_Lowspeed)); //35
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Limits.Motor_Speed_Max)); //36
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Limits.Motor_Speed_Min)); //37
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Iset_q_ref)); //38
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Te_set_ref)); //39
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_Ld)); //40
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_Ld_Inv)); //41
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_Lq)); //42
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_Lq_Inv)); //43
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_Flux)); //44
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.inv_Kt)); //45
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_Pn)); //46
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_Pn_Inv));
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(mVar_RAM.Motor_Parameters.Motor_Resolver_Zero));
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Set_Speed_Ref));
    APP_Net_WriteReg(addr++, mVar_RAM.mEncrypt); // 8+43 = 51

    addr = SYS_STATUS_REG_ADDR_BASE;
    extern MOTOR_CONTROL_VARIABLES MCV;
    float Ud__ = ipark1.d;
    float Uq__ = ipark1.q;
    uint32_t temperature = PACK_U32_FROM_U8(mMsgDATA_D.Temperature,
                                            mMsgDATA_C.Temperature,
                                            mMsgDATA_B.Temperature,
                                            mMCTRL_State);
    uint32_t error_code = PACK_U32_FROM_U8(mMsgDATA_D.errcode.all,
                                           mMsgDATA_C.errcode.all,
                                           mMsgDATA_B.errcode.all,
                                           0);
    // SYS_STATUS_REG_ADDR_BASE
    APP_Net_WriteReg(addr++, temperature);                                                             // 55
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(ids));                                           // 56
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(iqs));                                           // 57
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(Idref));                                         // 58
    APP_Net_WriteReg(addr++, 0);                                         // 59
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(Ud__));                                          // 60
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(Uq__));                                          // 61
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mEtheta1));                                  // 62
    APP_Net_WriteReg(addr++, error_code);                                                              // 63
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mDuty));                                     // 64
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mPT1));                                      // 65
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mPT2));                                      // 66
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mPT3));                                      // 67
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mPT4));                                      // 68
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mPT5));                                      // 69
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mNTC2));                                     // 70
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mTempNTC1));                                 // 71
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mTempNTC2));                                 // 72
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Ia));                                        // 73 OOO
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Ib));                                        // 74 OOO
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Ic));                                        // 75 OOO
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Vbus));                                      // 76
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Vbus_In));                                   // 77
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Vbus_filter));                               // 78
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Ibus));                                      // 79
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mEtheta));                                  // 80
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mEthetaZero));                               // 81
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mEthetaAVG));                         // 82
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mSpeed));                                    // 83
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mDuty));                                     // 84
    // 
    uint32_t sys_mode_reg = (SYS_OpertionMode & 0x07) | ((RotTX_ZeroEN_FLG & 0x01) << 3);
    APP_Net_WriteReg(addr++, sys_mode_reg);                                                        // 85
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mTe_ref));                                   // 86
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Va));                                        // 87
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Vb));                                        // 88
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.Vc));                                        // 89
    // SYS_STATUS_REG_ADDR_BASE + 35 = 89

    extern volatile struct CAN_TXSTATUS1_STRUC mCANStatus1Tran;
    extern volatile struct CAN_TXSTATUS2_STRUC mCANStatus2Tran;

    uint32_t OCFLT = mCANStatus1Tran.OCFLT.all;
    uint32_t SYSSTATUS = mCANStatus1Tran.SYSSTATUS.all;
    uint32_t INVSTATUS = mCANStatus2Tran.INVSTATUS.all;
    uint32_t MOTSTATUS = mCANStatus2Tran.MOTSTATUS.all;
    uint32_t flag1 = (OCFLT << 24) | (SYSSTATUS << 16) | (INVSTATUS << 8) | MOTSTATUS;
    APP_Net_WriteReg(addr++, flag1); // 90
    APP_Net_WriteReg(addr++, MCV.ResovlerFault); // 91
    APP_Net_WriteReg(addr++, FLOAT_TO_U32_FIXED_POINT(MCV.mEthetaRad)); // 92
}

void APP_Net_On_Config_Register_Set(uint16_t wroteRegAddr, uint32_t wroteRegData)
{
    // 这里处理寄存器设置
    // 限幅
    /*
    因为需要CPU1来限幅处理，所以如果需要CPU2保存参数那么CPU2就只能去保存
    CPU2限幅后的寄存器值。上传的寄存器值就是CPU1保存的。
    这样就需要按照如下步骤处理：
    1.收到寄存器修改的数据
    2.限幅，对比和当前内存中的值是否发生了变化。
    3.如果发生了变化那么更新CPU1寄存器，调用如下方法通知CPU2固化数据。
    APP_Net_Notify_CPU2_Store_Regs_In_Flash();
    */
    // 限制允许修改的寄存器范围，REG_mEncrypt不处理
    if (!(wroteRegAddr >= REG_DEV_IP &&
          wroteRegAddr < REG_mEncrypt))
    {
        return;
    }

    if (wroteRegAddr >= REG_Vdcset_ref &&
        wroteRegAddr <= REG_Iset_d_ref)
    {
        // 处理uint6_t寄存器

        // 限幅

        // 更新变量
        uint16_t * pVar = (uint16_t *)regAddrToVarAddr[wroteRegAddr];
        if(pVar != NULL)
        {
            *(pVar) = (uint16_t)wroteRegData;
        }
    }
    else if ((wroteRegAddr >= REG_DEV_IP &&
             wroteRegAddr <= REG_RESERVED) ||
            (wroteRegAddr >= REG_TIMER0_PRD &&
             wroteRegAddr <= REG_TIMER1_PRD)
            )
    {
        // 处理uint32_t寄存器
        uint32_t * pVar = (uint32_t *)regAddrToVarAddr[wroteRegAddr];
        if(pVar != NULL)
        {
            *(pVar) = (uint32_t)wroteRegData;
        }
    }
    else if (wroteRegAddr >= REG_DutyMAX &&
            wroteRegAddr <= REG_Motor_Parameters_Motor_RpstoRpm_COEF)
    {
        // 处理定点数寄存器
        // 同位宽的整数之间转换只是改变解释方式，不改变任何bit
        // 但是浮点数和整数之间则会修改数据的bit
        // 所以要先转换为int32_t再转换为float32
        float value = U32_FIXED_POINT_TO_FLOAT(wroteRegData);

        // 限幅
        if (wroteRegAddr == 49)
        {
            value = -value;
            MCV.Last_Set_Speed_Ref = MCV.Set_Speed_Ref;
            MCV.Set_Speed_Ref = value;

            if (MCV.Set_Speed_Ref > mVar_RAM.Motor_Limits.Motor_Speed_Max)
            {
                MCV.Set_Speed_Ref = mVar_RAM.Motor_Limits.Motor_Speed_Max; // 转速限幅
            }
            else if (MCV.Set_Speed_Ref < mVar_RAM.Motor_Limits.Motor_Speed_Min)
            {
                MCV.Set_Speed_Ref = mVar_RAM.Motor_Limits.Motor_Speed_Min;
            }

            // 新的转速设置
            if (MCV.Last_Set_Speed_Ref != MCV.Set_Speed_Ref)
            {
                MCV.RampSet_Speed_Ref = MCV.mSpeed;
            }

            CAN_CMD2FLG = 1;   // 该指令执行完，该位被置0
        }else if(wroteRegAddr == 38)
        {
            if (value > 600)
            {
                value = 600;
            }else if (value < -600)
            {
                value = -600;
            }
            
            MCV.Iset_q_ref = value;
        }else if(wroteRegAddr == 39)
        {
            if (value > 900)
            {
                value = 900;
            }else if (value < -900)
            {
                value = -900;
            }

            MCV.Te_set_ref = value;
            float Iset_q_ref = MCV.inv_Kt * MCV.Te_set_ref;
            if (Iset_q_ref > 600)
            {
                Iset_q_ref = 600;
            }else if (Iset_q_ref < -600)
            {
                Iset_q_ref = -600;
            }
            MCV.Iset_q_ref = Iset_q_ref;
        }
        else
        {
            float * pVar = (float *)regAddrToVarAddr[wroteRegAddr];
            if(pVar != NULL)
            {
                *(pVar) = value;
            }
        }

        CANTimeOutCNT = 0; // 超时计数器清零
    }
}

void APP_Net_On_Command(uint16_t cmdAddr, uint32_t cmdData)
{
    // 这里处理停止之类的命令
    switch (cmdData)
    {
    case 0:
        BFL_DebugPin_Reset(DEBUG_PIN_1);
        break;
    case 1:
        BFL_DebugPin_Set(DEBUG_PIN_1);
        break;
    case 2:
        BFL_DebugPin_Toggle(DEBUG_PIN_1);
        break;
    case 0xA5:
        saveParamToFlash();
        break;
    case 0x1C:
        if (MCV.Set_Speed_Ref == 0) // 驱动器不运行时才可进行参数设置
        {
            RotTX_ZeroEN_FLG = 1;
        }
        CAN_CMD5FLG = 1; // 该指令执行完，该位被置0
        break;
    case 0x1D:
        SYS_OpertionMode = 1; // 电流环模式
        break;
    case 0x1E:
        SYS_OpertionMode = 2; // 速度环环模式
        break;
    case 0x1F:
        SYS_OpertionMode = 0; 
        break;
    case 0xCA:
        HDL_CPU_Reset();
//        break;
    default:
        break;
    }
}

void APP_Net_Regs_Sync_CPU2_TO_CPU1()
{
    // 是否使用CPU2保存的参数：1是，0否
#define USING_CPU2_SAVED_PARAM 0
#if USING_CPU2_SAVED_PARAM
    // Synchronize flash register values saved from CPU2 to CPU1's variables
    if (APP_Net_Is_Param_Load_Success())
    {
        // CPU2保存的参数加载成功才重新载入，否则使用默认参数
        APP_Net_BurstReadReg_From_CPU2(
            (SysRegs_t *)&g_cpu1_to_cpu2_info.sys_regs_for_send);

        // Sync to variable
        uint32_t *cpu1_regs = &g_cpu1_to_cpu2_info.sys_regs_for_send.reg;
        for (int addr = REG_Vdcset_ref; addr < REG_mEncrypt; addr++)
        {
            APP_Net_On_Config_Register_Set(addr, cpu1_regs[addr]);
        }
    }

#endif // USING_CPU2_SAVED_PARAM
}
#endif // CPU1
