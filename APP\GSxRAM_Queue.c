/**
 * @file GSxRAM_Queue.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-24
 * @last modified 2025-08-24
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#include "GSxRAM_Queue.h"
#include "ccommon.h"
#include "GSxRAM_Define.h"

#ifdef __cplusplus
#pragma DATA_SECTION("Cpu1ToCpu2InfoFile")
#else
#pragma DATA_SECTION(g_app_net_cmd_buf_1_2, "Cpu1ToCpu2InfoFile");
#endif
volatile byte_t g_app_net_cmd_buf_1_2[APP_NET_1_2_CMD_BUF_SIZE] = {0};

#ifdef __cplusplus
#pragma DATA_SECTION("Cpu2ToCpu1InfoFile")
#else
#pragma DATA_SECTION(g_app_net_buf_2_1, "Cpu2ToCpu1InfoFile");
#endif
volatile byte_t g_app_net_buf_2_1[2][APP_NET_2_1_BUF_SIZE] = {0};
//========================================

uint16_t GSxRam_Queue_Dequeue_1_2_FD1()
{
    // Please ensure that the queue is not empty before calling this function
    uint16_t data = g_app_net_cmd_buf_1_2[g_cpu2_to_cpu1_info.buf_r_ptr[1]];
    g_cpu2_to_cpu1_info.buf_r_ptr[1] = (g_cpu2_to_cpu1_info.buf_r_ptr[1] + 1) % APP_NET_1_2_CMD_BUF_SIZE;
    return data;
}

void GSxRam_Queue_Enqueue_1_2_FD1(uint16_t data)
{
    // Please ensure that the queue is not full before calling this function
    int16_t wPtr = g_cpu1_to_cpu2_info.buf_w_ptr[1];
    int16_t newWPtr;

    // 直接写入数据
    g_app_net_cmd_buf_1_2[wPtr] = data;

    // 递增并检查是否需要回绕
    newWPtr = wPtr + 1;
    if (newWPtr >= APP_NET_1_2_CMD_BUF_SIZE) {
        newWPtr = 0;
    }
    g_cpu1_to_cpu2_info.buf_w_ptr[1] = newWPtr;
}

uint16_t GSxRam_Queue_Size_1_2_FD1()
{
    int16_t wPtr = g_cpu1_to_cpu2_info.buf_w_ptr[1];
    int16_t rPtr = g_cpu2_to_cpu1_info.buf_r_ptr[1];
    // cycle buffer
    uint16_t elementSize = (wPtr - rPtr + APP_NET_1_2_CMD_BUF_SIZE) % APP_NET_1_2_CMD_BUF_SIZE;
    return elementSize;
}

uint16_t GSxRam_Queue_FreeSize_1_2_FD1()
{
    int16_t wPtr = g_cpu1_to_cpu2_info.buf_w_ptr[1];
    int16_t rPtr = g_cpu2_to_cpu1_info.buf_r_ptr[1];
    int16_t diff;

    // 使用寄存器变量，减少内存访问
    diff = (rPtr - wPtr - 1);

    // 利用分支预测，大多数情况下不需要加上缓冲区大小
    if (diff < 0) {
        diff += APP_NET_1_2_CMD_BUF_SIZE;
    }

    return (uint16_t)diff;
}

//========================================

/**
 * @brief Dequeue data from gsxram queue
 *
 * @param fd
 * @return uint16_t
 */
uint16_t GSxRam_Queue_Dequeue_2_1(uint16_t fd)
{
    // Please ensure that the queue is not empty before calling this function
    uint16_t data = g_app_net_buf_2_1[fd][g_cpu1_to_cpu2_info.buf_r_ptr[fd]];
    g_cpu1_to_cpu2_info.buf_r_ptr[fd] = (g_cpu1_to_cpu2_info.buf_r_ptr[fd] + 1) % APP_NET_2_1_BUF_SIZE;
    return data;
}

/**
 * @brief Enqueue data to gsxram queue
 *
 * @param _fd The buffer index
 * @param _data uint16_t data
 *
 */
void GSxRam_Queue_Enqueue_2_1(uint16_t fd, uint16_t data)
{
    // Please ensure that the queue is not full before calling this function
    g_app_net_buf_2_1[fd][g_cpu2_to_cpu1_info.buf_w_ptr[fd]] = data;
    g_cpu2_to_cpu1_info.buf_w_ptr[fd] = (g_cpu2_to_cpu1_info.buf_w_ptr[fd] + 1) % APP_NET_2_1_BUF_SIZE;
}

/**
 * @brief Get the element size currently in the queue
 *
 * @param fd
 * @return uint16_t
 */
uint16_t GSxRam_Queue_Size_2_1(uint16_t fd)
{
    int16_t wPtr = g_cpu2_to_cpu1_info.buf_w_ptr[fd];
    int16_t rPtr = g_cpu1_to_cpu2_info.buf_r_ptr[fd];
    // cycle buffer
    uint16_t elementSize = (wPtr - rPtr + APP_NET_2_1_BUF_SIZE) % APP_NET_2_1_BUF_SIZE;
    return elementSize;
}

/**
 * @brief Get the free space in the queue
 *
 * @param fd
 * @return uint16_t
 */
uint16_t GSxRam_Queue_FreeSize_2_1(uint16_t fd)
{
    int16_t wPtr = g_cpu2_to_cpu1_info.buf_w_ptr[fd];
    int16_t rPtr = g_cpu1_to_cpu2_info.buf_r_ptr[fd];
    // cycle buffer
    uint16_t freeSize = (rPtr - wPtr - 1 + APP_NET_2_1_BUF_SIZE) % APP_NET_2_1_BUF_SIZE;
    return freeSize;
}
