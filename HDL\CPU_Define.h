/**
 * @file CPU_Define.h
 * <AUTHOR> (l<PERSON><PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2024-04-17
 * @last modified 2024-04-17
 *
 * @copyright Copyright (c) 2024 Liu <PERSON> Personal.
 *
 */
#ifndef CPU_DEFINE_H
#define CPU_DEFINE_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>

#include "F28x_Project.h"

#define DISABLE_INT() _disable_interrupts()
#define ENABLE_INT() _enable_interrupts()

#define HDL_CPU_Reset()           \
    do                            \
    {                             \
        DINT;                     \
        EALLOW;                   \
        WdRegs.WDCR.all = 0x0028; \
        while (1)                 \
            ;                     \
    } while (0)

    typedef enum E_CPU_ID
    {
        CPU1_ID = 1,
        CPU2_ID = 2,
        CPU_CORE_NUM = 2,
    } CPU_ID_t;
#ifdef __cplusplus
}
#endif
#endif //! CPU_DEFINE_H
