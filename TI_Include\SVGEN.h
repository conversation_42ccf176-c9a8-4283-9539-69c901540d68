//#############################################################################
// $Copyright:
// $
//#############################################################################

/* =================================================================================
File name:       SVGEN.H  
===================================================================================*/


#ifndef __SVGEN_H__
#define __SVGEN_H__

typedef struct 	{ float  Ualpha; 			// Input: reference alpha-axis phase voltage
				  float  Ubeta;			// Input: reference beta-axis phase voltage
                  float  Ta;				// Output: reference phase-a switching function
                  float  Tb;				// Output: reference phase-b switching function
                  float  Tc;				// Output: reference phase-c switching function
                  float  tmp1;			// Variable: temp variable
                  float  tmp2;			// Variable: temp variable
                  float  tmp3;			// Variable: temp variable
				  Uint16 VecSector;		// Space vector sector
				} SVGEN;
																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																				

extern SVGEN svgen1;
/*-----------------------------------------------------------------------------
Default initalizer for the SVGENDPWM object.
-----------------------------------------------------------------------------*/                     
#define SVGENDPWM_DEFAULTS { 0,0,0,0,0 }                       

/*------------------------------------------------------------------------------
Space Vector  Generator for Discontinuous PWM (SVGEN) Macro Definition
------------------------------------------
------------------------------------*/


static inline void runSVGEN(SVGEN *v)//Refer to svgen_dq.pdf---SLu modifiedUint16 Sector = 0;
{
    float Va,Vb,Vc,t1,t2,temp_sv1,temp_sv2;
    float X,Y,Z;
    Uint16 N = 0;
//#define SVGENDPWM_MACRO(v)

    N = 0;
    temp_sv1=v->Ubeta*0.5;                  /*divide by 2*/
    temp_sv2=0.8660254*v->Ualpha;   /* 0.8660254 = sqrt(3)/2*/

    /* Inverse clarke transformation */
    Va = v->Ubeta;
    Vb = -temp_sv1 + temp_sv2;
    Vc = -temp_sv1 - temp_sv2;
    /* 60 degree Sector determination */
    if (Va>0) N = 1;
    if (Vb>0) N = N+2;
    if (Vc>0) N = N+4;
   // v.N = Sector;
    /* X,Y,Z  calculations */
    X = v->Ubeta;
    Y = temp_sv1 + temp_sv2;
    Z = temp_sv1 - temp_sv2;
    /* Sector 0: this is special case for (Ualpha,Ubeta) = (0,0)*/
    switch(N)
     {
        case 0:
            v->Ta = 0.5;
            v->Tb = 0.5;
            v->Tc = 0.5;
            break;
        case 1:   /*Sector 1: t1=Z and t2=Y (abc ---> Tb,Ta,Tc)*/
            v->VecSector=2;//Sector II
            t1 = Z;
            t2 = Y;
            v->Tb=(1-t1-t2)*0.5;
            v->Ta = v->Tb+t1;             /* taon = tbon+t1       */
            v->Tc = v->Ta+t2;             /* tcon = taon+t2       */
            break;
        case 2:   /* Sector 2: t1=Y and t2=-X (abc ---> Ta,Tc,Tb)*/
            v->VecSector=6;//Sector VI
            t1 = Y;
            t2 = -X;
            v->Ta=(1-t1-t2)*0.5;
            v->Tc = v->Ta+t1;             /*  tcon = taon+t1      */
            v->Tb = v->Tc+t2;             /*  tbon = tcon+t2      */
            break;
        case 3:   /* Sector 3: t1=-Z and t2=X (abc ---> Ta,Tb,Tc)*/
            v->VecSector=1;//Sector I
            t1 = -Z;
            t2 = X;
            v->Ta=(1-t1-t2)*0.5;
            v->Tb = v->Ta+t1;             /*  tbon = taon+t1      */
            v->Tc = v->Tb+t2;             /*  tcon = tbon+t2      */
            break;
        case 4:   /* Sector 4: t1=-X and t2=Z (abc ---> Tc,Tb,Ta)*/
            v->VecSector=4;//Sector IV
            t1 = -X;
            t2 = Z;
            v->Tc=(1-t1-t2)*0.5;
            v->Tb = v->Tc+t1;             /*  tbon = tcon+t1      */
            v->Ta = v->Tb+t2;             /*  taon = tbon+t2      */
            break;
        case 5:   /* Sector 5: t1=X and t2=-Y (abc ---> Tb,Tc,Ta)*/
            v->VecSector=3;//Sector III
            t1 = X;
            t2 = -Y;                   /*  tbon = (1-t1-t2)/2  */
            v->Tb=(1-t1-t2)*0.5;
            v->Tc = v->Tb+t1;             /*  taon = tcon+t2      */
            v->Ta = v->Tc+t2;
            break;
        case 6:   /* Sector 6: t1=-Y and t2=-Z (abc ---> Tc,Ta,Tb)*/
            v->VecSector=5;//Sector V
            t1 = -Y;
            t2 = -Z;
            v->Tc=(1-t1-t2)*0.5;
            v->Ta = v->Tc+t1;             /*  taon = tcon+t1      */
            v->Tb = v->Ta+t2;             /*  tbon = taon+t2      */
            break;
     }
    /*  Convert the unsigned GLOBAL_Q format (ranged (0,1)) ->.. */
    /*  ..signed GLOBAL_Q format (ranged (-1,1))*/
    v->Ta = 2*(v->Ta-0.5);
    v->Tb = 2*(v->Tb-0.5);
    v->Tc = 2*(v->Tc-0.5);

}
				//
//	v.tmp1= v.Ubeta;															\
//	v.tmp2= _IQdiv2(v.Ubeta) + (_IQmpy(_IQ(0.866),v.Ualpha));					\
//    v.tmp3= v.tmp2 - v.tmp1;													\
//																				\
//	v.VecSector=3;																\
//	v.VecSector=(v.tmp2> 0)?( v.VecSector-1):v.VecSector;						\
//	v.VecSector=(v.tmp3> 0)?( v.VecSector-1):v.VecSector;						\
//	v.VecSector=(v.tmp1< 0)?(7-v.VecSector) :v.VecSector;						\
//																				\
//	if     (v.VecSector==1 || v.VecSector==6)									\
//		{																		\
//			v.Ta= 0; 															\
//			v.Tb= v.tmp3; 														\
//			v.Tc= v.tmp2 ;														\
//		}																		\
//	else if(v.VecSector==2 || v.VecSector==3)									\
//		{																		\
//			v.Ta= -v.tmp3; 														\
//			v.Tb= 0; 															\
//			v.Tc= v.tmp1;														\
//		}																		\
//	else 																		\
//		{																		\
//			v.Ta= -v.tmp2; 														\
//			v.Tb= -v.tmp1; 														\
//			v.Tc= 0;															\
//		}																		\
//																				\
//	v.Ta= _IQmpy2(v.Ta)-_IQ(1.0);												\
//	v.Tb= _IQmpy2(v.Tb)-_IQ(1.0);												\
//	v.Tc= _IQmpy2(v.Tc)-_IQ(1.0);												\
	
//  v.Tx is converted from (0,1) range to (-1,1) for PWM macro   
	
#endif // __SVGEN_H__
