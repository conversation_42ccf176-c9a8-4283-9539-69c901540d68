/**
* @file GSxRAM_Define.c
* <AUTHOR> (liu<PERSON><PERSON>@outlook.com)
* @brief
* @version 0.1
* @date 2025-08-24
* @last modified 2025-08-24
*
* @copyright Copyright (c) 2025 Liu <PERSON> Personal.
*
*/
#include "GSxRAM_Define.h"
//----------------------------------------
#ifdef __cplusplus
#pragma DATA_SECTION("Cpu1ToCpu2InfoFile")
#else
#pragma DATA_SECTION(g_cpu1_to_cpu2_info, "Cpu1ToCpu2InfoFile");
#endif
volatile CPU1ToCPU2Info_t g_cpu1_to_cpu2_info = {0};

//----------------------------------------
#ifdef __cplusplus
#pragma DATA_SECTION("Cpu2ToCpu1InfoFile")
#else
#pragma DATA_SECTION(g_cpu2_to_cpu1_info, "Cpu2ToCpu1InfoFile");
#endif
volatile CPU2ToCPU1Info_t g_cpu2_to_cpu1_info = {0};

//----------------------------------------
#ifdef __cplusplus
#pragma DATA_SECTION("RegsFlashSaveFile")
#else
#pragma DATA_SECTION(g_sys_regs_for_save, "RegsFlashSaveFile");
#endif
const volatile SysRegs_t g_sys_regs_for_save;
