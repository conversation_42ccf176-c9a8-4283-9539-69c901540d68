/**
 * @file BFL_DebugPin.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2024-05-16
 * @last modified 2024-05-16
 *
 * @copyright Copyright (c) 2024 Liu <PERSON> Personal.
 *
 */
#include "BFL_DebugPin.h"
#include "F28x_Project.h"

void BFL_DebugPin_Init()
{
#if defined(CPU1)
    BFL_DebugPin_Reset(DEBUG_PIN_1);
    BFL_DebugPin_Reset(DEBUG_PIN_2);
    EALLOW;
    //  General purpose I/O
    GpioCtrlRegs.GPBMUX1.bit.GPIO33 = 0x00;
    // Configures the GPIO pin as an output
    GpioCtrlRegs.GPBDIR.bit.GPIO33 = 1;
    // Enable the internal pullup on the specified pin.
    GpioCtrlRegs.GPBPUD.bit.GPIO33 = 0;

    GpioCtrlRegs.GPDMUX2.bit.GPIO124 = 0x00;
    GpioCtrlRegs.GPDDIR.bit.GPIO124 = 1;
    GpioCtrlRegs.GPDPUD.bit.GPIO124 = 0;
    EDIS;

    GPIO_SetupPinMux(124, GPIO_MUX_CPU2, 0);
#endif // CPU1
}

void BFL_DebugPin_Set(BFL_DebugPin_t pin)
{
    switch (pin)
    {
    case DEBUG_PIN_1:
        GpioDataRegs.GPBSET.bit.GPIO33 = 1;
        break;
    case DEBUG_PIN_2:
        GpioDataRegs.GPDSET.bit.GPIO124 = 1;
        break;
    default:
        break;
    }
}

void BFL_DebugPin_Reset(BFL_DebugPin_t pin)
{
    switch (pin)
    {
    case DEBUG_PIN_1:
        GpioDataRegs.GPBCLEAR.bit.GPIO33 = 1;
        break;
    case DEBUG_PIN_2:
        GpioDataRegs.GPDCLEAR.bit.GPIO124 = 1;
        break;
    default:
        break;
    }
}

void BFL_DebugPin_Toggle(BFL_DebugPin_t pin)
{
    switch (pin)
    {
    case DEBUG_PIN_1:
        GpioDataRegs.GPBTOGGLE.bit.GPIO33 = 1;
        break;
    case DEBUG_PIN_2:
        GpioDataRegs.GPDTOGGLE.bit.GPIO124 = 1;
        break;
    default:
        break;
    }
}
