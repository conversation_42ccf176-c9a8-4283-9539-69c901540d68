/**
 * @file mtime.h
 * <AUTHOR> (liu<PERSON><PERSON>@outlook.com)
 * @brief
 * @version 0.1
 * @date 2023-09-14
 * @last modified 2023-09-14
 *
 * @copyright Copyright (c) 2023 Liu <PERSON> Personal.
 *
 */
#ifndef MTIME_H
#define MTIME_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

#define UTC_BASE_YEAR 1970
#define MONTH_PER_YEAR 12
#define DAY_PER_YEAR 365
#define SEC_PER_DAY 86400
#define SEC_PER_HOUR 3600
#define SEC_PER_MIN 60

#define SECOND_TO_MS(x) ((x) * 1000ull)
#define MINUTE_TO_MS(x) ((x) * 60ull * 1000ull)
#define HOUR_TO_MS(x) ((x) * 60ull * 60ull * 1000ull)
#define DAY_TO_MS(x) ((x) * 24ull * 60ull * 60ull * 1000ull)

#define SECOND_TO_US(x) ((x) * 1000000ull)
#define MINUTE_TO_US(x) ((x) * 60ull * 1000000ull)
#define HOUR_TO_US(x) ((x) * 60ull * 60ull * 1000000ull)
#define MS_TO_US(x) ((x) * 1000ull)

#define MINUTE_TO_SEC(x) ((x) * 60ull)
#define SEC(x) ((x) * 1ull)
#define MS(x) ((x) * 1ull)
#define MINUTE(x) ((x) * 1ull)

// UINT32 4294967295 -> 4294967295MS -> 49.71天 -> 1193.04小时
// UINT32 4294967295 -> 4294967295US -> 1.19304小时
#ifdef __cplusplus
}
#endif
#endif //! MTIME_H
