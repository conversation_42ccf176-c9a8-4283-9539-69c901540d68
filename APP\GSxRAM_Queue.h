/**
 * @file GSxRAM_Queue.h
 * <AUTHOR> (liu<PERSON><EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-08-24
 * @last modified 2025-08-24
 *
 * @copyright Copyright (c) 2025 Liu <PERSON> Personal.
 *
 */
#ifndef GSXRAM_QUEUE_H
#define GSXRAM_QUEUE_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>

#define APP_NET_SEND_BUF_SIZE (1024 * 7)
#define APP_NET_2_1_BUF_SIZE (256)
#define APP_NET_1_2_CMD_BUF_SIZE (16)

    void GSxRam_Queue_Enqueue_1_2_FD1(uint16_t data);
    uint16_t GSxRam_Queue_Dequeue_1_2_FD1();
    uint16_t GSxRam_Queue_Size_1_2_FD1();
    uint16_t GSxRam_Queue_FreeSize_1_2_FD1();

    void GSxRam_Queue_Enqueue_2_1(uint16_t fd, uint16_t data);
    uint16_t GSxRam_Queue_Dequeue_2_1(uint16_t fd);
    uint16_t GSxRam_Queue_Size_2_1(uint16_t fd);
    uint16_t GSxRam_Queue_FreeSize_2_1(uint16_t fd);
#ifdef __cplusplus
}
#endif
#endif //! GSXRAM_QUEUE_H
