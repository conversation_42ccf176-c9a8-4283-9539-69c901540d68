//=======================================================
//    Project：         MMC_PET_CPU1
//    File:                 MMC_PET_Init.h
//    Created on:     2021
//    By：                GCY
//    Describe：
//=======================================================

#ifndef USER_INCLUDE_MOTORDRIVE_INIT_H_
#define USER_INCLUDE_MOTORDRIVE_INIT_H_

#ifdef __cplusplus
extern "C" {
#endif

//=============================================================================
// Includes and Defines
//=============================================================================
//  Includes
#include "F28x_Project.h"

// Defines

//=============================================================================
//  Typdefs and Externs
//=============================================================================

//  Typdefs

//  Global Variables


inline void MY_SysCtl_resetDevice(void)//
{

    // Write an incorrect check value to the watchdog control register
    // This will cause a device reset
    EALLOW;
    // Enable the watchdog
    WdRegs.WDCR.all = 0x0028;//SYSCTL_WD_CHKBITS

    // Write a bad check value
    WdRegs.WDCR.all = 0x00;//
    EDIS;
    //The device should have reset, so this should never be reached. Just in
    //case, loop forever.
    while(1)
    {

    }

}
//=============================================================================
//  Function Prototypes
//=============================================================================
void InitPeripheral(void);
//void Init_emif1_async_cs3(void);
void Init_scia_fifo(void);
void Init_scib_fifo(void);
void Init_scic_fifo(void);
void Init_scid_fifo(void);
void Init_spia_fifo(void);
void Init_spib_fifo(void);
void Init_spic_fifo(void);
void Init_ADC(void);
void Init_epwm1(void);
void Init_epwm2(void);
void Init_epwm3(void);
void Init_epwm4(void);
void Init_epwm7(void);
void InitMyGpio(void);
//void MMC_Ctrl_PI_init(void);
void INIT_TIMER0(void);//
void INIT_TIMER1(void);//继电器定时

#ifdef __cplusplus
}
#endif /* extern "C" */

#endif  // end of MMC_PET_INIT_H_

//=======================================================
//                                                          End of file.
//=======================================================

