// The user must define CLA_C in the project linker settings if using the
// CLA C compiler
// Project Properties -> C2000 Linker -> Advanced Options -> Command File
// Preprocessing -> --define
#ifdef CLA_C
// Define a size for the CLA scratchpad area that will be used
// by the CLA compiler for local symbols and temps
// Also force references to the special symbols that mark the
// scratchpad are.
CLA_SCRATCHPAD_SIZE = 0x100;
--undef_sym=__cla_scratchpad_end
--undef_sym=__cla_scratchpad_start
#endif //CLA_C

MEMORY
{
PAGE 0 :
   /* BEGIN is used for the "boot to SARAM" bootloader mode   */
   BEGIN           	: origin = 0x084000,   length = 0x000002
   RAMM0           	: origin = 0x000123,   length = 0x0002DD
   RAMD0           	: origin = 0x00B000,   length = 0x000800
   RAMLS0          	: origin = 0x008000,   length = 0x000800
   RAMLS1          	: origin = 0x008800,   length = 0x000800   
   /* RAMLS4      	   : origin = 0x00A000, length = 0x000800 */
   /* RAMLS5           : origin = 0x00A800, length = 0x000800 */
   RAMLS4_5         : origin = 0x00A000,   length = 0x001000
   
   RAMGS12_14          : origin = 0x018000, length = 0x003000     /* Only Available on F28379D, F28377D, F28375D devices. Remove line on other devices. */
   //RAMGS12          : origin = 0x018000, length = 0x001000     /* Only Available on F28379D, F28377D, F28375D devices. Remove line on other devices. */
   //RAMGS13          : origin = 0x019000, length = 0x001000   /* Only Available on F28379D, F28377D, F28375D devices. Remove line on other devices. */
   //RAMGS14          : origin = 0x01A000, length = 0x001000   /* Only Available on F28379D, F28377D, F28375D devices. Remove line on other devices. */

   RAMGS15          : origin = 0x01B000, length = 0x000FF8     /* Only Available on F28379D, F28377D, F28375D devices. Remove line on other devices. */
   RAMGS15_RSVD     : origin = 0x01BFF8, length = 0x000008    /* Reserve and do not use for code as per the errata advisory "Memory: Prefetching Beyond Valid Memory" */

   RESET           	: origin = 0x3FFFC0,   length = 0x000002

   /* Flash sectors */
   FLASHC         : origin = 0x084004,   length = 0x001FFC	/* on-chip Flash */
   FLASH_PARAM    : origin = 0x086000,   length = 0x002000	/* on-chip Flash */
   FLASH_BACKUP   : origin = 0x088000,   length = 0x002000	/* on-chip Flash */
   FLASH_APP      : origin = 0x090000, length = 0x018000	/* on-chip Flash */
   // FLASHG           : origin = 0x098000, length = 0x008000	/* on-chip Flash */
   // FLASHH           : origin = 0x0A0000, length = 0x008000	/* on-chip Flash */
   // FLASHI           : origin = 0x0A8000,   length = 0x008000	/* on-chip Flash */
   FLASHJ           : origin = 0x0B0000,   length = 0x008000	/* on-chip Flash */
   FLASHK           : origin = 0x0B8000,   length = 0x002000	/* on-chip Flash */
   FLASHL           : origin = 0x0BA000,   length = 0x002000	/* on-chip Flash */
   FLASHM           : origin = 0x0BC000,   length = 0x002000	/* on-chip Flash */
   FLASHN           : origin = 0x0BE000,   length = 0x001FF0	/* on-chip Flash */

   IQTABLES    		: origin = 0x0A8000, length = 0x000B50    /* IQ Math Tables in part of FLASHI */
   IQTABLES2   		: origin = 0x0A8B50, length = 0x00008C    /* IQ Math Tables in part of FLASHI */
   IQTABLES3   		: origin = 0x0A8BDC, length = 0x0000AA	  /* IQ Math Tables in part of FLASHI */
   IQMATH      		: origin = 0x0A8C86, length = 0x000afa	  /* IQ Math functions in part of FLASHI */
   FPUTABLES 		: origin = 0x0A9780, length = 0x0006A0

//   FLASHN_RSVD     : origin = 0x0BFFF0, length = 0x000010    /* Reserve and do not use for code as per the errata advisory "Memory: Prefetching Beyond Valid Memory" */


PAGE 1 :

   BOOT_RSVD       : origin = 0x000002, length = 0x000121     /* Part of M0, BOOT rom will use this for stack */
   RAMM1           : origin = 0x000400, length = 0x0003F8     /* on-chip RAM block M1 */
//   RAMM1_RSVD      : origin = 0x0007F8, length = 0x000008     /* Reserve and do not use for code as per the errata advisory "Memory: Prefetching Beyond Valid Memory" */

   RAMLS2_3      		: origin = 0x009000,   length = 0x001000
   //RAMLS3      		: origin = 0x009800,   length = 0x000800

   RAMGS0           : origin = 0x00C000,   length = 0x001000
   RAMGS1           : origin = 0x00D000,   length = 0x001000
   RAMGS2           : origin = 0x00E000,   length = 0x001000
   RAMGS3           : origin = 0x00F000,   length = 0x001000
   RAMGS4           : origin = 0x010000,   length = 0x001000
   RAMGS5           : origin = 0x011000,   length = 0x001000
   RAMGS6           : origin = 0x012000,   length = 0x001000
   RAMGS7           : origin = 0x013000,   length = 0x001000
	// The RAMGS8-10 is used for the App Net application.
   // RAMGS8      : origin = 0x014000, length = 0x001000
   // RAMGS9      : origin = 0x015000, length = 0x001000
   // RAMGS10     : origin = 0x016000, length = 0x001000
   RAMGS8      : origin = 0x014000, length = 0x001000
   // CPU2
   RAMGS9_DMA_CH4_SPIA_RX      : origin = 0x015000, length = 0x00800
   // CPU2
   RAMGS9_CPU2_TO_CPU1_INFO      : origin = 0x015800, length = 0x00800
   // CPU1
   RAMGS10_CPU1_TO_CPU2_INFO     : origin = 0x016000, length = 0x000400
	// CPU1
   RAMGS10_APP_NET_SEND_BUF     : origin = 0x016400, length = 0x001C00

//   RAMGS11_RSVD     : origin = 0x017FF8, length = 0x000008    /* Reserve and do not use for code as per the errata advisory "Memory: Prefetching Beyond Valid Memory" */

   // RAMGS11          : origin = 0x017000, length = 0x001000     /* Only Available on F28379D, F28377D, F28375D devices. Remove line on other devices. */
   // RAMGS12          : origin = 0x018000, length = 0x001000     /* Only Available on F28379D, F28377D, F28375D devices. Remove line on other devices. */
   // RAMGS13          : origin = 0x019000,   length = 0x001000   /* Only Available on F28379D, F28377D, F28375D devices. Remove line on other devices. */

   CPU2TOCPU1RAM   : origin = 0x03F800, length = 0x000400
   CPU1TOCPU2RAM   : origin = 0x03FC00, length = 0x000400

   EMIF1_CS0n       : origin = 0x80000000, length = 0x10000000
   EMIF1_CS2n       : origin = 0x00100000, length = 0x00200000
   EMIF1_CS3n       : origin = 0x00300000, length = 0x00080000
   EMIF1_CS4n       : origin = 0x00380000, length = 0x00060000
   EMIF2_CS0n       : origin = 0x90000000, length = 0x10000000
   EMIF2_CS2n       : origin = 0x00002000, length = 0x00001000

   CLA1_MSGRAMLOW   : origin = 0x001480,   length = 0x000080
   CLA1_MSGRAMHIGH  : origin = 0x001500,   length = 0x000080
}


SECTIONS
{
   /* Allocate program areas: */
   .cinit           : > FLASHC         PAGE = 0, ALIGN(8)
   .text            : > FLASH_APP      PAGE = 0, ALIGN(8)
   codestart        : > BEGIN          PAGE = 0, ALIGN(8)
   .stack           : > RAMM1          PAGE = 1
   
   .user_var_store			      : > FLASH_PARAM,	 	PAGE = 0, ALIGN(4)//, TYPE=NOLOAD		//
   .user_var_store_backup			: > FLASH_BACKUP,	 	PAGE = 0, ALIGN(4)//, TYPE=NOLOAD		//
   /* Allocate uninitalized data sections: */

   #if defined(__TI_EABI__)
      .switch          : LOAD = FLASHC,
                           RUN = RAMLS0,
                           LOAD_START(switch_loadstart),
                           LOAD_SIZE(switch_loadsize),
                           RUN_START(switch_runstart),
                           PAGE = 0

      .init_array         : > FLASHC,       PAGE = 0, ALIGN(8)
      .bss                : > RAMLS1,       PAGE = 0
      .bss:output         : > RAMLS1,       PAGE = 0
      .data               : > RAMLS2_3,       PAGE = 1
      .sysmem             : > RAMGS1,       PAGE = 1
      .const              : >> FLASHC,       PAGE = 0, ALIGN(8)
   #else
      .switch          : LOAD = FLASHC,
                        RUN = RAMLS0,
                        LOAD_START(_switch_loadstart),
                        LOAD_SIZE(_switch_loadsize),
                        RUN_START(_switch_runstart),
                        PAGE = 0
      .pinit              : > FLASHC,       PAGE = 0, ALIGN(8)
      .ebss               : > RAMGS1,		   PAGE = 1
      .esysmem            : > RAMGS1,        PAGE = 1
      .econst             : > FLASHC   	 PAGE = 0, ALIGN(8)
   #endif

   .reset           : > RESET,     PAGE = 0, TYPE = DSECT /* not used, */
//--added by GCY
   IQmath           : > IQMATH,     PAGE = 0
   IQmathTables     : > IQTABLES,   PAGE = 0
   FPUmathTables	: > FPUTABLES, 	PAGE =0
//--added by GCY

//   Filter_RegsFile  : > RAMGS0,	   PAGE = 1
   DMA_RAM_BUF        : >> RAMGS0,     PAGE = 1

   ramgs_dmach5         : > RAMGS8,    PAGE = 1
   ramgs_dmach4         : > RAMGS9_DMA_CH4_SPIA_RX,    PAGE = 1
   Cpu2ToCpu1InfoFile   : > RAMGS9_CPU2_TO_CPU1_INFO,    PAGE = 1
   Cpu1ToCpu2InfoFile   : > RAMGS10_CPU1_TO_CPU2_INFO,    PAGE = 1
   AppNetSendBuf1File   : > RAMGS10_APP_NET_SEND_BUF,    PAGE = 1
   ramcpu1tocpu2    : > CPU1TOCPU2RAM,    PAGE = 1
   ramcpu2tocpu1    : > CPU2TOCPU1RAM,    PAGE = 1
   RegsFlashSaveFile : > FLASHN,    PAGE = 0
   CPU1_To_CPU2		: > CPU1TOCPU2RAM,	PAGE = 1
   CPU2_To_CPU1		: > CPU2TOCPU1RAM,	PAGE = 1

    /* CLA specific sections */
   #if defined(__TI_EABI__)
   		Cla1Prog    : LOAD = FLASH_APP,
                      RUN = RAMLS4_5,
                      LOAD_START(Cla1funcsLoadStart),
                      LOAD_END(Cla1funcsLoadEnd),
                      RUN_START(Cla1funcsRunStart),
                      LOAD_SIZE(Cla1funcsLoadSize),
                      PAGE = 0, ALIGN(8)
   #else
      	Cla1Prog    : LOAD = FLASH_APP,
                      RUN = RAMLS4_5,
                      LOAD_START(_Cla1funcsLoadStart),
                      LOAD_END(_Cla1funcsLoadEnd),
                      RUN_START(_Cla1funcsRunStart),
                      LOAD_SIZE(_Cla1funcsLoadSize),
                      PAGE = 0, ALIGN(8)
   #endif

   CLADataLS0		: > RAMLS0, PAGE=0
   CLADataLS1		: > RAMLS1, PAGE=0

   Cla1ToCpuMsgRAM  : > CLA1_MSGRAMLOW,   PAGE = 1
   CpuToCla1MsgRAM  : > CLA1_MSGRAMHIGH,  PAGE = 1


#ifdef __TI_COMPILER_VERSION__
   #if __TI_COMPILER_VERSION__ >= 15009000
        #if defined(__TI_EABI__)
		    .TI.ramfunc : {} LOAD = FLASH_APP,
							 RUN = RAMGS12_14,
                	         LOAD_START(RamfuncsLoadStart),
                 	         LOAD_SIZE(RamfuncsLoadSize),
                 	         LOAD_END(RamfuncsLoadEnd),
                  	         RUN_START(RamfuncsRunStart),
                  	         RUN_SIZE(RamfuncsRunSize),
                   	         RUN_END(RamfuncsRunEnd),
							 PAGE = 0, ALIGN(8)
		#else
			.TI.ramfunc : {} LOAD = FLASH_APP,
							 RUN = RAMGS12_14,
                	         LOAD_START(_RamfuncsLoadStart),
                 	         LOAD_SIZE(_RamfuncsLoadSize),
                 	         LOAD_END(_RamfuncsLoadEnd),
                  	         RUN_START(_RamfuncsRunStart),
                  	         RUN_SIZE(_RamfuncsRunSize),
                   	         RUN_END(_RamfuncsRunEnd),
							 PAGE = 0, ALIGN(8)
		#endif
   #else
   ramfuncs         : LOAD = FLASH_APP,
                      RUN = RAMGS12_14,
                      LOAD_START(_RamfuncsLoadStart),
                      LOAD_SIZE(_RamfuncsLoadSize),
                      LOAD_END(_RamfuncsLoadEnd),
                      RUN_START(_RamfuncsRunStart),
                      RUN_SIZE(_RamfuncsRunSize),
                      RUN_END(_RamfuncsRunEnd),
                      PAGE = 0, ALIGN(8)
   #endif
#endif

   /* The following section definitions are required when using the IPC API Drivers */
    GROUP : > CPU1TOCPU2RAM, PAGE = 1
    {
        PUTBUFFER
        PUTWRITEIDX
        GETREADIDX
    }

    GROUP : > CPU2TOCPU1RAM, PAGE = 1
    {
        GETBUFFER :    TYPE = DSECT
        GETWRITEIDX :  TYPE = DSECT
        PUTREADIDX :   TYPE = DSECT
    }



   /* The following section definition are for SDFM examples */
   // Filter1_RegsFile : > RAMGS1,	PAGE = 1, fill=0x1111
   // Filter2_RegsFile : > RAMGS2,	PAGE = 1, fill=0x2222
   // Filter3_RegsFile : > RAMGS3,	PAGE = 1, fill=0x3333
   // Filter4_RegsFile : > RAMGS4,	PAGE = 1, fill=0x4444

#ifdef CLA_C
   /* CLA C compiler sections */
   //
   // Must be allocated to memory the CLA has write access to
   //
   CLAscratch       :
                     { *.obj(CLAscratch)
                     . += CLA_SCRATCHPAD_SIZE;
                     *.obj(CLAscratch_end) } >  RAMLS1,  PAGE = 0

   .scratchpad      : > RAMLS1,       PAGE = 0
   .bss_cla		    : > RAMLS1,       PAGE = 0
   .const_cla	    :  LOAD = FLASH_APP,
                       RUN = RAMLS1,
                       RUN_START(_Cla1ConstRunStart),
                       LOAD_START(_Cla1ConstLoadStart),
                       LOAD_SIZE(_Cla1ConstLoadSize),
                       PAGE = 0
#endif //CLA_C
}

/*
//===========================================================================
// End of file.
//===========================================================================
*/
